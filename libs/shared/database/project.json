{"name": "database", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/shared/database/src", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/shared/database", "tsConfig": "libs/shared/database/tsconfig.lib.json", "packageJson": "libs/shared/database/package.json", "main": "libs/shared/database/src/index.ts", "assets": ["libs/shared/database/*.md"]}}}}