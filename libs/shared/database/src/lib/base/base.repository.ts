import { Model, ModelCtor } from 'sequelize-typescript';
import { FindOptions, CreateOptions, UpdateOptions, DestroyOptions, WhereOptions } from 'sequelize';

export class BaseRepository<T extends Model> {
  constructor(private readonly model: ModelCtor<T>) {}

  async findAll(options?: FindOptions): Promise<T[]> {
    return this.model.findAll(options);
  }

  async findById(id: number | string, options?: FindOptions): Promise<T | null> {
    return this.model.findByPk(id, options);
  }

  async create(data: Partial<T>, options?: CreateOptions): Promise<T> {
    return this.model.create(data as any, options);
  }

  async update(
    where: WhereOptions<T>, 
    data: Partial<T>, 
    options?: UpdateOptions
  ): Promise<[affectedCount: number, affectedRows: T[]]> {
    const [affectedCount] = await this.model.update(data, {
      where,
      ...options,
    });

    let affectedRows: T[] = [];
    if (options?.returning) {
      affectedRows = await this.model.findAll({ where });
    }

    return [affectedCount, affectedRows];
  }

  async updateById(
    id: number | string, 
    data: Partial<T>, 
    options?: UpdateOptions
  ): Promise<[affectedCount: number, affectedRows: T[]]> {
    return this.update({ id } as unknown as WhereOptions<T>, data, options);
  }

  async delete(id: number | string, options?: DestroyOptions): Promise<number> {
    return this.model.destroy({
      where: { id } as unknown as WhereOptions<T>,
      ...options,
    });
  }

  async findOne(options?: FindOptions): Promise<T | null> {
    return this.model.findOne(options);
  }

  async findAndCountAll(options?: FindOptions): Promise<{ rows: T[]; count: number }> {
    return this.model.findAndCountAll(options);
  }
}
