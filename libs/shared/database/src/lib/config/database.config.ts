import { Dialect } from 'sequelize';

export interface DatabaseConfig {
  host: string;
  port: number;
  username: string;
  password: string;
  database: string;
  dialect: Dialect;
  logging: boolean;
}

export const getDatabaseConfig = (serviceName: string): DatabaseConfig => {
  return {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432', 10), // Changed default from 5432 to 5433
    username: process.env.DB_USER || `${serviceName}_user`,
    password: process.env.DB_PASSWORD || `${serviceName}_pass`,
    database: process.env.DB_NAME || `${serviceName}_db`,
    dialect: 'postgres',
    logging: process.env.NODE_ENV !== 'production',
  };
};
