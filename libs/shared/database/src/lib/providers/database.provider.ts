import { Sequelize } from 'sequelize-typescript';
import { DatabaseConfig } from '../config/database.config';

export const createSequelizeProvider = (
  config: DatabaseConfig,
  models: any[]
) => ({
  provide: 'SEQUELIZE',
  useFactory: async () => {
    const sequelize = new Sequelize({
      ...config,
      models,
      pool: {
        max: 10,
        min: 0,
        acquire: 30000,
        idle: 10000,
      },
    });

    await sequelize.authenticate();
    console.log('Database connection established successfully.');

    return sequelize;
  },
});