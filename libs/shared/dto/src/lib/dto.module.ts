import { Module } from '@nestjs/common';
import { UniversityDtoModule } from './university/university.dto.module';

// Re-export all DTOs from all domain modules
export * from './university/university.dto.module';
// export * from './agency/agency.dto.module';
// export * from './teachers/teachers.dto.module';

@Module({
  imports: [UniversityDtoModule.register()],
  exports: [UniversityDtoModule]
})
export class DtoModule {}
