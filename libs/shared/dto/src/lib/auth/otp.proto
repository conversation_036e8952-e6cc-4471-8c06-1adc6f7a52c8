syntax = "proto3";
package auth;

message GenerateOtpRequest {
  string email = 1;
  string type = 2;
  string ipAddress = 3;
  string userAgent = 4;
}
message GenerateOtpResponse {
  bool success = 1;
  string message = 2;
  string expiresAt = 3;
}

message VerifyOtpRequest {
  string email = 1;
  string otp = 2;
  string type = 3;
  string ipAddress = 4;
  string userAgent = 5;
}
message VerifyOtpResponse {
  bool success = 1;
  string message = 2;
}
