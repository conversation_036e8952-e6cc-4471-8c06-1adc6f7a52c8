syntax = "proto3";
package auth;
import "common.proto";

// Employee Management Messages
message CreateEmployeeRequest {
  // User creation fields
  string email = 1;
  string password = 2;
  string name = 3;
  string phone = 4;
  string departmentName = 5;
  string organizationName = 6;
  string employeeRoleName = 7;

  // Employee specific fields
  int64 userId = 8;
  string lastName = 9;
  string firstName = 10;
  string joiningDate = 11;
  string jobType = 12;
  string jobStatus = 13;
  string dateOfBirth = 14;
  string bloodGroup = 15;
  string gender = 16;
  int64 agencyId = 17;
  string orgId = 18;
  repeated EmployeeAddressInfo addresses = 19;
  repeated EmployeeEmergencyContactInfo emergencyContacts = 20;
  repeated EmployeeIdentityDocInfo identityDocs = 21;
  repeated EmployeeBankAccountInfo bankAccounts = 22;

  // Audit fields
  int64 requestUserId = 23;
  string roleName = 24;
  string ipAddress = 25;
  string userAgent = 26;
}

message UpdateEmployeeRequest {
  int64 id = 1;
  string lastName = 2;
  string firstName = 3;
  string joiningDate = 4;
  string jobType = 5;
  string jobStatus = 6;
  string dateOfBirth = 7;
  string bloodGroup = 8;
  string gender = 9;
  int64 agencyId = 10;
  string orgId = 11;
  repeated EmployeeAddressInfo addresses = 12;
  repeated EmployeeEmergencyContactInfo emergencyContacts = 13;
  repeated EmployeeIdentityDocInfo identityDocs = 14;
  repeated EmployeeBankAccountInfo bankAccounts = 15;
  int64 requestUserId = 16;
  string roleName = 17;
  string ipAddress = 18;
  string userAgent = 19;
}

message GetEmployeeRequest {
  int64 id = 1;
  int64 requestUserId = 2;
  string roleName = 3;
  string ipAddress = 4;
  string userAgent = 5;
}

message ListEmployeesRequest {
  int32 page = 1;
  int32 limit = 2;
  string search = 3;
  string jobType = 4;
  string jobStatus = 5;
  int64 agencyId = 6;
  string orgId = 7;
  int64 requestUserId = 8;
  string roleName = 9;
  string ipAddress = 10;
  string userAgent = 11;
}

message RemoveEmployeeRequest {
  int64 id = 1;
  int64 requestUserId = 2;
  string roleName = 3;
  string ipAddress = 4;
  string userAgent = 5;
}

message EmployeeResponse {
  bool success = 1;
  string message = 2;
  EmployeeInfo employee = 3;
}

message ListEmployeesResponse {
  bool success = 1;
  string message = 2;
  repeated EmployeeInfo employees = 3;
  int32 total = 4;
  int32 page = 5;
  int32 limit = 6;
}

message RemoveEmployeeResponse {
  bool success = 1;
  string message = 2;
}

message EmployeeInfo {
  int64 userId = 1;
  string lastName = 2;
  string firstName = 3;
  string joiningDate = 4;
  string jobType = 5;
  string jobStatus = 6;
  string dateOfBirth = 7;
  string bloodGroup = 8;
  string gender = 9;
  int64 agencyId = 10;
  string orgId = 11;
  repeated EmployeeAddressInfo addresses = 12;
  repeated EmployeeEmergencyContactInfo emergencyContacts = 13;
  repeated EmployeeIdentityDocInfo identityDocs = 14;
  repeated EmployeeBankAccountInfo bankAccounts = 15;
  string createdAt = 16;
  string updatedAt = 17;
}

message EmployeeAddressInfo {
  int64 id = 1;
  int64 userId = 2;
  string addressType = 3;
  string addressLine = 4;
  string country = 5;
  string state = 6;
  string city = 7;
  string postalCode = 8;
}

message EmployeeEmergencyContactInfo {
  int64 id = 1;
  int64 userId = 2;
  string category = 3;
  string name = 4;
  string relationship = 5;
  string address = 6;
  string phoneNumber = 7;
  string email = 8;
}

message EmployeeIdentityDocInfo {
  int64 id = 1;
  int64 userId = 2;
  string docType = 3;
  string nationality = 4;
  string issueDate = 5;
  string expiryDate = 6;
}

message EmployeeBankAccountInfo {
  int64 id = 1;
  int64 userId = 2;
  string accountHolder = 3;
  string accountNumber = 4;
  string bankName = 5;
  string branchName = 6;
}
