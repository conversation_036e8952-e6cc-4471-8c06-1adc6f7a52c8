syntax = "proto3";
package auth;
import "common.proto";

message LoginRequest {
  string email = 1;
  string password = 2;
  string ipAddress = 3;
  string userAgent = 4;
}
message LoginResponse {
  bool success = 1;
  string message = 2;
  string accessToken = 3;
  string refreshToken = 4;
  UserInfo user = 5;
}

message RegisterRequest {
  string name = 1;
  string nationality = 2;
  string organizationName = 3;
  string email = 4;
  string password = 5;
  string phone = 6;
  string roleName = 7;
  string departmentName = 8;
  string ipAddress = 9;
  string userAgent = 10;
}
message RegisterResponse {
  bool success = 1;
  string message = 2;
}

message LogoutRequest {
  string accessToken = 1;
}
message LogoutResponse {
  bool success = 1;
  string message = 2;
}

message ValidateTokenRequest {
  string token = 1;
}
message ValidateTokenResponse {
  bool valid = 1;
  string message = 2;
  UserInfo user = 3;
}

message RefreshTokenRequest {
  string refreshToken = 1;
}
message RefreshTokenResponse {
  bool success = 1;
  string message = 2;
  string accessToken = 3;
  string refreshToken = 4;
}

message SsoAuthRequest {
  string provider = 1;
  string token = 2;
  string email = 3;
  string name = 4;
  string ipAddress = 5;
  string userAgent = 6;
}
message SsoAuthResponse {
  bool success = 1;
  string message = 2;
  string accessToken = 3;
  string refreshToken = 4;
  UserInfo user = 5;
}

// Password Reset Messages
message ForgotPasswordRequest {
  string email = 1;
  string ipAddress = 2;
  string userAgent = 3;
}

message ForgotPasswordResponse {
  bool success = 1;
  string message = 2;
  string resetToken = 3;  // Only for development
}

message VerifyResetTokenRequest {
  string email = 1;
  string resetToken = 2;
}

message VerifyResetTokenResponse {
  bool valid = 1;
  string message = 2;
  string expiresAt = 3;
}

message ResetPasswordRequest {
  string email = 1;
  string resetToken = 2;
  string newPassword = 3;
  string confirmPassword = 4;
  string ipAddress = 5;
  string userAgent = 6;
}

message ResetPasswordResponse {
  bool success = 1;
  string message = 2;
}

// basic user type (without full nested roles/depts)
message UserInfo {
  int64 id = 1;
  string name = 2;
  string email = 3;
  string phone = 4;
  string nationality = 7;
  string ppUrl = 8;
  string status = 18;
  repeated RoleBasic roles = 19;
  repeated DepartmentInfo departments = 20;
  repeated SocialSiteInfo socialSites = 21;
}
