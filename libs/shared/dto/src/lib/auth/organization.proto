syntax = "proto3";
package auth;
import "common.proto";

// Organization Management Messages
message CreateOrganizationRequest {
  string name = 1;
  string type = 2;
  string imageUrl = 3;
  string description = 4;
  string website = 5;
  string address = 6;
  string country = 7;
  string state = 8;
  string city = 9;
  string postalCode = 10;
  string phone = 11;
  string email = 12;
  int64 userId = 13;
  string roleName = 14;
  string ipAddress = 15;
  string userAgent = 16;
}

message CreateOrganizationResponse {
  bool success = 1;
  string message = 2;
  OrganizationInfo organization = 3;
}

message GetOrganizationRequest {
  int64 id = 1;
  int64 userId = 2;
  string roleName = 3;
  string ipAddress = 4;
  string userAgent = 5;
}

message GetOrganizationResponse {
  bool success = 1;
  string message = 2;
  OrganizationInfo organization = 3;
}

message UpdateOrganizationRequest {
  int64 id = 1;
  string name = 2;
  string type = 3;
  string imageUrl = 4;
  string description = 5;
  string website = 6;
  string address = 7;
  string country = 8;
  string state = 9;
  string city = 10;
  string postalCode = 11;
  string phone = 12;
  string email = 13;
  int64 userId = 14;
  string roleName = 15;
  string ipAddress = 16;
  string userAgent = 17;
}

message UpdateOrganizationResponse {
  bool success = 1;
  string message = 2;
  OrganizationInfo organization = 3;
}

message DeleteOrganizationRequest {
  int64 id = 1;
  int64 userId = 2;
  string roleName = 3;
  string ipAddress = 4;
  string userAgent = 5;
}

message DeleteOrganizationResponse {
  bool success = 1;
  string message = 2;
}

message ListOrganizationsRequest {
  int32 page = 1;
  int32 limit = 2;
  string search = 3;
  string type = 4;
  int64 userId = 5;
  string roleName = 6;
  string ipAddress = 7;
  string userAgent = 8;
}

message ListOrganizationsResponse {
  bool success = 1;
  string message = 2;
  repeated OrganizationInfo organizations = 3;
  int32 total = 4;
  int32 page = 5;
  int32 limit = 6;
}

message OrganizationInfo {
  int64 id = 1;
  string name = 2;
  string type = 3;
  string imageUrl = 4;
  string description = 5;
  string website = 6;
  string address = 7;
  string country = 8;
  string state = 9;
  string city = 10;
  string postalCode = 11;
  string phone = 12;
  string email = 13;
  bool isActive = 14;
  string createdAt = 15;
  string updatedAt = 16;
}
