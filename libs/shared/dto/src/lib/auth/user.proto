syntax = "proto3";
package auth;
import "common.proto";

// full user with nested roles & depts
message AuthUserInfo {
  int64 id = 1;
  string name = 2;
  string email = 3;
  string phone = 4;
  string socialLink = 5;
  string gender = 6;
  string nationality = 7;
  string presentAddress = 8;
  string presentCountry = 9;
  string presentState = 10;
  string presentCity = 11;
  string presentAddressZipcode = 12;
  string permanentAddress = 13;
  string permanentCountry = 14;
  string permanentState = 15;
  string permanentCity = 16;
  string permanentAddressZipcode = 17;
  string status = 18;

  repeated AuthRoleDetailsInfo roles = 19;
  repeated AuthDepartmentWithUsersInfo departments = 20;
  repeated SocialSiteInfo socialSites = 21;
}

message AuthDepartmentWithUsersInfo {
  int64 id = 1;
  string name = 2;
  optional int64 parent_id = 3;
  repeated AuthDepartmentUserInfo users = 4;
  repeated AuthDepartmentWithUsersInfo children = 5;
}

message AuthDepartmentUserInfo {
  int64 id = 1;
  string name = 2;
  string email = 3;
}

message AuthRoleDetailsInfo {
  int64 id = 1;
  string role = 2;
  repeated AuthRoleDepartmentInfo department = 3;
  repeated AuthRoleModuleInfo modules = 4;
}

message AuthRoleDepartmentInfo {
  int64 id = 1;
  string name = 2;
  repeated AuthRoleUserInfo users = 3;
}

message AuthRoleUserInfo {
  int64 id = 1;
  string name = 2;
  string email = 3;
}

message AuthRoleModuleInfo {
  int64 id = 1;
  string module = 2;
  repeated AuthRoleFeaturePermission features = 3;
}

message AuthRoleFeaturePermission {
  int64 id = 1;
  string feature = 2;
  bool permissions = 3;
  repeated AuthRoleSubFeaturePermission subFeatures = 4;
}

message AuthRoleSubFeaturePermission {
  int64 id = 1;
  string subFeature = 2;
  bool permissions = 3;
}
