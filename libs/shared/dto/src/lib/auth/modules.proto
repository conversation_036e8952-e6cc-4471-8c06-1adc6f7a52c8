syntax = "proto3";
package auth;
import "common.proto";

message CreateModuleRequest {
  string name = 1;
  repeated FeatureInput features = 2;
}
message CreateModuleResponse {
  bool success = 1;
  string message = 2;
  ModuleInfo module = 3;
}

message BulkCreateModulesRequest {
  repeated ModuleInput modules = 1;
  int64 userId = 2;
  string roleName = 3;
  string ipAddress = 4;
  string userAgent = 5;
}
message BulkCreateModulesResponse {
  bool success = 1;
  string message = 2;
  repeated ModuleInfo modules = 3;
}

message ListModulesResponse {
  bool success = 1;
  string message = 2;
  repeated ModuleInfo modules = 3;
}

message FeatureInput {
  string name = 1;
  repeated SubFeatureInput subFeatures = 2;
}
message SubFeatureInput {
  string name = 1;
}
message ModuleInput {
  string name = 1;
  repeated FeatureInput features = 2;
}
