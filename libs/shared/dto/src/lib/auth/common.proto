syntax = "proto3";
package auth;

// metadata passed to every call
message GetDataRequest {
  int64 userId = 1;
  string roleName = 2;
  string ipAddress = 3;
  string userAgent = 4;
}

// small reusable types
message RoleBasic {
  int64 id = 1;
  string name = 2;
  repeated FeatureInfo features = 3;
  repeated SubFeatureInfo subFeatures = 4;
}
message SocialSiteInfo {
  int64 id = 1;
  string domain = 2;
  string url = 3;
}
message DepartmentInfo {
  int64 id = 1;
  string name = 2;
  int64 parentId = 3;
  repeated DepartmentInfo children = 4;
}
message ModuleInfo {
  int64 id = 1;
  string name = 2;
  repeated FeatureInfo features = 3;
}
message FeatureInfo {
  int64 id = 1;
  string name = 2;
  int64 moduleId = 3;
  bool permission = 4;
  repeated SubFeatureInfo subFeatures = 5;
  ModuleInfo module = 6;
}
message SubFeatureInfo {
  int64 id = 1;
  string name = 2;
  int64 featureId = 3;
  bool permission = 4;
}
