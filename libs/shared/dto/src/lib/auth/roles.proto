syntax = "proto3";
package auth;
import "common.proto";

message RoleDetailsInfo {
  int64 id = 1;
  string role = 2;
  repeated RoleDepartmentInfo department = 3;
  repeated RoleModuleInfo modules = 4;
}

message RoleDepartmentInfo {
  int64 id = 1;
  string name = 2;
  repeated RoleUserInfo users = 3;
}
message RoleUserInfo {
  int64 id = 1;
  string name = 2;
  string email = 3;
}
message RoleModuleInfo {
  int64 id = 1;
  string module = 2;
  repeated RoleFeaturePermission features = 3;
}

message RoleFeaturePermission {
  int64 id = 1;
  string feature = 2;
  bool permissions = 3;
  repeated RoleSubFeaturePermission subFeatures = 4;
}
message RoleSubFeaturePermission {
  int64 id = 1;
  string subFeature = 2;
  bool permissions = 3;
}

message RolesWithDetailsResponse {
  bool success = 1;
  string message = 2;
  repeated RoleDetailsInfo roles = 3;
}
message RoleDetailsResponse {
  bool success = 1;
  string message = 2;
  RoleDetailsInfo role = 3;
}

// Create / Update / Delete RPC payloads
message CreateRoleRequest {
  string name = 1;
  string description = 2;
  int64 organizationId = 3;
  bool isSystemRole = 4;
  repeated FeaturePermission permissions = 5;
  GetDataRequest getArgs = 6;
}

message CreateRoleWithDetailsRequest {
  int64 id = 1;
  string name = 2;
  repeated CreateRoleDepartmentDetail departments = 3;
  repeated CreateRoleModuleDetail modules = 4;
  GetDataRequest getArgs = 5;
}
message CreateRoleDepartmentDetail {
  int64 id = 1;
  string name = 2;
  repeated CreateRoleUserDetail users = 3;
}
message CreateRoleUserDetail {
  int64 id = 1;
  string name = 2;
  string email = 3;
}
message CreateRoleModuleDetail {
  int64 id = 1;
  repeated CreateRoleFeaturePermission features = 2;
}
message CreateRoleFeaturePermission {
  int64 id = 1;
  string feature = 2;
  bool permissions = 3;
  repeated CreateRoleSubFeaturePermission subFeatures = 4;
}
message CreateRoleSubFeaturePermission {
  int64 id = 1;
  string subFeature = 2;
  bool permissions = 3;
}

message CreateRoleWithDetailsResponse {
  bool success = 1;
  string message = 2;
  RoleDetailsInfo role = 3;
}

message GetRoleDetailsRequest {
  string name = 1;
  GetDataRequest getArgs = 2;
}
message GetRoleDetailsResponse {
  bool success = 1;
  string message = 2;
  RoleDetailsInfo role = 3;
}

// Simple role management messages
message GetRoleRequest {
  int64 id = 1;
}

message RoleResponse {
  bool success = 1;
  string message = 2;
  RoleBasic role = 3;
}

message UpdateRoleRequest {
  int64 id = 1;
  string roleName = 2;
  GetDataRequest getArgs = 3;
}
message UpdateRoleResponse {
  bool success = 1;
  string message = 2;
}

message DeleteRoleRequest {
  int64 id = 1;
  GetDataRequest getArgs = 2;
}
message DeleteRoleResponse {
  bool success = 1;
  string message = 2;
}

message ListRolesRequest {
  int64 organizationId = 1;
  bool includeSystemRoles = 2;
  int32 page = 3;
  int32 limit = 4;
}

message ListRolesResponse {
  bool success = 1;
  string message = 2;
  repeated RoleBasic roles = 3;
  int32 total = 4;
  int32 page = 5;
  int32 limit = 6;
}

// organization roles
message GetOrganizationRolesRequest {
  int64 organizationId = 1;
  GetDataRequest getArgs = 2;
}

message GetOrganizationRolesResponse {
  bool success = 1;
  string message = 2;
  repeated RoleDetailsInfo roles = 3;
}
