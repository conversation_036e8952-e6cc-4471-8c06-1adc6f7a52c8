syntax = "proto3";

package settings;

// Settings service definition
service SettingsService {
  // Get settings by user ID
  rpc GetUserSettings (GetUserSettingsRequest) returns (UserSettings) {}
  
  // Update user settings
  rpc UpdateUserSettings (UpdateUserSettingsRequest) returns (UserSettings) {}
  
  // Get application settings
  rpc GetAppSettings (GetAppSettingsRequest) returns (AppSettings) {}
  
  // Update application settings
  rpc UpdateAppSettings (UpdateAppSettingsRequest) returns (AppSettings) {}
  
  // Get settings by category
  rpc GetSettingsByCategory (GetSettingsByCategoryRequest) returns (CategorySettings) {}
  
  // Batch update settings
  rpc BatchUpdateSettings (BatchUpdateSettingsRequest) returns (BatchUpdateSettingsResponse) {}
  
  // Watch settings changes
  rpc WatchSettings (WatchSettingsRequest) returns (stream SettingsChange) {}
}

// Request to get user settings
message GetUserSettingsRequest {
  string user_id = 1;
  optional string category = 2;
}

// Request to update user settings
message UpdateUserSettingsRequest {
  string user_id = 1;
  map<string, SettingValue> settings = 2;
}

// Request to get application settings
message GetAppSettingsRequest {
  string app_id = 1;
  optional string environment = 2;
}

// Request to update application settings
message UpdateAppSettingsRequest {
  string app_id = 1;
  string environment = 2;
  map<string, SettingValue> settings = 3;
}

// Request to get settings by category
message GetSettingsByCategoryRequest {
  string category = 1;
  optional string scope = 2;
}

// Request for batch update
message BatchUpdateSettingsRequest {
  repeated SettingUpdate updates = 1;
}

// Request to watch settings changes
message WatchSettingsRequest {
  string scope = 1;
  repeated string categories = 2;
}

// Setting value with type
message SettingValue {
  oneof value {
    string string_value = 1;
    int64 int_value = 2;
    double float_value = 3;
    bool bool_value = 4;
    JsonObject json_value = 5;
  }
  string description = 6;
  optional string last_updated_by = 7;
  optional int64 last_updated_at = 8;
}

// JSON object representation
message JsonObject {
  string data = 1;
}

// User settings response
message UserSettings {
  string user_id = 1;
  map<string, SettingValue> settings = 2;
  int64 last_updated_at = 3;
}

// Application settings response
message AppSettings {
  string app_id = 1;
  string environment = 2;
  map<string, SettingValue> settings = 3;
  int64 last_updated_at = 4;
}

// Category settings response
message CategorySettings {
  string category = 1;
  map<string, SettingValue> settings = 2;
  optional string scope = 3;
}

// Single setting update
message SettingUpdate {
  string key = 1;
  SettingValue value = 2;
  string scope = 3;
  optional string category = 4;
}

// Batch update response
message BatchUpdateSettingsResponse {
  repeated UpdateResult results = 1;
}

// Individual update result
message UpdateResult {
  string key = 1;
  bool success = 2;
  optional string error = 3;
}

// Settings change notification
message SettingsChange {
  string key = 1;
  SettingValue old_value = 2;
  SettingValue new_value = 3;
  string scope = 4;
  string category = 5;
  int64 timestamp = 6;
}

// Settings metadata
message SettingsMetadata {
  string version = 1;
  int64 last_updated_at = 2;
  string last_updated_by = 3;
  map<string, string> tags = 4;
}