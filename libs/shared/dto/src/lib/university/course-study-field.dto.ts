import { IsNumber, IsBoolean, IsOptional } from 'class-validator';

export class CourseStudyFieldDto {
  @IsNumber()
  id!: number;

  @IsNumber()
  courseId!: number;

  @IsNumber()
  studyFieldId!: number;

  @IsBoolean()
  isActive!: boolean;

  @IsOptional()
  course?: {
    id: number;
    title: string;
  };

  @IsOptional()
  studyField?: {
    id: number;
    field: string;
  };
}

export class CreateCourseStudyFieldDto {
  @IsNumber()
  courseId!: number;

  @IsNumber()
  studyFieldId!: number;

  @IsBoolean()
  @IsOptional()
  isActive?: boolean;
}

export class UpdateCourseStudyFieldDto {
  @IsBoolean()
  @IsOptional()
  isActive?: boolean;
}

export class GetCourseStudyFieldsRequestDto {
  @IsNumber()
  @IsOptional()
  courseId?: number;

  @IsNumber()
  @IsOptional()
  studyFieldId?: number;

  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @IsNumber()
  @IsOptional()
  page?: number;

  @IsNumber()
  @IsOptional()
  limit?: number;
}

export class GetCourseStudyFieldsResponseDto {
  total!: number;
  page!: number;
  limit!: number;
  courseStudyFields!: CourseStudyFieldDto[];
}