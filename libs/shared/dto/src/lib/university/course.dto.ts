import { IsString, <PERSON><PERSON><PERSON><PERSON>, IsD<PERSON>imal, IsOptional, IsBoolean } from 'class-validator';
import { Type } from 'class-transformer';

export class CourseDto {
  @IsNumber()
  id!: number;

  @IsNumber()
  universityId!: number;

  @IsString()
  title!: string;

  @IsString()
  duration!: string;

  @IsString()
  fieldOfStudy!: number;

  @IsDecimal()
  applicationFee!: number;

  @IsDecimal()
  tuitionFee!: number;

  @IsString()
  lastAcademic!: string;

  @IsNumber()
  minGpa!: number;

  @IsNumber()
  courseRank!: number;

  @IsNumber()
  acceptedRate!: number;

  @IsString()
  requirements!: string;

  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @IsString()
  createdAt!: string;

  @IsString()
  updatedAt!: string;

  // Include university details when needed
  @IsOptional()
  university?: {
    id: number;
    name: string;
    logo: string;
  };
}

export class CreateCourseRequestDto {
  @IsNumber()
  universityId!: number;

  @IsString()
  title!: string;

  @IsString()
  duration!: string;

  @IsNumber()
  fieldOfStudy!: number;

  @IsDecimal()
  applicationFee!: number;

  @IsDecimal()
  tuitionFee!: number;

  @IsString()
  lastAcademic!: string;

  @IsNumber()
  minGpa!: number;

  @IsNumber()
  courseRank!: number;

  @IsNumber()
  acceptedRate!: number;

  @IsString()
  requirements!: string;

  @IsBoolean()
  @IsOptional()
  isActive?: boolean;
}

export class CreateCourseResponseDto extends CourseDto {}

export class UpdateCourseRequestDto {
  @IsString()
  @IsOptional()
  title?: string;

  @IsString()
  @IsOptional()
  duration?: string;

  @IsString()
  @IsOptional()
  fieldOfStudy?: string;

  @IsDecimal()
  @IsOptional()
  applicationFee?: number;

  @IsDecimal()
  @IsOptional()
  tuitionFee?: number;

  @IsString()
  @IsOptional()
  lastAcademic?: string;

  @IsNumber()
  @IsOptional()
  minGpa?: number;

  @IsNumber()
  @IsOptional()
  courseRank?: number;

  @IsNumber()
  @IsOptional()
  acceptedRate?: number;

  @IsString()
  @IsOptional()
  requirements?: string;

  @IsBoolean()
  @IsOptional()
  isActive?: boolean;
}

export class UpdateCourseResponseDto extends CourseDto {}

export class DeleteCourseRequestDto {
  @IsNumber()
  id!: number;
}

export class DeleteCourseResponseDto {
  @IsNumber()
  id!: number;

  @IsBoolean()
  success!: boolean;
}

export class GetCourseRequestDto {
  @IsNumber()
  id!: number;
}

export class GetCourseResponseDto extends CourseDto {}

export class GetAllCoursesRequestDto {
  @IsNumber()
  @IsOptional()
  universityId?: number;

  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  page?: number;

  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  limit?: number;
}

export class GetAllCoursesResponseDto {
  @IsNumber()
  total!: number;

  @IsNumber()
  page!: number;

  @IsNumber()
  limit!: number;

  courses!: CourseDto[];
}
