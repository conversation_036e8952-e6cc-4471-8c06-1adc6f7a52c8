import { Module, DynamicModule } from '@nestjs/common';
import * as countryDtos from './country.dto';
import * as universityDtos from './university.dto';
// Import other DTO files as needed
// import * as alumniDtos from './alumni.dto';
// import * as programDtos from './program.dto';

@Module({})
export class UniversityDtoModule {
  static register(): DynamicModule {
    // Collect all DTOs from imported files
    const allDtoCollections = [
      countryDtos,
      universityDtos,
      // Add other DTO collections when available
      // alumniDtos,
      // programDtos,
    ];
    
    // Create providers for all exported classes from the DTO files
    const providers = [];
    
    // Loop through all DTO collections
    for (const dtoCollection of allDtoCollections) {
      // Get all exported items from the collection
      for (const [name, value] of Object.entries(dtoCollection)) {
        // Only add class constructors (DTOs)
        if (typeof value === 'function') {
          providers.push({
            provide: name,
            useValue: value
          });
        }
      }
    }
    
    return {
      module: UniversityDtoModule,
      providers: providers,
      exports: providers
    };
  }
}
