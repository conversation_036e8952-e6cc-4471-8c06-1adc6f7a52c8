import { IsString, <PERSON>N<PERSON><PERSON>, IsOptional, IsBoolean } from 'class-validator';

export class UniversityAlumniDto {
  @IsNumber()
  id!: number;

  @IsNumber()
  courseId!: number;

  @IsNumber()
  universityId!: number;

  @IsString()
  name!: string;

  @IsString()
  organization!: string;

  @IsString()
  designation!: string;

  @IsString()
  profile!: string;

  @IsBoolean()
  status!: boolean;
}

export class CreateUniversityAlumniRequestDto {
  @IsNumber()
  courseId!: number;

  @IsNumber()
  universityId!: number;

  @IsString()
  name!: string;

  @IsString()
  organization!: string;

  @IsString()
  designation!: string;

  @IsString()
  profile!: string;

  @IsBoolean()
  @IsOptional()
  status?: boolean;
}

export class UpdateUniversityAlumniRequestDto {
  @IsString()
  @IsOptional()
  name?: string;

  @IsString()
  @IsOptional()
  organization?: string;

  @IsString()
  @IsOptional()
  designation?: string;

  @IsString()
  @IsOptional()
  profile?: string;

  @IsBoolean()
  @IsOptional()
  status?: boolean;
}

export class GetAllUniversityAlumniRequestDto {
  @IsNumber()
  @IsOptional()
  universityId?: number;

  @IsNumber()
  @IsOptional()
  courseId?: number;

  @IsNumber()
  @IsOptional()
  page?: number;

  @IsNumber()
  @IsOptional()
  limit?: number;
}

export class GetAllUniversityAlumniResponseDto {
  @IsNumber()
  total!: number;

  @IsNumber()
  page!: number;

  @IsNumber()
  limit!: number;

  universityAlumni!: UniversityAlumniDto[];
}