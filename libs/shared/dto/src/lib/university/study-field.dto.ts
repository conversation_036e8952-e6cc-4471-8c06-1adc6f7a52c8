import { IsString, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Optional, IsBoolean } from 'class-validator';

export class StudyFieldDto {
  @IsNumber()
  id!: number;

  @IsString()
  field!: string;

  @IsString()
  @IsOptional()
  parentField!: string;

  @IsBoolean()
  isActive!: boolean;

  @IsString()
  createdAt!: string;

  @IsString()
  updatedAt!: string;
}

export class CreateStudyFieldDto {
  @IsString()
  field!: string;

  @IsString()
  @IsOptional()
  parentFieldId?: number;

  @IsBoolean()
  @IsOptional()
  isActive?: boolean;
}

export class UpdateStudyFieldDto {
  @IsString()
  @IsOptional()
  field?: string;

  @IsString()
  @IsOptional()
  parentFieldId?: number;

  @IsBoolean()
  @IsOptional()
  isActive?: boolean;
}