import { IsString, <PERSON>N<PERSON><PERSON>, IsOptional, IsEmail } from 'class-validator';

export class PrimaryContactDto {
  @IsNumber()
  id!: number;

  @IsNumber()
  universityId!: number;

  @IsString()
  phoneNumber!: string;

  @IsEmail()
  email!: string;
}

export class CreatePrimaryContactRequestDto {
  @IsNumber()
  universityId!: number;

  @IsString()
  phoneNumber!: string;

  @IsEmail()
  email!: string;
}

export class CreatePrimaryContactResponseDto extends PrimaryContactDto {}

export class UpdatePrimaryContactRequestDto {
  @IsString()
  @IsOptional()
  phoneNumber?: string;

  @IsEmail()
  @IsOptional()
  email?: string;
}

export class UpdatePrimaryContactResponseDto extends PrimaryContactDto {}

export class DeletePrimaryContactRequestDto {
  @IsNumber()
  id!: number;
}

export class DeletePrimaryContactResponseDto {
  @IsNumber()
  id!: number;

  @IsNumber()
  success!: boolean;
}

export class GetPrimaryContactRequestDto {
  @IsNumber()
  id!: number;
}

export class GetPrimaryContactResponseDto extends PrimaryContactDto {}