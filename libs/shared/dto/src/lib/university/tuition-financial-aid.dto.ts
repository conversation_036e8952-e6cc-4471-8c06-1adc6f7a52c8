import { IsN<PERSON>ber, IsBoolean, IsOptional } from 'class-validator';

export class CreateTuitionFinancialAidDto {
  @IsNumber()
  universityId!: number;

  @IsNumber()
  tuitionFeeDiscount!: number;

  @IsBoolean()
  financialAidAcceptance!: boolean;

  @IsBoolean()
  scholarshipOpportunity!: boolean;

  @IsBoolean()
  accommodationStatus!: boolean;
}

export class UpdateTuitionFinancialAidDto {
  @IsNumber()
  @IsOptional()
  tuitionFeeDiscount?: number;

  @IsBoolean()
  @IsOptional()
  financialAidAcceptance?: boolean;

  @IsBoolean()
  @IsOptional()
  scholarshipOpportunity?: boolean;

  @IsBoolean()
  @IsOptional()
  accommodationStatus?: boolean;
}
