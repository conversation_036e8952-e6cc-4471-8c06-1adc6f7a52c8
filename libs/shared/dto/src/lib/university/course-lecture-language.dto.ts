import { IsNumber, IsOptional } from 'class-validator';

export class CourseLectureLanguageDto {
  @IsNumber()
  id!: number;

  @IsNumber()
  courseId!: number;

  @IsNumber()
  languageId!: number;
}

export class CreateCourseLectureLanguageRequestDto {
  @IsNumber()
  courseId!: number;

  @IsNumber()
  languageId!: number;
}

export class GetAllCourseLectureLanguagesRequestDto {
  @IsNumber()
  @IsOptional()
  courseId?: number;

  @IsNumber()
  @IsOptional()
  languageId?: number;

  @IsNumber()
  @IsOptional()
  page?: number;

  @IsNumber()
  @IsOptional()
  limit?: number;
}

export class GetAllCourseLectureLanguagesResponseDto {
  @IsNumber()
  total!: number;

  @IsNumber()
  page!: number;

  @IsNumber()
  limit!: number;

  courseLectureLanguages!: CourseLectureLanguageDto[];
}