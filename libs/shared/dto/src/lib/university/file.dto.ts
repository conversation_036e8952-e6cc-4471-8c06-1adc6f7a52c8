import { IsString, <PERSON><PERSON><PERSON><PERSON>, IsOptional, IsBoolean } from 'class-validator';

export class CreateFileDto {
  @IsNumber()
  universityId!: number;

  @IsString()
  fileName!: string;

  @IsString()
  fileType!: string;

  @IsString()
  fileUrl!: string;

  @IsBoolean()
  @IsOptional()
  isActive?: boolean;
}

export class UpdateFileDto {
  @IsString()
  @IsOptional()
  fileName?: string;

  @IsString()
  @IsOptional()
  fileType?: string;

  @IsString()
  @IsOptional()
  fileUrl?: string;

  @IsBoolean()
  @IsOptional()
  isActive?: boolean;
}