import { IsString, <PERSON><PERSON><PERSON>Empty, Is<PERSON>ptional, IsBoolean } from 'class-validator';

export class CreateCountryDto {
  @IsString({ message: 'countryName must be a string' })
  @IsNotEmpty({ message: 'countryName is required' })
  countryName!: string;

  @IsString({ message: 'continent must be a string' })
  @IsNotEmpty({ message: 'continent is required' })
  continent!: string;
  
  @IsBoolean({ message: 'isActive must be a boolean' })
  @IsOptional()
  isActive: boolean = true; // default to true
}

export class UpdateCountryDto {
  @IsString({ message: 'countryName must be a string' })
  @IsOptional()
  countryName?: string;

  @IsString({ message: 'continent must be a string' })
  @IsOptional()
  continent?: string;
  
  @IsBoolean({ message: 'isActive must be a boolean' })
  @IsOptional()
  isActive: boolean = true; // default to true
}
