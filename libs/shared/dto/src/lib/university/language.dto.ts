import { IsString, <PERSON><PERSON><PERSON><PERSON>, IsOptional, IsBoolean } from 'class-validator';

export class LanguageDto {
  @IsNumber()
  id!: number;

  @IsString()
  name!: string;

  @IsBoolean()
  isActive!: boolean;
}

export class CreateLanguageRequestDto {
  @IsString()
  name!: string;

  @IsBoolean()
  @IsOptional()
  isActive?: boolean;
}

export class UpdateLanguageRequestDto {
  @IsString()
  @IsOptional()
  name?: string;

  @IsBoolean()
  @IsOptional()
  isActive?: boolean;
}

export class GetAllLanguagesRequestDto {
  @IsNumber()
  @IsOptional()
  page?: number;

  @IsNumber()
  @IsOptional()
  limit?: number;
}

export class GetAllLanguagesResponseDto {
  @IsNumber()
  total!: number;

  @IsNumber()
  page!: number;

  @IsNumber()
  limit!: number;

  languages!: LanguageDto[];
}
