import { IsString, IsNumber, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';

export class CourseTestRequirementDto {
  @IsNumber()
  id!: number;

  @IsNumber()
  courseId!: number;

  @IsString()
  testName!: string;

  @IsNumber()
  scoreOverall!: number;

  @IsNumber()
  reading!: number;

  @IsNumber()
  writing!: number;

  @IsNumber()
  listening!: number;

  @IsNumber()
  speaking!: number;

  @IsString()
  createdAt!: string;

  @IsString()
  updatedAt!: string;

  // Include course details when needed
  @IsOptional()
  course?: {
    id: number;
    title: string;
  };
}

export class CreateCourseTestRequirementRequestDto {
  @IsNumber()
  courseId!: number;

  @IsString()
  testName!: string;

  @IsNumber()
  scoreOverall!: number;

  @IsNumber()
  reading!: number;

  @IsNumber()
  writing!: number;

  @IsNumber()
  listening!: number;

  @IsNumber()
  speaking!: number;
}

export class UpdateCourseTestRequirementRequestDto {
  @IsString()
  @IsOptional()
  testName?: string;

  @IsNumber()
  @IsOptional()
  scoreOverall?: number;

  @IsNumber()
  @IsOptional()
  reading?: number;

  @IsNumber()
  @IsOptional()
  writing?: number;

  @IsNumber()
  @IsOptional()
  listening?: number;

  @IsNumber()
  @IsOptional()
  speaking?: number;
}

export class GetAllCourseTestRequirementsRequestDto {
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  courseId?: number;

  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  page?: number;

  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  limit?: number;
}

export class GetAllCourseTestRequirementsResponseDto {
  @IsNumber()
  total!: number;

  @IsNumber()
  page!: number;

  @IsNumber()
  limit!: number;

  // courseTestRequirements!: CourseTestRequirementDto[];
}
