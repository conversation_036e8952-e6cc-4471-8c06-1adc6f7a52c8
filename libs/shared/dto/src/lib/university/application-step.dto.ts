import { IsString, <PERSON>N<PERSON><PERSON>, IsOptional, IsBoolean } from 'class-validator';

export class CreateApplicationStepDto {
  @IsNumber()
  universityId!: number;

  @IsNumber()
  stepNumber!: number;

  @IsString()
  description!: string;

  @IsBoolean()
  @IsOptional()
  isActive?: boolean;
}

export class UpdateApplicationStepDto {
  @IsNumber()
  @IsOptional()
  stepNumber?: number;

  @IsString()
  @IsOptional()
  description?: string;

  @IsBoolean()
  @IsOptional()
  isActive?: boolean;
}
