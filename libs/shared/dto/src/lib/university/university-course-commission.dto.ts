import { IsString, IsNumber, IsOptional, IsDate } from 'class-validator';

export class UniversityCourseCommissionDto {
  @IsNumber()
  id!: number;

  @IsNumber()
  universityId!: number;

  @IsNumber()
  courseId!: number;

  @IsString()
  name!: string;

  @IsNumber()
  commission!: number;

  @IsString()
  payoutCircle!: string;

  @IsString()
  period!: string;

  @IsDate()
  startDate!: Date;

  @IsDate()
  endDate!: Date;

  @IsNumber()
  currencyId!: number;

  @IsString()
  paymentFrequency!: string;

  @IsString()
  paymentTerms!: string;

  @IsString()
  paymentMethod!: string;

  @IsString()
  note!: string;
}

export class CreateUniversityCourseCommissionRequestDto {
  @IsNumber()
  universityId!: number;

  @IsNumber()
  courseId!: number;

  @IsString()
  name!: string;

  @IsNumber()
  commission!: number;

  @IsString()
  payoutCircle!: string;

  @IsString()
  period!: string;

  @IsDate()
  startDate!: Date;

  @IsDate()
  endDate!: Date;

  @IsNumber()
  currencyId!: number;

  @IsString()
  paymentFrequency!: string;

  @IsString()
  paymentTerms!: string;

  @IsString()
  paymentMethod!: string;

  @IsString()
  @IsOptional()
  note?: string;
}

export class UpdateUniversityCourseCommissionRequestDto {
  @IsString()
  @IsOptional()
  name?: string;

  @IsNumber()
  @IsOptional()
  commission?: number;

  @IsString()
  @IsOptional()
  payoutCircle?: string;

  @IsString()
  @IsOptional()
  period?: string;

  @IsDate()
  @IsOptional()
  startDate?: Date;

  @IsDate()
  @IsOptional()
  endDate?: Date;

  @IsNumber()
  @IsOptional()
  currencyId?: number;

  @IsString()
  @IsOptional()
  paymentFrequency?: string;

  @IsString()
  @IsOptional()
  paymentTerms?: string;

  @IsString()
  @IsOptional()
  paymentMethod?: string;

  @IsString()
  @IsOptional()
  note?: string;
}

export class GetAllUniversityCourseCommissionsRequestDto {
  @IsNumber()
  @IsOptional()
  universityId?: number;

  @IsNumber()
  @IsOptional()
  courseId?: number;

  @IsNumber()
  @IsOptional()
  page?: number;

  @IsNumber()
  @IsOptional()
  limit?: number;
}

export class GetAllUniversityCourseCommissionsResponseDto {
  @IsNumber()
  total!: number;

  @IsNumber()
  page!: number;

  @IsNumber()
  limit!: number;

  commissions!: UniversityCourseCommissionDto[];
}