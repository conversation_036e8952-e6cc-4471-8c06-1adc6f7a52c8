syntax = "proto3";

package university;

service UniversityService {
  // Country operations
  rpc CreateCountry(CreateCountryRequest) returns (CountryResponse);
  rpc GetCountry(GetCountryRequest) returns (CountryResponse);
  rpc UpdateCountry(UpdateCountryRequest) returns (CountryResponse);
  rpc DeleteCountry(DeleteCountryRequest) returns (DeleteCountryResponse);
  rpc ListCountries(ListCountriesRequest) returns (ListCountriesResponse);
  
  // Course Study Field operations
  rpc CreateCourseStudyField(CreateCourseStudyFieldRequest) returns (CourseStudyFieldResponse);
  rpc GetCourseStudyField(GetCourseStudyFieldRequest) returns (CourseStudyFieldResponse);
  rpc UpdateCourseStudyField(UpdateCourseStudyFieldRequest) returns (CourseStudyFieldResponse);
  rpc DeleteCourseStudyField(DeleteCourseStudyFieldRequest) returns (DeleteCourseStudyFieldResponse);
  rpc ListCourseStudyFields(ListCourseStudyFieldsRequest) returns (ListCourseStudyFieldsResponse);
  rpc GetCourseStudyFieldsByCourse(GetCourseStudyFieldsByCourseRequest) returns (ListCourseStudyFieldsResponse);
  rpc GetCourseStudyFieldsByStudyField(GetCourseStudyFieldsByStudyFieldRequest) returns (ListCourseStudyFieldsResponse);
}

// Error message
message ErrorResponse {
  int32 code = 1;
  string message = 2;
  string details = 3;
}

// Country messages
message CreateCountryRequest {
  string countryName = 1;
  string continent = 2;
  bool isActive = 3;
}
message Country {
  int32 id = 1;
  string countryName = 2;
  string continent = 3;
  bool isActive = 4;
  string createdAt = 5;
  string updatedAt = 6;
}

message CountryResponse {
  int32 status = 1;
  string message = 2;
  Country data = 3;
  ErrorResponse error = 4;
}

message UpdateCountryRequest {
  int64 id = 1;
  string countryName = 2;
  string continent = 3;
  bool isActive = 4;
}

message GetCountryRequest {
  int64 id = 1;
}

message DeleteCountryRequest {
  int64 id = 1;
}

message DeleteCountryResponse {
  string status = 1;
  int32 code = 2;
  string message = 3;
  ErrorResponse error = 4;
}



message ListCountriesRequest {
  int32 page = 1;
  int32 limit = 2;
  bool isActive = 3;
}

message ListCountriesResponse {
  int32 total = 1;
  int32 page = 2;
  int32 limit = 3;
  repeated CountryResponse countries = 4;
  ErrorResponse error = 5;
}
// Course Study Field messages
message CreateCourseStudyFieldRequest {
  int64 course_id = 1;
  int64 study_field_id = 2;
  bool is_active = 3;
}

message GetCourseStudyFieldRequest {
  int64 id = 1;
}

message UpdateCourseStudyFieldRequest {
  int64 id = 1;
  bool is_active = 2;
}

message DeleteCourseStudyFieldRequest {
  int64 id = 1;
}

message CourseStudyFieldResponse {
  int64 id = 1;
  int64 course_id = 2;
  int64 study_field_id = 3;
  bool is_active = 4;
  CourseInfo course = 5;
  StudyFieldInfo study_field = 6;
  string created_at = 7;
  string updated_at = 8;
  ErrorResponse error = 9;
}

message CourseInfo {
  int64 id = 1;
  string title = 2;
}

message StudyFieldInfo {
  int64 id = 1;
  string field = 2;
}

message ListCourseStudyFieldsRequest {
  int32 page = 1;
  int32 limit = 2;
  int64 course_id = 3;
  int64 study_field_id = 4;
  bool is_active = 5;
}

message ListCourseStudyFieldsResponse {
  int32 total = 1;
  int32 page = 2;
  int32 limit = 3;
  repeated CourseStudyFieldResponse course_study_fields = 4;
  ErrorResponse error = 5;
}

message GetCourseStudyFieldsByCourseRequest {
  int64 course_id = 1;
}

message GetCourseStudyFieldsByStudyFieldRequest {
  int64 study_field_id = 1;
}

message DeleteCourseStudyFieldResponse {
  bool success = 1;
  int64 id = 2;
  ErrorResponse error = 3;
}
