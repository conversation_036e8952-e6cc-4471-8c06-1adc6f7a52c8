import { IsString, <PERSON>N<PERSON><PERSON>, IsOptional, IsBoolean } from 'class-validator';

export class CourseSubjectDto {
  @IsNumber()
  id!: number;

  @IsNumber()
  courseId!: number;

  @IsString()
  subject!: string;

  @IsNumber()
  credit!: number;

  @IsString()
  description!: string;

  @IsBoolean()
  status!: boolean;
}

export class CreateCourseSubjectRequestDto {
  @IsNumber()
  courseId!: number;

  @IsString()
  subject!: string;

  @IsNumber()
  credit!: number;

  @IsString()
  description!: string;

  @IsBoolean()
  @IsOptional()
  status?: boolean;
}

export class UpdateCourseSubjectRequestDto {
  @IsString()
  @IsOptional()
  subject?: string;

  @IsNumber()
  @IsOptional()
  credit?: number;

  @IsString()
  @IsOptional()
  description?: string;

  @IsBoolean()
  @IsOptional()
  status?: boolean;
}

export class GetAllCourseSubjectsRequestDto {
  @IsNumber()
  @IsOptional()
  courseId?: number;

  @IsNumber()
  @IsOptional()
  page?: number;

  @IsNumber()
  @IsOptional()
  limit?: number;
}

export class GetAllCourseSubjectsResponseDto {
  @IsNumber()
  total!: number;

  @IsNumber()
  page!: number;

  @IsNumber()
  limit!: number;

  courseSubjects!: CourseSubjectDto[];
}