import { IsString, <PERSON>N<PERSON><PERSON>, IsOptional, IsEmail } from 'class-validator';

export class CampusDto {
  @IsNumber()
  id!: number;

  @IsNumber()
  universityId!: number;

  @IsString()
  name!: string;

  @IsString()
  address!: string;

  @IsString()
  country!: string;

  @IsString()
  state!: string;

  @IsString()
  city!: string;

  @IsString()
  postalCode!: string;

  @IsString()
  contactNumber!: string;

  @IsEmail()
  email!: string;
}

export class CreateCampusRequestDto {
  @IsNumber()
  universityId!: number;

  @IsString()
  name!: string;

  @IsString()
  address!: string;

  @IsString()
  country!: string;

  @IsString()
  state!: string;

  @IsString()
  city!: string;

  @IsString()
  postalCode!: string;

  @IsOptional()
  @IsString()
  contactNumber?: string;

  @IsOptional()
  @IsEmail()
  email?: string;
}

export class CreateCampusResponseDto extends CampusDto {}

export class UpdateCampusRequestDto {
  @IsString()
  @IsOptional()
  name?: string;

  @IsString()
  @IsOptional()
  address?: string;

  @IsString()
  @IsOptional()
  country?: string;

  @IsString()
  @IsOptional()
  state?: string;

  @IsString()
  @IsOptional()
  city?: string;

  @IsString()
  @IsOptional()
  postalCode?: string;

  @IsString()
  @IsOptional()
  contactNumber?: string;

  @IsEmail()
  @IsOptional()
  email?: string;
}

export class UpdateCampusResponseDto extends CampusDto {}

export class DeleteCampusRequestDto {
  @IsNumber()
  id!: number;
}

export class DeleteCampusResponseDto {
  @IsNumber()
  id!: number;

  @IsNumber()
  success!: boolean;
}

export class GetCampusRequestDto {
  @IsNumber()
  id!: number;
}

export class GetCampusResponseDto extends CampusDto {}

export class GetAllCampusesRequestDto {
  @IsNumber()
  @IsOptional()
  universityId?: number;

  @IsNumber()
  @IsOptional()
  page?: number;

  @IsNumber()
  @IsOptional()
  limit?: number;
}

export class GetAllCampusesResponseDto {
  @IsNumber()
  total!: number;

  @IsNumber()
  page!: number;

  @IsNumber()
  limit!: number;

  campuses!: CampusDto[];
}
