import { IsString, IsNumber, IsOptional } from 'class-validator';

export class UniversityBankDto {
  @IsNumber()
  id!: number;

  @IsNumber()
  universityId!: number;

  @IsString()
  accountName!: string;

  @IsString()
  accountNumber!: string;

  @IsString()
  holderName!: string;

  @IsString()
  ibanSwiftCode!: string;

  @IsString()
  taxId!: string;

  @IsString()
  vatNumber!: string;

  @IsNumber()
  currencyId!: number;

  @IsString()
  paymentFrequency!: string;

  @IsString()
  paymentTerms!: string;

  @IsString()
  paymentMethod!: string;
}

export class CreateUniversityBankRequestDto {
  @IsNumber()
  universityId!: number;

  @IsString()
  accountName!: string;

  @IsString()
  accountNumber!: string;

  @IsString()
  holderName!: string;

  @IsString()
  ibanSwiftCode!: string;

  @IsString()
  taxId!: string;

  @IsString()
  vatNumber!: string;

  @IsNumber()
  currencyId!: number;

  @IsString()
  paymentFrequency!: string;

  @IsString()
  paymentTerms!: string;

  @IsString()
  paymentMethod!: string;
}

export class UpdateUniversityBankRequestDto {
  @IsString()
  @IsOptional()
  accountName?: string;

  @IsString()
  @IsOptional()
  accountNumber?: string;

  @IsString()
  @IsOptional()
  holderName?: string;

  @IsString()
  @IsOptional()
  ibanSwiftCode?: string;

  @IsString()
  @IsOptional()
  taxId?: string;

  @IsString()
  @IsOptional()
  vatNumber?: string;

  @IsNumber()
  @IsOptional()
  currencyId?: number;

  @IsString()
  @IsOptional()
  paymentFrequency?: string;

  @IsString()
  @IsOptional()
  paymentTerms?: string;

  @IsString()
  @IsOptional()
  paymentMethod?: string;
}

export class GetAllUniversityBanksRequestDto {
  @IsNumber()
  @IsOptional()
  universityId?: number;

  @IsNumber()
  @IsOptional()
  page?: number;

  @IsNumber()
  @IsOptional()
  limit?: number;
}

export class GetAllUniversityBanksResponseDto {
  @IsNumber()
  total!: number;

  @IsNumber()
  page!: number;

  @IsNumber()
  limit!: number;

  banks!: UniversityBankDto[];
}