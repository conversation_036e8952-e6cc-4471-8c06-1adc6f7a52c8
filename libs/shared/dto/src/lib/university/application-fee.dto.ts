import { IsString, <PERSON>N<PERSON>ber, IsDateString, IsOptional, IsBoolean } from 'class-validator';

export class CreateApplicationFeeDto {
  @IsNumber()
  universityId!: number;

  @IsNumber()
  stepNumber!: number;

  @IsString()
  description!: string;

  @IsString()
  minApplicationCriteria!: string;

  @IsNumber()
  standardApplicationFee!: number;

  @IsNumber()
  avgTuitionFee!: number;

  @IsNumber()
  costOfLiving!: number;

  @IsString()
  avgUndergraduateProgram!: string;

  @IsString()
  avgGraduateProgram!: string;

  @IsString()
  discountType!: string;

  @IsNumber()
  discountValue!: number;

  @IsDateString()
  startDate!: string;

  @IsDateString()
  endDate!: string;

  @IsString()
  promoCode!: string;

  @IsNumber()
  maxRedemptionAllowed!: number;

  @IsBoolean()
  @IsOptional()
  isActive?: boolean;
}

export class UpdateApplicationFeeDto {
  @IsNumber()
  @IsOptional()
  stepNumber?: number;

  @IsString()
  @IsOptional()
  description?: string;

  @IsString()
  @IsOptional()
  minApplicationCriteria?: string;

  @IsNumber()
  @IsOptional()
  standardApplicationFee?: number;

  @IsNumber()
  @IsOptional()
  avgTuitionFee?: number;

  @IsNumber()
  @IsOptional()
  costOfLiving?: number;

  @IsString()
  @IsOptional()
  avgUndergraduateProgram?: string;

  @IsString()
  @IsOptional()
  avgGraduateProgram?: string;

  @IsString()
  @IsOptional()
  discountType?: string;

  @IsNumber()
  @IsOptional()
  discountValue?: number;

  @IsDateString()
  @IsOptional()
  startDate?: string;

  @IsDateString()
  @IsOptional()
  endDate?: string;

  @IsString()
  @IsOptional()
  promoCode?: string;

  @IsNumber()
  @IsOptional()
  maxRedemptionAllowed?: number;

  @IsBoolean()
  @IsOptional()
  isActive?: boolean;
}