import { IsString, IsN<PERSON>ber, IsDate, IsOptional } from 'class-validator';

export class SemesterTimeframeDto {
  @IsNumber()
  id!: number;

  @IsNumber()
  universityId!: number;

  @IsString()
  semesterName!: string;

  @IsDate()
  applicationStartDate!: Date;

  @IsDate()
  applicationEndDate!: Date;

  @IsDate()
  classStartDate!: Date;

  @IsDate()
  classEndDate!: Date;
}

export class CreateSemesterTimeframeRequestDto {
  @IsNumber()
  universityId!: number;

  @IsString()
  semesterName!: string;

  @IsDate()
  applicationStartDate!: Date;

  @IsDate()
  applicationEndDate!: Date;

  @IsDate()
  classStartDate!: Date;

  @IsDate()
  classEndDate!: Date;
}

export class CreateSemesterTimeframeResponseDto extends SemesterTimeframeDto {}

export class UpdateSemesterTimeframeRequestDto {
  @IsString()
  @IsOptional()
  semesterName?: string;

  @IsDate()
  @IsOptional()
  applicationStartDate?: Date;

  @IsDate()
  @IsOptional()
  applicationEndDate?: Date;

  @IsDate()
  @IsOptional()
  classStartDate?: Date;

  @IsDate()
  @IsOptional()
  classEndDate?: Date;
}

export class UpdateSemesterTimeframeResponseDto extends SemesterTimeframeDto {}

export class DeleteSemesterTimeframeRequestDto {
  @IsNumber()
  id!: number;
}

export class DeleteSemesterTimeframeResponseDto {
  @IsNumber()
  id!: number;

  @IsNumber()
  success!: boolean;
}

export class GetSemesterTimeframeRequestDto {
  @IsNumber()
  id!: number;
}

export class GetSemesterTimeframeResponseDto extends SemesterTimeframeDto {}

export class GetAllSemesterTimeframesRequestDto {
  @IsNumber()
  @IsOptional()
  universityId?: number;

  @IsNumber()
  @IsOptional()
  page?: number;

  @IsNumber()
  @IsOptional()
  limit?: number;
}

export class GetAllSemesterTimeframesResponseDto {
  @IsNumber()
  total!: number;

  @IsNumber()
  page!: number;

  @IsNumber()
  limit!: number;

  semesterTimeframes!: SemesterTimeframeDto[];
}