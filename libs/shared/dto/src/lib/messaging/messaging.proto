syntax = "proto3";

package messaging;

// Main Messaging Service definition
service MessagingService {
  // Message Operations
  rpc SendMessage (SendMessageRequest) returns (SendMessageResponse) {}
  rpc GetMessages (GetMessagesRequest) returns (GetMessagesResponse) {}
  rpc DeleteMessage (DeleteMessageRequest) returns (DeleteMessageResponse) {}
  rpc UpdateMessage (UpdateMessageRequest) returns (UpdateMessageResponse) {}
  
  // Chat Room Operations
  rpc CreateChatRoom (CreateChatRoomRequest) returns (CreateChatRoomResponse) {}
  rpc JoinChatRoom (JoinChatRoomRequest) returns (JoinChatRoomResponse) {}
  rpc LeaveChatRoom (LeaveChatRoomRequest) returns (LeaveChatRoomResponse) {}
  
  // Stream Operations
  rpc StreamMessages (StreamMessagesRequest) returns (stream MessageEvent) {}
  rpc StreamChatRoomEvents (ChatRoomStreamRequest) returns (stream ChatRoomEvent) {}
}

// Message related messages
message Message {
  string id = 1;
  string content = 2;
  string sender_id = 3;
  string chat_room_id = 4;
  int64 timestamp = 5;
  MessageType type = 6;
  map<string, string> metadata = 7;
}

enum MessageType {
  TEXT = 0;
  IMAGE = 1;
  VIDEO = 2;
  FILE = 3;
  SYSTEM = 4;
}

message SendMessageRequest {
  string chat_room_id = 1;
  string content = 2;
  string sender_id = 3;
  MessageType type = 4;
  map<string, string> metadata = 5;
}

message SendMessageResponse {
  Message message = 1;
  string status = 2;
}

message GetMessagesRequest {
  string chat_room_id = 1;
  int32 limit = 2;
  int32 offset = 3;
  int64 since_timestamp = 4;
}

message GetMessagesResponse {
  repeated Message messages = 1;
  int32 total_count = 2;
  bool has_more = 3;
}

message DeleteMessageRequest {
  string message_id = 1;
  string user_id = 2;
}

message DeleteMessageResponse {
  bool success = 1;
  string status = 2;
}

message UpdateMessageRequest {
  string message_id = 1;
  string content = 2;
  string user_id = 3;
}

message UpdateMessageResponse {
  Message message = 1;
  string status = 2;
}

// Chat Room related messages
message ChatRoom {
  string id = 1;
  string name = 2;
  string description = 3;
  repeated string participant_ids = 4;
  string created_by = 5;
  int64 created_at = 6;
  ChatRoomType type = 7;
  map<string, string> metadata = 8;
}

enum ChatRoomType {
  DIRECT = 0;
  GROUP = 1;
  CHANNEL = 2;
}

message CreateChatRoomRequest {
  string name = 1;
  string description = 2;
  repeated string participant_ids = 3;
  ChatRoomType type = 4;
  map<string, string> metadata = 5;
}

message CreateChatRoomResponse {
  ChatRoom chat_room = 1;
  string status = 2;
}

message JoinChatRoomRequest {
  string chat_room_id = 1;
  string user_id = 2;
}

message JoinChatRoomResponse {
  bool success = 1;
  string status = 2;
}

message LeaveChatRoomRequest {
  string chat_room_id = 1;
  string user_id = 2;
}

message LeaveChatRoomResponse {
  bool success = 1;
  string status = 2;
}

// Streaming related messages
message StreamMessagesRequest {
  string chat_room_id = 1;
  string user_id = 2;
}

message MessageEvent {
  enum EventType {
    NEW = 0;
    UPDATED = 1;
    DELETED = 2;
    REACTION = 3;
  }
  
  EventType type = 1;
  Message message = 2;
  string user_id = 3;
  int64 timestamp = 4;
}

message ChatRoomStreamRequest {
  string chat_room_id = 1;
  string user_id = 2;
}

message ChatRoomEvent {
  enum EventType {
    USER_JOINED = 0;
    USER_LEFT = 1;
    ROOM_UPDATED = 2;
    TYPING_START = 3;
    TYPING_END = 4;
  }
  
  EventType type = 1;
  string user_id = 2;
  ChatRoom chat_room = 3;
  int64 timestamp = 4;
  map<string, string> metadata = 5;
}