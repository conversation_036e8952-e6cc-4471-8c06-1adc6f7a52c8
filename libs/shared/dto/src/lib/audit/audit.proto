syntax = "proto3";

package audit;

service AuditService {
  rpc CreateAuditLog (CreateAuditLogRequest) returns (AuditLogResponse) {}
  rpc GetAuditLog (GetAuditLogRequest) returns (AuditLogResponse) {}
  rpc ListAuditLogs (ListAuditLogsRequest) returns (ListAuditLogsResponse) {}
  rpc SearchAuditLogs (SearchAuditLogsRequest) returns (ListAuditLogsResponse) {}
}

message AuditLog {
  string id = 1;
  string userId = 2;
  string serviceName = 3;
  string action = 4;
  string resourceType = 5;
  string resourceId = 6;
  string description = 7;
  map<string, string> metadata = 8;
  string ipAddress = 9;
  string userAgent = 10;
  int64 createdAt = 11;
}

message CreateAuditLogRequest {
  int64 userId = 1;
  string userRole = 2;
  string actions = 3;
  string serviceName = 4;
  string resourceType = 5;
  int64 resourceId = 6;
  string description = 7;
  map<string, string> metadata = 8;
  string ipAddress = 9;
  string userAgent = 10;
  string source = 11;
}


message GetAuditLogRequest {
  string id = 1;
}

message AuditLogResponse {
  AuditLog auditLog = 1;
  string message = 2;
}

message ListAuditLogsRequest {
  int32 pageSize = 1;
  string pageToken = 2;
  string orderBy = 3;
}

message ListAuditLogsResponse {
  repeated AuditLog auditLogs = 1;
  string nextPageToken = 2;
  int32 totalSize = 3;
}

message SearchAuditLogsRequest {
  string userId = 1;
  string serviceName = 2;
  string action = 3;
  string resourceType = 4;
  string resourceId = 5;
  int64 startDate = 6;
  int64 endDate = 7;
  int32 pageSize = 8;
  string pageToken = 9;
}
