syntax = "proto3";

package agency;

service AgencyService {
  rpc GetAgency (GetAgencyRequest) returns (GetAgencyResponse) {}
  rpc CreateAgency (CreateAgencyRequest) returns (GetAgencyResponse) {}
  rpc GetAgencyAuditLogs (GetAgencyAuditLogsRequest) returns (GetAgencyAuditLogsResponse) {}
}

message GetAgencyRequest {
  string id = 1;
  string user_id = 2;
}

message CreateAgencyRequest {
  string name = 1;
  string user_id = 2;
}

message GetAgencyResponse {
  string name = 1;
}

message GetAgencyAuditLogsRequest {
  string agency_id = 1;
}

message GetAgencyAuditLogsResponse {
  string id = 1;
  string user_id = 2;
  string service_name = 3;
  string action = 4;
  string resource_type = 5;
  string resource_id = 6;
  string description = 7;
  map<string, string> metadata = 8;
  string ip_address = 9;
  string user_agent = 10;
  int64 created_at = 11;
}
