syntax = "proto3";

package students;

// Main Student Service definition
service StudentService {
  // Student CRUD Operations
  rpc CreateStudent(CreateStudentRequest) returns (StudentResponse) {}
  rpc CreateStudentAfterRegistration(CreateStudentAfterRegistrationRequest)
      returns (StudentResponse) {}
  rpc GetStudent(GetStudentRequest) returns (StudentResponse) {}
  rpc UpdateStudent(UpdateStudentRequest) returns (StudentResponse) {}
  rpc DeleteStudent(DeleteStudentRequest) returns (DeleteStudentResponse) {}
  rpc ListStudents(ListStudentsRequest) returns (ListStudentsResponse) {}

  // Academic Operations
  rpc EnrollInCourse(EnrollmentRequest) returns (EnrollmentResponse) {}
  rpc DropCourse(EnrollmentRequest) returns (EnrollmentResponse) {}
  rpc GetEnrollments(GetEnrollmentsRequest) returns (GetEnrollmentsResponse) {}
  rpc UpdateGrades(UpdateGradesRequest) returns (UpdateGradesResponse) {}

  // Academic Progress
  rpc GetAcademicProgress(AcademicProgressRequest) returns (AcademicProgressResponse) {}
  rpc GetTranscript(TranscriptRequest) returns (TranscriptResponse) {}

  // Stream Operations
  rpc StreamStudentUpdates(StreamStudentRequest) returns (stream StudentEvent) {}
}

// Basic Student message
message Student {
  string id = 1;
  string first_name = 2;
  string last_name = 3;
  string email = 4;
  string phone = 5;
  Date date_of_birth = 6;
  string student_id = 7;
  EnrollmentStatus enrollment_status = 8;
  AcademicLevel academic_level = 9;
  string major = 10;
  string minor = 11;
  double gpa = 12;
  int32 total_credits = 13;
  Address address = 14;
  repeated EmergencyContact emergency_contacts = 15;
  map<string, string> metadata = 16;
  Timestamp created_at = 17;
  Timestamp updated_at = 18;
}

// Date message
message Date {
  int32 year = 1;
  int32 month = 2;
  int32 day = 3;
}

// Timestamp message
message Timestamp {
  int64 seconds = 1;
  int32 nanos = 2;
}

// Address message
message Address {
  string street = 1;
  string city = 2;
  string state = 3;
  string postal_code = 4;
  string country = 5;
}

// Emergency Contact message
message EmergencyContact {
  string name = 1;
  string relationship = 2;
  string phone = 3;
  string email = 4;
}

// Enrollment Status enum
enum EnrollmentStatus {
  ENROLLMENT_STATUS_UNSPECIFIED = 0;
  ENROLLMENT_STATUS_ACTIVE = 1;
  ENROLLMENT_STATUS_INACTIVE = 2;
  ENROLLMENT_STATUS_GRADUATED = 3;
  ENROLLMENT_STATUS_ON_LEAVE = 4;
  ENROLLMENT_STATUS_WITHDRAWN = 5;
  ENROLLMENT_STATUS_SUSPENDED = 6;
}

// Academic Level enum
enum AcademicLevel {
  ACADEMIC_LEVEL_UNSPECIFIED = 0;
  ACADEMIC_LEVEL_FRESHMAN = 1;
  ACADEMIC_LEVEL_SOPHOMORE = 2;
  ACADEMIC_LEVEL_JUNIOR = 3;
  ACADEMIC_LEVEL_SENIOR = 4;
  ACADEMIC_LEVEL_GRADUATE = 5;
}

// Course Enrollment message
message CourseEnrollment {
  string course_id = 1;
  string semester = 2;
  string grade = 3;
  double credits = 4;
  EnrollmentStatus status = 5;
  Timestamp enrolled_at = 6;
}

// Request/Response messages for CRUD operations
message CreateStudentRequest {
  string first_name = 1;
  string last_name = 2;
  string email = 3;
  string phone = 4;
  Date date_of_birth = 5;
  string major = 6;
  string minor = 7;
  Address address = 8;
  repeated EmergencyContact emergency_contacts = 9;
  map<string, string> metadata = 10;
}

message CreateStudentAfterRegistrationRequest {
  string user_id = 1;
  string first_name = 2;
  string last_name = 3;
  string email = 4;
  string phone = 5;
  Date date_of_birth = 6;
  string major = 7;
  string minor = 8;
  AcademicLevel academic_level = 9;
  Address address = 10;
  repeated EmergencyContact emergency_contacts = 11;
  map<string, string> metadata = 12;
}

message GetStudentRequest {
  string id = 1;
}

message UpdateStudentRequest {
  string id = 1;
  Student student = 2;
  repeated string update_mask = 3;
}

message DeleteStudentRequest {
  string id = 1;
}

message DeleteStudentResponse {
  bool success = 1;
  string message = 2;
}

message ListStudentsRequest {
  int32 page_size = 1;
  string page_token = 2;
  string filter = 3;
  string order_by = 4;
}

message ListStudentsResponse {
  repeated Student students = 1;
  string next_page_token = 2;
  int32 total_count = 3;
}

message StudentResponse {
  Student student = 1;
}

// Academic Operations messages
message EnrollmentRequest {
  string student_id = 1;
  string course_id = 2;
  string semester = 3;
}

message EnrollmentResponse {
  CourseEnrollment enrollment = 1;
  string status = 2;
}

message GetEnrollmentsRequest {
  string student_id = 1;
  string semester = 2;
}

message GetEnrollmentsResponse {
  repeated CourseEnrollment enrollments = 1;
}

message UpdateGradesRequest {
  string student_id = 1;
  string course_id = 2;
  string grade = 3;
}

message UpdateGradesResponse {
  bool success = 1;
  string message = 2;
  double new_gpa = 3;
}

// Academic Progress messages
message AcademicProgressRequest {
  string student_id = 1;
}

message AcademicProgressResponse {
  double gpa = 1;
  int32 credits_completed = 2;
  int32 credits_remaining = 3;
  repeated string completed_requirements = 4;
  repeated string pending_requirements = 5;
}

message TranscriptRequest {
  string student_id = 1;
}

message TranscriptResponse {
  Student student = 1;
  repeated CourseEnrollment courses = 2;
  double cumulative_gpa = 3;
  int32 total_credits_earned = 4;
  repeated string honors = 5;
  string academic_standing = 6;
}

// Streaming messages
message StreamStudentRequest {
  string student_id = 1;
}

message StudentEvent {
  enum EventType {
    EVENT_TYPE_UNSPECIFIED = 0;
    EVENT_TYPE_CREATED = 1;
    EVENT_TYPE_UPDATED = 2;
    EVENT_TYPE_DELETED = 3;
    EVENT_TYPE_ENROLLED = 4;
    EVENT_TYPE_DROPPED = 5;
    EVENT_TYPE_GRADE_UPDATED = 6;
    EVENT_TYPE_STATUS_CHANGED = 7;
  }

  EventType type = 1;
  Student student = 2;
  Timestamp timestamp = 3;
  map<string, string> metadata = 4;
}