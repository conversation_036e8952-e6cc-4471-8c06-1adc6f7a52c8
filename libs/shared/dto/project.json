{"name": "dto", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/shared/dto/src", "projectType": "library", "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/shared/dto", "main": "libs/shared/dto/src/index.ts", "tsConfig": "libs/shared/dto/tsconfig.lib.json", "assets": ["libs/shared/dto/*.md"]}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}}, "tags": []}