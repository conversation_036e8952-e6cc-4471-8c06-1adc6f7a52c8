import { Injectable, Inject } from '@nestjs/common';
import { JwtService as NestJwtService } from '@nestjs/jwt';
import { TokenPayload, RefreshTokenPayload } from '../token/token.interface';

@Injectable()
export class JwtService {
  constructor(
    private readonly jwtService: NestJwtService,
    @Inject('AUTH_OPTIONS') private readonly options: any
  ) {}

  async generateAccessToken(payload: TokenPayload): Promise<string> {
    return this.jwtService.sign(payload);
  }

  async generateRefreshToken(userId: bigint): Promise<string> {
    const payload: RefreshTokenPayload = {
      sub: userId,
      type: 'refresh',
    };

    return this.jwtService.sign(payload, {
      expiresIn: this.options.refreshExpiresIn,
    });
  }

  async verifyToken(token: string): Promise<any> {
    try {
      return this.jwtService.verify(token);
    } catch (error) {
      return null;
    }
  }

  async decodeToken(token: string): Promise<any> {
    return this.jwtService.decode(token);
  }

  async validateAccessToken(token: string): Promise<TokenPayload | null> {
    try {
      const payload = await this.jwtService.verify(token);
      if (payload.type === 'access') {
        return payload;
      }
      return null;
    } catch (error) {
      return null;
    }
  }

  async validateRefreshToken(
    token: string
  ): Promise<RefreshTokenPayload | null> {
    try {
      const payload = await this.jwtService.verify(token);
      if (payload.type === 'refresh') {
        return payload;
      }
      return null;
    } catch (error) {
      return null;
    }
  }
}
