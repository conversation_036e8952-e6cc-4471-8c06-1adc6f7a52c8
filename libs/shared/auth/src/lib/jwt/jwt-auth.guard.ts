// src/auth/jwt/jwt-auth.guard.ts
import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core'; // ← import Reflector
import { JwtService } from './jwt.service';
import { IS_PUBLIC_KEY } from '../public/public.decorator'; // ← import our new metadata key

@Injectable()
export class JwtAuthGuard implements CanActivate {
  constructor(
    private readonly jwtService: JwtService,
    private readonly reflector: Reflector // ← inject Reflector
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    // 1) If the handler or class is marked public, skip JWT check:
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);
    if (isPublic) {
      return true;
    }

    // 2) Otherwise, enforce the normal JWT logic:
    const request = context.switchToHttp().getRequest();
    const token = this.extractTokenFromHeader(request);
    if (!token) {
      throw new UnauthorizedException('No token provided');
    }

    const payload = await this.jwtService.validateAccessToken(token);
    if (!payload) {
      throw new UnauthorizedException('Invalid or expired token');
    }

    // Attach entire payload, so later guards can read roles + departments + permissions
    request.user = {
      id: payload.sub,
      email: payload.email,
      roles: payload.roles,
      departments: payload.departments,
      permissions: payload.permissions, // (subaction strings)
    };

    return true;
  }

  private extractTokenFromHeader(request: any): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}
