import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { DEPARTMENTS_KEY } from './departments.decorator';

@Injectable()
export class DepartmentsGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredDepts = this.reflector.getAllAndOverride<string[]>(
      DEPARTMENTS_KEY,
      [context.getHandler(), context.getClass()]
    );
    if (!requiredDepts || requiredDepts.length === 0) {
      return true; // no department restriction
    }

    const { user } = context.switchToHttp().getRequest();
    if (!user) {
      throw new ForbiddenException('User not authenticated');
    }

    const userDepts: string[] = user.departments || [];
    // check if user belongs to at least one required department
    const hasDept = requiredDepts.some((d) => userDepts.includes(d));
    if (!hasDept) {
      throw new ForbiddenException('Insufficient department privileges');
    }
    return true;
  }
}
