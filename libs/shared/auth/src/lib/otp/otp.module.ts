import { Module, DynamicModule } from '@nestjs/common';
import { OtpService } from './otp.service';
import { RedisModule } from '@nestjs-modules/ioredis';

export interface OtpModuleOptions {
  redis: {
    host: string;
    port: number;
    password?: string;
    db?: number;
  };
}

@Module({})
export class OtpModule {
  static forRoot(options: OtpModuleOptions): DynamicModule {
    return {
      module: OtpModule,
      imports: [
        RedisModule.forRootAsync({
          useFactory: () => ({
            type: 'single',
            url: `redis://${
              options.redis.password ? `:${options.redis.password}@` : ''
            }${options.redis.host}:${options.redis.port}`,
            db: options.redis.db || 0,
          }),
        }),
      ],
      providers: [OtpService],
      exports: [OtpService],
    };
  }
}
