import { Inject, Injectable } from '@nestjs/common';
import { Redis } from 'ioredis';
import { OtpPayload, GenerateOtpOptions } from './otp.interface';
import { InjectRedis } from '@nestjs-modules/ioredis';

@Injectable()
export class OtpService {
  constructor(@InjectRedis() private readonly redisClient: Redis) {}

  async generateOtp(options: GenerateOtpOptions): Promise<OtpPayload> {
    const otp = this.createOtpCode(options.length || 6);
    const expiresIn = options.expiresInSeconds || 300; // 5 minutes
    const expiresAt = new Date(Date.now() + expiresIn * 1000);

    const payload: OtpPayload = {
      identifier: options.identifier,
      otp,
      expiresAt,
    };

    await this.redisClient.setex(
      this.buildKey(options.identifier),
      expiresIn,
      otp
    );

    return payload;
  }

  async verifyOtp(identifier: string, code: string): Promise<boolean> {
    const key = this.buildKey(identifier);
    const storedOtp = await this.redisClient.get(key);

    if (storedOtp && storedOtp === code) {
      await this.redisClient.del(key); // one-time use
      return true;
    }

    return false;
  }

  private createOtpCode(length: number): string {
    const digits = '0123456789';
    let otp = '';
    for (let i = 0; i < length; i++) {
      otp += digits[Math.floor(Math.random() * 10)];
    }
    return otp;
  }

  private buildKey(identifier: string): string {
    return `otp:${identifier}`;
  }
}
