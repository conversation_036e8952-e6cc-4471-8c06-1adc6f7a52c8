import { Module, DynamicModule } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { JwtService } from './jwt/jwt.service';
import { RoleService } from './role/role.service';
import { JwtAuthGuard } from './jwt/jwt-auth.guard';
import { SSOModule } from './sso/sso.module';
import { GoogleAuthService } from './sso/google-auth.service';
import { APP_GUARD } from '@nestjs/core';
import { PermissionsGuard } from './permissions/permissions.guard';
import { DepartmentsGuard } from './department/departments.guard';
import { ConfigModule } from '@nestjs/config';
import { SubFeaturesGuard } from './sub-features/subfeatures.guard';
import { RolesGuard } from './role/roles.guard';

export interface AuthModuleOptions {
  secret: string;
  expiresIn?: string;
  refreshExpiresIn?: string;
}

@Module({})
export class AuthenticationModule {
  static forRoot(options: AuthModuleOptions): DynamicModule {
    return {
      module: AuthenticationModule,
      imports: [
        ConfigModule.forRoot({
          isGlobal: false,
        }),
        JwtModule.register({
          secret: options.secret,
          signOptions: {
            expiresIn: options.expiresIn || '1h',
          },
        }),
        SSOModule,
      ],
      providers: [
        {
          provide: 'AUTH_OPTIONS',
          useValue: {
            secret: options.secret,
            expiresIn: options.expiresIn || '1h',
            refreshExpiresIn: options.refreshExpiresIn || '7d',
          },
        },
        JwtService,
        RoleService,
        { provide: APP_GUARD, useClass: JwtAuthGuard },
        { provide: APP_GUARD, useClass: PermissionsGuard },
        { provide: APP_GUARD, useClass: DepartmentsGuard },
        { provide: APP_GUARD, useClass: SubFeaturesGuard },
        { provide: APP_GUARD, useClass: RolesGuard },
        GoogleAuthService,
        JwtAuthGuard,
        PermissionsGuard,
        DepartmentsGuard,
        SubFeaturesGuard,
        RolesGuard,
      ],
      exports: [
        JwtService,
        RoleService,
        JwtAuthGuard,
        DepartmentsGuard,
        PermissionsGuard,
        SubFeaturesGuard,
        RolesGuard,
        GoogleAuthService,
      ],
    };
  }
}
