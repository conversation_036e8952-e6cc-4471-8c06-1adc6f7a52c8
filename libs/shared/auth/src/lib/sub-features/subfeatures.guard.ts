// src/auth/subfeatures.guard.ts
import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { SUBFEATURES_KEY } from './subfeatures.decorator';

@Injectable()
export class SubFeaturesGuard implements CanActivate {
  constructor(private readonly reflector: Reflector) {}

  canActivate(ctx: ExecutionContext): boolean {
    const required = this.reflector.getAllAndOverride<string[]>(
      SUBFEATURES_KEY,
      [ctx.getHandler(), ctx.getClass()]
    );
    if (!required || required.length === 0) return true;

    const req = ctx.switchToHttp().getRequest();
    const user = req.user;
    if (!user) throw new ForbiddenException('Not authenticated');

    // Check direct user permissions
    const directPermissions = Array.isArray(user.permissions)
      ? user.permissions
      : [];

    // Check department-level permissions
    const departmentPermissions = [];
    if (Array.isArray(user.departments)) {
      for (const dept of user.departments) {
        if (Array.isArray(dept.subFeatures)) {
          for (const sf of dept.subFeatures) {
            if (sf.DepartmentSubFeaturePermission?.isAllowed) {
              const fullPermission = `${sf.feature?.module?.name}:${sf.feature?.name}:${sf.name}`;
              departmentPermissions.push(fullPermission);
            }
          }
        }
      }
    }

    // Combine all permissions
    const allPermissions = [...directPermissions, ...departmentPermissions];

    const hasAll = required.every((key) => allPermissions.includes(key));
    if (!hasAll) {
      throw new ForbiddenException('Missing required sub-feature permissions');
    }
    return true;
  }
}
