import { Injectable } from '@nestjs/common';

@Injectable()
export class RoleService {
  async validateUserRoles(
    userRoles: string[],
    requiredRoles: string[]
  ): Promise<boolean> {
    if (!requiredRoles || requiredRoles.length === 0) {
      return true; // No specific roles required
    }

    if (!userRoles || userRoles.length === 0) {
      return false; // User has no roles
    }

    // Check if user has at least one of the required roles
    return requiredRoles.some((role) => userRoles.includes(role));
  }

  async validateUserPermissions(
    userPerms: string[],
    requiredPerms: string[]
  ): Promise<boolean> {
    if (!requiredPerms.length) return true;
    if (!userPerms || !userPerms.length) return false;
    return requiredPerms.every((p) => userPerms.includes(p));
  }
}
