import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  Logger,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { ROLES_KEY } from './roles.decorator';

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.getAllAndOverride<string[]>(
      ROLES_KEY,
      [context.getHandler(), context.getClass()]
    );
    if (!requiredRoles || requiredRoles.length === 0) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;
    if (!user?.roles) {
      throw new ForbiddenException('No roles found on user');
    }

    // Normalize helper: remove spaces & lowercase
    const normalize = (s: string) => s.replace(/\s+/g, '').toLowerCase();

    const normalizedUserRoles = user.roles.map(normalize);
    const normalizedRequired = requiredRoles.map(normalize);

    const hasRole = normalizedRequired.some((r) =>
      normalizedUserRoles.includes(r)
    );
    Logger.debug('hasRole', hasRole, normalizedRequired, normalizedUserRoles);

    if (!hasRole) {
      throw new ForbiddenException('Insufficient role privileges');
    }

    return true;
  }
}
