{"name": "health-check", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/shared/health-check/src", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/shared/health-check", "tsConfig": "libs/shared/health-check/tsconfig.lib.json", "packageJson": "libs/shared/health-check/package.json", "main": "libs/shared/health-check/src/index.ts", "assets": ["libs/shared/health-check/*.md"]}}}}