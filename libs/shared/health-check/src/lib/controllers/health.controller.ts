import { Request, Response } from 'express';

export class HealthController {
  async check(req: Request, res: Response) {
    try {
      const healthCheck = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        checks: {
          database: {
            status: 'up'
          },
          redis: {
            status: 'up'
          }
        }
      };
      
      res.status(200).json(healthCheck);
    } catch (error) {
      res.status(503).json({
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error.message
      });
    }
  }
}