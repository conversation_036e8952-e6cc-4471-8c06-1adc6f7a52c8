import { Request, Response } from 'express';

export class ServiceHealthController {
  private serviceName: string;

  constructor(serviceName: string = 'service') {
    this.serviceName = serviceName;
  }

  async check(req: Request, res: Response) {
    try {
      const healthCheck = {
        status: 'healthy',
        service: this.serviceName,
        timestamp: new Date().toISOString(),
        version: process.env.npm_package_version || '1.0.0'
      };
      
      res.status(200).json(healthCheck);
    } catch (error) {
      res.status(503).json({
        status: 'unhealthy',
        service: this.serviceName,
        timestamp: new Date().toISOString(),
        error: error.message
      });
    }
  }
}