import { ServiceHealthController } from './service-health.controller';
import { Request, Response } from 'express';

describe('ServiceHealthController', () => {
  let controller: ServiceHealthController;
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;

  beforeEach(() => {
    controller = new ServiceHealthController('test-service');
    mockRequest = {};
    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    };
  });

  it('should return service health status', async () => {
    await controller.check(mockRequest as Request, mockResponse as Response);

    expect(mockResponse.status).toHaveBeenCalledWith(200);
    expect(mockResponse.json).toHaveBeenCalledWith(
      expect.objectContaining({
        status: 'healthy',
        service: 'test-service'
      })
    );
  });
});