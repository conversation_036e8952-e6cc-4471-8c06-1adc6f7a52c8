{"name": "config", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/shared/config/src", "projectType": "library", "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/shared/config", "main": "libs/shared/config/src/index.ts", "tsConfig": "libs/shared/config/tsconfig.lib.json", "assets": ["libs/shared/config/*.md"]}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}}, "tags": []}