import { DateUtil } from './date.util';

describe('DateUtil', () => {
  const testDate = new Date('2023-12-25T10:30:45.000Z');

  describe('toISOString', () => {
    it('should convert date to ISO string', () => {
      expect(DateUtil.toISOString(testDate)).toBe('2023-12-25T10:30:45.000Z');
    });
  });

  describe('formatDate', () => {
    it('should format date to YYYY-MM-DD', () => {
      expect(DateUtil.formatDate(testDate)).toBe('2023-12-25');
    });
  });

  describe('formatDateTime', () => {
    it('should format date to YYYY-MM-DD HH:mm:ss', () => {
      const result = DateUtil.formatDateTime(testDate);
      expect(result).toMatch(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/);
    });
  });

  describe('addDays', () => {
    it('should add days to date', () => {
      const result = DateUtil.addDays(testDate, 5);
      expect(DateUtil.formatDate(result)).toBe('2023-12-30');
    });
  });

  describe('isBetween', () => {
    it('should check if date is between two dates', () => {
      const start = new Date('2023-12-20');
      const end = new Date('2023-12-30');
      expect(DateUtil.isBetween(testDate, start, end)).toBe(true);
    });
  });

  describe('isValid', () => {
    it('should validate dates', () => {
      expect(DateUtil.isValid(testDate)).toBe(true);
      expect(DateUtil.isValid('invalid-date')).toBe(false);
    });
  });

  describe('format', () => {
    it('should format date using custom format string', () => {
      expect(DateUtil.format(testDate, 'YYYY-MM-DD')).toBe('2023-12-25');
      expect(DateUtil.format(testDate, 'HH:mm:ss')).toMatch(/^\d{2}:\d{2}:\d{2}$/);
    });
  });

  describe('relative', () => {
    it('should return relative time string', () => {
      const now = new Date();
      const fiveMinutesAgo = DateUtil.addDays(now, -5);
      expect(DateUtil.relative(fiveMinutesAgo)).toContain('days ago');
    });
  });
});