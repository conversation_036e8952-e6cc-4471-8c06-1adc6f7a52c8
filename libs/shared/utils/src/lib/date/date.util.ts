export class DateUtil {
  /**
   * Converts a date to ISO string format (YYYY-MM-DDTHH:mm:ss.sssZ)
   */
  static toISOString(date: Date | string | number): string {
    const convertedDate = DateUtil.toDate(date);
    return convertedDate.toISOString();
  }

  /**
   * Converts any date input to Date object
   */
  static toDate(date: Date | string | number): Date {
    if (date instanceof Date) {
      return date;
    }
    return new Date(date);
  }

  /**
   * Formats date to YYYY-MM-DD
   */
  static formatDate(date: Date | string | number): string {
    const d = DateUtil.toDate(date);
    return d.toISOString().split('T')[0];
  }

  /**
   * Formats time to HH:mm:ss
   */
  static formatTime(date: Date | string | number): string {
    const d = DateUtil.toDate(date);
    return d.toTimeString().split(' ')[0];
  }

  /**
   * Formats datetime to YYYY-MM-DD HH:mm:ss
   */
  static formatDateTime(date: Date | string | number): string {
    const d = DateUtil.toDate(date);
    return `${DateUtil.formatDate(d)} ${DateUtil.formatTime(d)}`;
  }

  /**
   * Adds specified number of days to a date
   */
  static addDays(date: Date | string | number, days: number): Date {
    const d = DateUtil.toDate(date);
    d.setDate(d.getDate() + days);
    return d;
  }

  /**
   * Adds specified number of months to a date
   */
  static addMonths(date: Date | string | number, months: number): Date {
    const d = DateUtil.toDate(date);
    d.setMonth(d.getMonth() + months);
    return d;
  }

  /**
   * Adds specified number of years to a date
   */
  static addYears(date: Date | string | number, years: number): Date {
    const d = DateUtil.toDate(date);
    d.setFullYear(d.getFullYear() + years);
    return d;
  }

  /**
   * Returns the difference between two dates in days
   */
  static diffInDays(date1: Date | string | number, date2: Date | string | number): number {
    const d1 = DateUtil.toDate(date1);
    const d2 = DateUtil.toDate(date2);
    const diffTime = Math.abs(d2.getTime() - d1.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  /**
   * Checks if a date is between two other dates
   */
  static isBetween(
    date: Date | string | number,
    startDate: Date | string | number,
    endDate: Date | string | number
  ): boolean {
    const d = DateUtil.toDate(date);
    const start = DateUtil.toDate(startDate);
    const end = DateUtil.toDate(endDate);
    return d >= start && d <= end;
  }

  /**
   * Returns start of day (00:00:00.000)
   */
  static startOfDay(date: Date | string | number): Date {
    const d = DateUtil.toDate(date);
    d.setHours(0, 0, 0, 0);
    return d;
  }

  /**
   * Returns end of day (23:59:59.999)
   */
  static endOfDay(date: Date | string | number): Date {
    const d = DateUtil.toDate(date);
    d.setHours(23, 59, 59, 999);
    return d;
  }

  /**
   * Checks if a date is valid
   */
  static isValid(date: Date | string | number): boolean {
    try {
      const d = DateUtil.toDate(date);
      return d instanceof Date && !isNaN(d.getTime());
    } catch {
      return false;
    }
  }

  /**
   * Returns current timestamp in milliseconds
   */
  static timestamp(): number {
    return Date.now();
  }

  /**
   * Formats a date using specified format string
   * Supported formats: YYYY, MM, DD, HH, mm, ss
   */
  static format(date: Date | string | number, format: string): string {
    const d = DateUtil.toDate(date);
    
    const tokens = {
      YYYY: d.getFullYear().toString(),
      MM: (d.getMonth() + 1).toString().padStart(2, '0'),
      DD: d.getDate().toString().padStart(2, '0'),
      HH: d.getHours().toString().padStart(2, '0'),
      mm: d.getMinutes().toString().padStart(2, '0'),
      ss: d.getSeconds().toString().padStart(2, '0'),
    };

    return Object.entries(tokens).reduce((result, [token, value]) => {
      return result.replace(token, value);
    }, format);
  }

  /**
   * Returns relative time string (e.g., "2 hours ago", "in 3 days")
   */
  static relative(date: Date | string | number): string {
    const d = DateUtil.toDate(date);
    const now = new Date();
    const diff = now.getTime() - d.getTime();
    const seconds = Math.floor(Math.abs(diff) / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (diff < 0) {
      if (seconds < 60) return `in ${seconds} seconds`;
      if (minutes < 60) return `in ${minutes} minutes`;
      if (hours < 24) return `in ${hours} hours`;
      return `in ${days} days`;
    } else {
      if (seconds < 60) return `${seconds} seconds ago`;
      if (minutes < 60) return `${minutes} minutes ago`;
      if (hours < 24) return `${hours} hours ago`;
      return `${days} days ago`;
    }
  }
}