export class EnvironmentError extends Error {
  constructor(variable: string, additionalInfo?: string) {
    const message = `Environment variable ${variable} is not set${
      additionalInfo ? `: ${additionalInfo}` : ''
    }`;
    super(message);
    this.name = 'EnvironmentError';
  }
}

export class EnvUtil {
  static getString(key: string, defaultValue?: string): string {
    const value = process.env[key];
    
    if (!value && defaultValue === undefined) {
      throw new EnvironmentError(key);
    }
    
    return value || defaultValue!;
  }

  static getNumber(key: string, defaultValue?: number): number {
    const value = process.env[key];
    
    if (!value && defaultValue === undefined) {
      throw new EnvironmentError(key);
    }
    
    const numValue = value ? Number(value) : defaultValue;
    
    if (numValue === undefined || Number.isNaN(numValue)) {
      throw new EnvironmentError(key, 'Invalid number format');
    }
    
    return numValue;
  }

  static getBoolean(key: string, defaultValue?: boolean): boolean {
    const value = process.env[key]?.toLowerCase();
    
    if (!value && defaultValue === undefined) {
      throw new EnvironmentError(key);
    }
    
    if (!value) return defaultValue!;
    
    if (value === 'true' || value === '1') return true;
    if (value === 'false' || value === '0') return false;
    
    throw new EnvironmentError(key, 'Invalid boolean format');
  }

  static getEnum<T extends string>(key: string, validValues: T[], defaultValue?: T): T {
    const value = process.env[key] as T;
    
    if (!value && defaultValue === undefined) {
      throw new EnvironmentError(key);
    }
    
    if (!value) return defaultValue!;
    
    if (!validValues.includes(value)) {
      throw new EnvironmentError(
        key,
        `Value must be one of: ${validValues.join(', ')}`
      );
    }
    
    return value;
  }
}