import { Model, ModelCtor } from 'sequelize-typescript';
import { NotFoundException, ConflictException } from '@nestjs/common';
import { WhereOptions } from 'sequelize';

export class DatabaseUtil {
  /**
   * Checks if a record exists in the database by ID
   * @param model The Sequelize model to query
   * @param id The ID to check
   * @param entityName Optional name of the entity for error messages
   * @returns The found entity if it exists
   * @throws NotFoundException if the entity doesn't exist
   */
  static async checkEntityExists<T extends Model>(
    model: ModelCtor<T>,
    id: number | string,
    entityName: string = 'Record'
  ): Promise<T> {
    const entity = await model.findByPk(id);
    
    if (!entity) {
      throw new NotFoundException(`${entityName} with ID ${id} not found`);
    }
    
    return entity;
  }

  /**
   * Checks if a record exists without throwing an exception
   * @param model The Sequelize model to query
   * @param id The ID to check
   * @returns Boolean indicating if the entity exists
   */
  static async entityExists<T extends Model>(
    model: ModelCtor<T>,
    id: number | string
  ): Promise<boolean> {
    const entity = await model.findByPk(id);
    return !!entity;
  }

  /**
   * Checks if a record exists with specific conditions
   * @param model The Sequelize model to query
   * @param conditions The where conditions
   * @param entityName Optional name of the entity for error messages
   * @returns The found entity if it exists
   * @throws NotFoundException if the entity doesn't exist
   */
  static async checkEntityExistsWhere<T extends Model>(
    model: ModelCtor<T>,
    conditions: any,
    entityName: string = 'Record'
  ): Promise<T> {
    const entity = await model.findOne({ where: conditions });
    
    if (!entity) {
      throw new NotFoundException(`${entityName} with specified conditions not found`);
    }
    
    return entity;
  }

  /**
   * Checks if data exists in a table with specific conditions
   * @param model The Sequelize model to query
   * @param conditions The where conditions to check
   * @param throwError Whether to throw an error if data exists (for uniqueness checks)
   * @param errorMessage Custom error message if data exists
   * @returns Boolean indicating if the data exists (if throwError is false)
   * @throws ConflictException if data exists and throwError is true
   */
  static async dataExists<T extends Model>(
    model: ModelCtor<T>,
    conditions: WhereOptions<T>,
    throwError: boolean = false,
    errorMessage: string = 'Record with these conditions already exists'
  ): Promise<boolean> {
    const exists = await model.findOne({ where: conditions as any });
    
    if (exists && throwError) {
      throw new ConflictException(errorMessage);
    }
    
    return !!exists;
  }
}
