{"name": "utils", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/shared/utils/src", "projectType": "library", "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/shared/utils", "tsConfig": "libs/shared/utils/tsconfig.lib.json", "packageJson": "libs/shared/utils/package.json", "main": "libs/shared/utils/src/index.ts", "assets": ["libs/shared/utils/*.md"]}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/shared/utils/**/*.ts"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/shared/utils/jest.config.ts"}}}, "tags": []}