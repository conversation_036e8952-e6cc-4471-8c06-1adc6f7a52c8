{"name": "monitoring", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/shared/monitoring/src", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/shared/monitoring", "tsConfig": "libs/shared/monitoring/tsconfig.lib.json", "packageJson": "libs/shared/monitoring/package.json", "main": "libs/shared/monitoring/src/index.ts", "assets": ["libs/shared/monitoring/*.md"]}}}}