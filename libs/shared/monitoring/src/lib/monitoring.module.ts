import { DynamicModule, Global, Module } from '@nestjs/common';
import { MetricsService } from './services/metrics.service';
import { TracingService } from './services/tracing.service';
import { MonitoringOptions } from './interfaces/monitoring.interface';

@Global() // Make the module global
@Module({
  providers: [
    {
      provide: MetricsService,
      useValue: new MetricsService(undefined), // Provide a default instance
    },
    {
      provide: TracingService,
      useValue: new TracingService(undefined), // Provide a default instance
    },
  ],
  exports: [MetricsService, TracingService],
})
export class MonitoringModule {
  static forRoot(options: MonitoringOptions): DynamicModule {
    return {
      module: MonitoringModule,
      providers: [
        {
          provide: 'MONITORING_OPTIONS',
          useValue: options,
        },
        {
          provide: MetricsService,
          useFactory: () => new MetricsService(options?.metrics),
        },
        {
          provide: TracingService,
          useFactory: () => new TracingService(options?.tracing),
        },
      ],
      exports: [MetricsService, TracingService],
    };
  }
}
