import { Injectable } from '@nestjs/common';
import * as promClient from 'prom-client';
import { MetricsOptions } from '../interfaces/monitoring.interface';

@Injectable()
export class MetricsService {
  contentType(arg0: string, contentType: any) {
    throw new Error('Method not implemented.');
  }
  private registry: promClient.Registry;
  private counters: Map<string, promClient.Counter>;
  private histograms: Map<string, promClient.Histogram>;
  private gauges: Map<string, promClient.Gauge>;
  private defaultLabels: Record<string, string>;
  private prefix: string;

  constructor(private options?: MetricsOptions) {
    this.registry = new promClient.Registry();
    this.counters = new Map();
    this.histograms = new Map();
    this.gauges = new Map();

    // Handle the case when options are undefined
    const safeOptions = options || {
      serviceName: 'unknown',
      serviceVersion: 'unknown',
      labels: {},
    };

    this.defaultLabels = {
      service: safeOptions.serviceName || 'unknown',
      version: safeOptions.serviceVersion || 'unknown',
      ...safeOptions.labels,
    };

    // Create a valid prefix from service name
    this.prefix = this.sanitizeMetricName(safeOptions.serviceName || 'unknown');

    this.registry.setDefaultLabels(this.defaultLabels);

    // Use sanitized prefix for default metrics
    promClient.collectDefaultMetrics({
      register: this.registry,
      prefix: `${this.prefix}_`,
    });
  }

  createCounter(name: string, help: string, labelNames: string[] = []) {
    const metricName = `${this.prefix}_${this.sanitizeMetricName(name)}`;
    const counter = new promClient.Counter({
      name: metricName,
      help,
      labelNames,
      registers: [this.registry],
    });
    this.counters.set(name, counter);
    return counter;
  }

  createHistogram(
    name: string,
    help: string,
    labelNames: string[] = [],
    buckets?: number[]
  ) {
    const metricName = `${this.prefix}_${this.sanitizeMetricName(name)}`;
    const histogram = new promClient.Histogram({
      name: metricName,
      help,
      labelNames,
      buckets: buckets || promClient.linearBuckets(0.1, 0.1, 10),
      registers: [this.registry],
    });
    this.histograms.set(name, histogram);
    return histogram;
  }

  createGauge(name: string, help: string, labelNames: string[] = []) {
    const metricName = `${this.prefix}_${this.sanitizeMetricName(name)}`;
    const gauge = new promClient.Gauge({
      name: metricName,
      help,
      labelNames,
      registers: [this.registry],
    });
    this.gauges.set(name, gauge);
    return gauge;
  }

  incrementCounter(name: string, labels?: Record<string, string>) {
    const counter = this.counters.get(name);
    if (counter) {
      counter.inc(labels);
    }
  }

  observeHistogram(
    name: string,
    value: number,
    labels?: Record<string, string>
  ) {
    const histogram = this.histograms.get(name);
    if (histogram) {
      if (labels) {
        histogram.labels(labels).observe(value);
      } else {
        histogram.observe(value);
      }
    }
  }

  async getMetrics(): Promise<string> {
    return this.registry.metrics();
  }

  private sanitizeMetricName(name: string): string {
    // Prometheus metric names must match the regex [a-zA-Z_:][a-zA-Z0-9_:]*
    let sanitized = name
      .toLowerCase()
      .replace(/[^a-z0-9_]/g, '_')
      .replace(/^[^a-z_]/, '_$&');

    // Avoid duplicate underscores
    sanitized = sanitized.replace(/_+/g, '_');

    // Trim leading/trailing underscores
    sanitized = sanitized.replace(/^_+|_+$/g, '');

    return sanitized;
  }
}
