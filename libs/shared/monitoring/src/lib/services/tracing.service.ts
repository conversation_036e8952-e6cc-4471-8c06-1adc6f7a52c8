import { Injectable } from '@nestjs/common';
import { NodeSDK } from '@opentelemetry/sdk-node';
import { OTLPTraceExporter } from '@opentelemetry/exporter-trace-otlp-http';
import { GrpcInstrumentation } from '@opentelemetry/instrumentation-grpc';
import { Resource } from '@opentelemetry/resources';
import { SemanticResourceAttributes } from '@opentelemetry/semantic-conventions';
import { TracingOptions } from '../interfaces/monitoring.interface';

@Injectable()
export class TracingService {
  private sdk: NodeSDK;

  constructor(options?: TracingOptions) {
    // Handle the case when options are undefined
    const safeOptions = options || {
      serviceName: 'unknown',
      jaegerEndpoint: 'http://localhost:4318/v1/traces',
    };

    const exporter = new OTLPTraceExporter({
      url: safeOptions.jaegerEndpoint || 'http://localhost:4318/v1/traces',
    });

    this.sdk = new NodeSDK({
      resource: new Resource({
        [SemanticResourceAttributes.SERVICE_NAME]: safeOptions.serviceName,
      }),
      traceExporter: exporter,
      instrumentations: [
        new GrpcInstrumentation({
          enabled: true,
        }),
      ],
    });
  }

  async start(): Promise<void> {
    await this.sdk.start();
  }

  async shutdown(): Promise<void> {
    await this.sdk.shutdown();
  }
}
