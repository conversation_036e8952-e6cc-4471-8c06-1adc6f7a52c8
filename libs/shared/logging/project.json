{"name": "logging", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/shared/logging/src", "projectType": "library", "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/shared/logging", "main": "libs/shared/logging/src/index.ts", "tsConfig": "libs/shared/logging/tsconfig.lib.json", "assets": ["libs/shared/logging/*.md"]}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}}, "tags": []}