export enum Modules {
  DASHBOARD_ACCESS = 'DashboardAccess',
  USER_MANAGEMENT = 'UserManagement',
  LEADS_MANAGEMENT = 'LeadsManagement',
  APPLICATION_MANAGEMENT = 'ApplicationManagement',
  FINANCIAL_TRANSACTIONS = 'FinancialTransactions',
  SYSTEM_SETTINGS = 'SystemSettings',
  REPORTS_AND_ANALYTICS = 'ReportsandAnalytics',
  AGENCY_MANAGEMENT = 'AgencyManagement',
  UNIVERSITY_MANAGEMENT = 'UniversityManagement',
  STUDENT_PROFILE_MANAGEMENT = 'StudentProfileManagement',
  SYSTEM_ADMINS = 'SystemAdmins',
  TASK_MANAGEMENT = 'TaskManagement',
}

export enum SubModules {
  OVERVIEW_OF_KEY_METRICS = 'Overviewofkeymetrics',
  QUICK_LINKS_TO_IMPORTANT_ACTIONS = 'Quicklinkstoimportantactions',
  NOTIFICATIONS_AND_REMINDERS = 'Notificationsandreminders',
  CREATE = 'Create',
  DELETE = 'Delete',
  DEACTIVATE = 'Deactivate',
  VIEW = 'View',
  VIEW_ACTIVITY = 'ViewActivity',
  EDIT = 'Edit',
  FOLLOW_UP_TRACKING = 'Follow-upTracking',
  ASSIGN_LEADS_TO_USER = 'Assignleadestouser',
  LEAD_CONVERSION_TO_APPLICATION = 'leadConversiontoapplication',
  TRACK_APPLICATION_STATUS = 'Trackapplicationstatus',
  TRACK_PAYMENTS = 'Trackpayments',
  EMPLOYEE_RECORDS = 'Employeerecords',
  EMPLOYEE_MANAGEMENT = 'Employeemanagement',
  ATTENDANCE_TRACKING = 'Attendancetracking',
  LEAVE_MANAGEMENT = 'Leavemanagement',
  ROLE_ASSIGNMENTS = 'Roleassignments',
  CRM_CONFIGURATION = 'CRMconfiguration',
  ROLE_AND_PERMISSION_SETUP = 'Roleandpermissionsetup',
  NOTIFICATION_PREFERENCES = 'Notificationpreferences',
  BACKUP_AND_SECURITY_SETTINGS = 'Backupandsecuritysettings',
  GENERATE_REPORTS_BY_MODULE = 'Generatereportsbymodule(Leads,Applications,Finance,etc.)',
  EXPORT_TO_PDF_EXCEL = 'ExporttoPDF/Excel',
  DASHBOARD_WIDGETS_WITH_FILTERS = 'Dashboardwidgetswithfilters',
  ONBOARD_NEW_AGENCY_PARTNERS = 'Onboardnewagencypartners',
  TRACK_AGENCY_PERFORMANCE = 'Trackagencyperformance',
  VIEW_APPLICATIONS_SUBMITTED_BY_AGENCIES = 'Viewapplicationssubmittedbyagencies',
  SET_COMMISSION_RATES = 'Setcommissionrates',
  ADD_UPDATE_UNIVERSITY_PROFILES = 'Add/updateuniversityprofiles',
  MANAGE_UNIVERSITY_REQUIREMENTS = 'Manageuniversityrequirements',
  APPLICATION_INTAKE_CALENDARS = 'Applicationintakecalendars',
  COURSE_LIST_AND_PROGRAM_INFO = 'Courselistandprograminfo',
  REQUEST_UNIVERSITY_FOR_ONBOARDING = 'RequestUniversityforOnboarding',
  COMMUNICATION_LOGS = 'Communicationlogs',
  SUBMIT_SUPPORT_TICKETS = 'Submitsupporttickets',
  IT_ACTIVITY_LOG = 'ITactivitylog',
  SYSTEM_STATUS_CHECKS = 'Systemstatuschecks',
  MAINTENANCE_AND_SETUP = 'MaintenanceandSetup',
  TRACK_TASK_STATUS_AND_PROGRESS = 'Tracktaskstatusandprogress',
}

export enum Actions {
  ASSIGN_ROLE_AND_PERMISSIONS = 'Assign role and permissions',
  ASSIGN_A_TEAM = 'Assign a Team',
  DOCUMENTS_UPLOAD = 'Documents Upload',
  DOCUMENTS_DOWNLOAD = 'Documents Download',
  DOCUMENTS_DELETE = 'Documents Delete',
  DOCUMENTS_VIEW = 'Documents View',
  DOCUMENTS_EDIT = 'Documents Edit',
  ESL_START_DATE_CHANGE = 'ESL Start Date (Change)',
  INTAKE_CHANGE = 'Intake (Change)',
  DOCUMENTS_MAIN_COPY_CHECK_UNCHECK = 'Documents Main Copy (Check/Uncheck)',
  DOCUEMENTS_REVIEW_APPROVE_REJECT = 'Docuements Review (Approve/Reject)',
  DOCUMENTS_RESUBMISSION = 'Documents Resubmission',
  NOTES_AGAINST_APPLICATIONS = 'Notes Against Applications',
  MANAGE_REFUNDS_AND_DISCOUNTS = 'Manage refunds and discounts',
  FINANCIAL_REPORTING = 'Financial reporting',
  SEND_INVOICES = 'Send Invoices',
  STORE_STUDENT_PERSONAL_INFO = 'Store student personal info',
  EDUCATION_HISTORY = 'Education history',
  ASSIGN_TASK = 'Assign Task',
  SET_DEADLINES_AND_PRIORITIES = 'Set deadlines and priorities',
  LINK_TASKS_TO_STUDENTS_LEADS_OR_APPLICATIONS = 'Link tasks to students, leads, or applications',
}

export enum UserPermissions {
  DA_OVERVIEW_OF_METRICS = 'DashboardAccess:Overviewofkeymetrics',
  DA_QUICK_LINKS_TO_IMPORTANT_ACTIONS = 'DashboardAccess:Quicklinkstoimportantactions',
  DA_NOTIFICATIONS_AND_REMINDERS = 'DashboardAccess:Notificationsandreminders',
  TM_CREATE = 'TaskManagement:Create',
  TM_DELETE = 'TaskManagement:Delete',
  UM_DEACTIVATE = 'UsersManagement:Deactivate',
  SPM_VIEW = 'StudentProfileManagement:View',
  UM_VIEW_ACTIVITY = 'UsersManagement:ViewActivity',
  TM_EDIT = 'TaskManagement:Edit',
  AM_TRACK_APPLICATION_STATUS = 'ApplicationManagement:Trackapplicationstatus',
  HR_EMPLOYEE_RECORDS = 'HRfeatureAccess:Employeerecords',
  HR_ATTENDANCE_TRACKING = 'HRfeatureAccess:Attendancetracking',
  HR_LEAVE_MANAGEMENT = 'HRfeatureAccess:Leavemanagement',
  HR_ROLE_ASSIGNMENTS = 'HRfeatureAccess:Roleassignments',
  SS_CRM_CONFIGURATION = 'SystemSettings:CRMconfiguration',
  SS_ROLE_AND_PERMISSION_SETUP = 'SystemSettings:Roleandpermissionsetup',
  SS_EMPLOYEE_MANAGEMENT = 'SystemSettings:Employeemanagement',
  SS_NOTIFICATION_PREFERENCES = 'SystemSettings:Notificationpreferences',
  SS_BACKUP_AND_SECURITY_SETTINGS = 'SystemSettings:Backupandsecuritysettings',
  RA_GENERATE_REPORTS_BY_FEATURE = 'Reports&Analytics:Generatereportsbyfeature(Leads,Applications,Finance,etc.)',
  RA_EXPORT_TO_PDF_EXCEL = 'Reports&Analytics:ExporttoPDF/Excel',
  RA_DASHBOARD_WIDGETS_WITH_FILTERS = 'Reports&Analytics:Dashboardwidgetswithfilters',
  AM_ONBOARD_NEW_AGENCY_PARTNERS = 'AgencyManagement:Onboardnewagencypartners',
  AM_TRACK_AGENCY_PERFORMANCE = 'AgencyManagement:Trackagencyperformance',
  AM_VIEW_APPLICATIONS_SUBMITTED_BY_AGENCIES = 'AgencyManagement:Viewapplicationssubmittedbyagencies',
  AM_SET_COMMISSION_RATES = 'AgencyManagement:Setcommissionrates',
  UM_ADD_UPDATE_UNIVERSITY_PROFILES = 'UniversityManagement:Add/updateuniversityprofiles',
  UM_MANAGE_UNIVERSITY_REQUIREMENTS = 'UniversityManagement:Manageuniversityrequirements',
  UM_APPLICATION_INTAKE_CALENDARS = 'UniversityManagement:Applicationintakecalendars',
  UM_COURSE_LIST_AND_PROGRAM_INFO = 'UniversityManagement:Courselistandprograminfo',
  SPM_COMMUNICATION_LOGS = 'StudentProfileManagement:Communicationlogs',
  SMITSA_SUBMIT_SUPPORT_TICKETS = 'Support&Maintenance/IT/SystemAdmins:Submitsupporttickets',
  SMITSA_IT_ACTIVITY_LOG = 'Support&Maintenance/IT/SystemAdmins:ITactivitylog',
  SMITSA_SYSTEM_STATUS_CHECKS = 'Support&Maintenance/IT/SystemAdmins:Systemstatuschecks',
  SMITSA_MAINTENANCE_AND_SETUP = 'Support&Maintenance/IT/SystemAdmins:MaintenanceandSetup',
}
