import { Inject, Injectable, Logger, OnModuleInit } from '@nestjs/common';
import {
  S3Client,
  PutObjectCommand,
  HeadBucketCommand,
  CreateBucketCommand,
  PutBucketPolicyCommand,
} from '@aws-sdk/client-s3';
import {
  UploadMetadata,
  StorageProvider,
} from '../interfaces/storage-provider.interface';

@Injectable()
export class S3StorageProvider implements StorageProvider, OnModuleInit {
  private s3: S3Client;
  private bucket: string;
  private publicEndpoint: string;

  constructor(@Inject('UPLOAD_MODULE_CONFIG') private readonly config: any) {
    const s3 = config?.s3 || {};
    this.bucket = s3.bucket;
    this.publicEndpoint = s3.publicEndpoint || s3.endpoint;

    this.s3 = new S3Client({
      region: s3.region,
      endpoint: s3.endpoint,
      credentials: {
        accessKeyId: s3.accessKey,
        secretAccessKey: s3.secretKey,
      },
      forcePathStyle: s3.forcePathStyle,
    });
  }

  async onModuleInit() {
    try {
      await this.s3.send(new HeadBucketCommand({ Bucket: this.bucket }));
    } catch {
      await this.s3.send(new CreateBucketCommand({ Bucket: this.bucket }));
    }

    // ✅ Apply anonymous read-only bucket policy
    const policy = {
      Version: '2012-10-17',
      Statement: [
        {
          Sid: 'PublicReadGetObject',
          Effect: 'Allow',
          Principal: '*', // Anonymous
          Action: ['s3:GetObject'],
          Resource: [`arn:aws:s3:::${this.bucket}/*`], // Allow object access
        },
      ],
    };

    await this.s3.send(
      new PutBucketPolicyCommand({
        Bucket: this.bucket,
        Policy: JSON.stringify(policy),
      })
    );
  }

  async upload(
    buffer: Buffer,
    key: string,
    metadata: UploadMetadata
  ): Promise<string> {
    await this.s3.send(
      new PutObjectCommand({
        Bucket: this.bucket,
        Key: key,
        Body: buffer,
        ContentType: metadata.contentType,
      })
    );

    return `${this.publicEndpoint}/${this.bucket}/${key}`;
  }
}
