import {
  Injectable,
  Inject,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { Request } from 'express';
import { v4 as uuid } from 'uuid';
import { extname } from 'path';
import {
  StorageProvider,
  UploadMetadata,
} from './interfaces/storage-provider.interface';
import { MulterFile } from './file-upload.types';
import sharp = require('sharp');

@Injectable()
export class UploadService {
  constructor(
    @Inject('StorageProvider')
    private readonly storage: StorageProvider
  ) {}

  private async resizeImage(buffer: Buffer): Promise<Buffer> {
    let resized = buffer;
    let quality = 90;

    while (resized.length > 2 * 1024 * 1024 && quality > 10) {
      resized = await sharp(buffer).jpeg({ quality }).toBuffer();
      quality -= 10;
    }

    if (resized.length > 2 * 1024 * 1024) {
      throw new BadRequestException('Image too large to resize under 2MB');
    }

    return resized;
  }

  async uploadFile(
    file: MulterFile,
    type: 'image' | 'file'
  ): Promise<{ url: string }> {
    const ext = extname(file.originalname);
    const key = `${type}s/${uuid()}${ext}`;

    let buffer = file.buffer;
    if (type === 'image' && buffer.length > 2 * 1024 * 1024) {
      buffer = await this.resizeImage(buffer);
    }

    const metadata: UploadMetadata = {
      contentType: file.mimetype,
      originalName: file.originalname,
    };

    const url = await this.storage.upload(buffer, key, metadata);
    return { url: url };
  }
}
