{"name": "common", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/shared/common/src", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/shared/common", "tsConfig": "libs/shared/common/tsconfig.lib.json", "packageJson": "libs/shared/common/package.json", "main": "libs/shared/common/src/index.ts", "assets": ["libs/shared/common/*.md"]}}}}