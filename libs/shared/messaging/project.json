{"name": "messaging", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/shared/messaging/src", "projectType": "library", "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/shared/messaging", "tsConfig": "libs/shared/messaging/tsconfig.lib.json", "packageJson": "libs/shared/messaging/package.json", "main": "libs/shared/messaging/src/index.ts", "assets": ["libs/shared/messaging/*.md"]}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/shared/messaging/**/*.ts"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/shared/messaging/jest.config.ts"}}}, "tags": []}