export interface MessagingModuleOptions {
  /**
   * RabbitMQ connection URI
   * @example 'amqp://user:password@localhost:5672'
   */
  uri: string;

  /**
   * RabbitMQ exchange name
   * @example 'amq.default'
   */
  exchange: string;

  /**
   * Optional queue configuration
   */
  queue?: {
    name: string;
    durable?: boolean;
    arguments?: Record<string, any>;
  };

  /**
   * Optional connection options
   */
  connectionOptions?: {
    wait?: boolean;
    timeout?: number;
    retryAttempts?: number;
    retryDelay?: number;
  };
}
