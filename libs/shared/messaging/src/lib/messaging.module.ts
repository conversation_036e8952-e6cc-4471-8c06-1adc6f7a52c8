import { Module, DynamicModule, Logger } from '@nestjs/common';
import { RabbitMQModule } from '@golevelup/nestjs-rabbitmq';
import { RmqPublisherService } from './rmq-publish.service';
import { MessagingModuleOptions } from './messaging-options.interface';

@Module({})
export class MessagingModule {
  private static readonly logger = new Logger(MessagingModule.name);

  static forRoot(options: MessagingModuleOptions): DynamicModule {
    // Validate and format the RabbitMQ URI
    const uri = this.validateRabbitMqUri(options.uri);

    return {
      module: MessagingModule,
      imports: [
        RabbitMQModule.forRoot({
          exchanges: [
            {
              name: options.exchange,
              type: 'direct',
            },
          ],
          uri,
          connectionInitOptions: { wait: false },
          enableControllerDiscovery: true,
        }),
      ],
      providers: [RmqPublisherService],
      exports: [RmqPublisherService],
      global: true,
    };
  }

  private static validateRabbitMqUri(uri: string): string {
    this.logger.debug(`[MessagingModule] 🔧 Raw URI: ${uri}`);

    try {
      // 1. If it's already a valid URL, use it
      const parsed = new URL(uri);
      this.logger.debug(
        `[MessagingModule] ✅ Valid RabbitMQ URI: ${parsed.href}`
      );
      return parsed.href;
    } catch {
      try {
        // 2. Try prepending amqp:// only if needed (e.g., when uri is just user:pass@host)
        const correctedUri = `amqp://${uri.replace(/^amqp:\/\//, '')}`;
        const parsed = new URL(correctedUri);
        this.logger.debug(
          `[MessagingModule] ✅ Corrected RabbitMQ URI: ${parsed.href}`
        );
        return parsed.href;
      } catch (e: any) {
        this.logger.error(
          `[MessagingModule] ❌ Invalid RabbitMQ URI: ${e.message}`
        );
        return 'amqp://guest:guest@localhost:5672';
      }
    }
  }
}
