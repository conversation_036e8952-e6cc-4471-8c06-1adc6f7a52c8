import { Injectable, Logger } from '@nestjs/common';
import * as amqp from 'amqp-connection-manager';

export interface RmqOptions {
  queue: string;
  exchange?: string;
  routingKey?: string;
  urls?: string[];
  queueOptions?: {
    durable?: boolean;
    arguments?: Record<string, any>;
  };
}

@Injectable()
export class RmqPublisherService {
  private readonly logger = new Logger(RmqPublisherService.name);
  private channelWrapper: amqp.ChannelWrapper | null = null;
  private connection: amqp.AmqpConnectionManager | null = null;
  private initialized = false;

  async init() {
    if (this.initialized) return;

    const connectionUrl =
      process.env['RABBITMQ_URI'] ||
      'amqp://rabbitmq_user:rabbitmq_pass@localhost:5672';

    try {
      this.connection = amqp.connect([connectionUrl]);

      this.connection.on('connect', () =>
        this.logger.debug('✅ RabbitMQ connected')
      );
      this.connection.on('disconnect', (err) =>
        this.logger.error('❌ RabbitMQ disconnected', err)
      );

      this.channelWrapper = this.connection.createChannel({
        json: false,
        setup: async (channel: any) => {
          await channel.assertQueue('messaging_emails_queue', {
            durable: true,
          });
        },
      });

      await this.channelWrapper.waitForConnect();
      this.logger.debug('✅ RabbitMQ channel is ready');

      this.initialized = true;
    } catch (error) {
      this.logger.error('❌ Failed to initialize RabbitMQ publisher:', error);
    }
  }

  async sendEmail(payload: any) {
    if (!this.initialized) {
      await this.init();
    }

    if (!this.channelWrapper || !this.connection?.isConnected()) {
      throw new Error('RabbitMQ connection is not active');
    }

    const message = {
      pattern: 'send_email',
      data: payload,
    };

    await this.channelWrapper.sendToQueue(
      'messaging_emails_queue',
      Buffer.from(JSON.stringify(message)),
      {
        persistent: true,
        contentType: 'application/json',
      }
    );

    this.logger.log('📬 Email message sent to queue: messaging_emails_queue');
  }
}
