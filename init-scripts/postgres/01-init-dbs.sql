-- 01-init-dbs.sql

-- audit_db
CREATE USER audit_user WITH PASSWORD 'audit_pass';
CREATE DATABASE audit_db;
GRANT ALL PRIVILEGES ON DATABASE audit_db TO audit_user;
\connect audit_db
GRANT USAGE, CREATE ON SCHEMA public TO audit_user;
\connect postgres

-- identity_db
CREATE USER identity_user WITH PASSWORD 'identity_pass';
CREATE DATABASE identity_db;
GRANT ALL PRIVILEGES ON DATABASE identity_db TO identity_user;
\connect identity_db
GRANT USAGE, CREATE ON SCHEMA public TO identity_user;
\connect postgres

-- payment_db
CREATE USER payment_user WITH PASSWORD 'payment_pass';
CREATE DATABASE payment_db;
GRANT ALL PRIVILEGES ON DATABASE payment_db TO payment_user;
\connect payment_db
GRANT USAGE, CREATE ON SCHEMA public TO payment_user;
\connect postgres

-- agency_db
CREATE USER agency_user WITH PASSWORD 'agency_pass';
CREATE DATABASE agency_db;
GRA<PERSON> ALL PRIVILEGES ON DATABASE agency_db TO agency_user;
\connect agency_db
GRANT USAGE, CREATE ON SCHEMA public TO agency_user;
\connect postgres

-- analytics_db
CREATE USER analytics_user WITH PASSWORD 'analytics_pass';
CREATE DATABASE analytics_db;
GRANT ALL PRIVILEGES ON DATABASE analytics_db TO analytics_user;
\connect analytics_db
GRANT USAGE, CREATE ON SCHEMA public TO analytics_user;
\connect postgres

-- auth_db
CREATE USER auth_user WITH PASSWORD 'auth_pass';
CREATE DATABASE auth_db;
GRANT ALL PRIVILEGES ON DATABASE auth_db TO auth_user;
\connect auth_db
GRANT USAGE, CREATE ON SCHEMA public TO auth_user;
\connect postgres

-- events_db
CREATE USER events_user WITH PASSWORD 'events_pass';
CREATE DATABASE events_db;
GRANT ALL PRIVILEGES ON DATABASE events_db TO events_user;
\connect events_db
GRANT USAGE, CREATE ON SCHEMA public TO events_user;
\connect postgres

-- help_db
CREATE USER help_user WITH PASSWORD 'help_pass';
CREATE DATABASE help_db;
GRANT ALL PRIVILEGES ON DATABASE help_db TO help_user;
\connect help_db
GRANT USAGE, CREATE ON SCHEMA public TO help_user;
\connect postgres

-- settings_db
CREATE USER settings_user WITH PASSWORD 'settings_pass';
CREATE DATABASE settings_db;
GRANT ALL PRIVILEGES ON DATABASE settings_db TO settings_user;
\connect settings_db
GRANT USAGE, CREATE ON SCHEMA public TO settings_user;
\connect postgres

-- students_db
CREATE USER students_user WITH PASSWORD 'students_pass';
CREATE DATABASE students_db;
GRANT ALL PRIVILEGES ON DATABASE students_db TO students_user;
\connect students_db
GRANT USAGE, CREATE ON SCHEMA public TO students_user;
\connect postgres

-- university_db
CREATE USER university_user WITH PASSWORD 'university_pass';
CREATE DATABASE university_db;
GRANT ALL PRIVILEGES ON DATABASE university_db TO university_user;
\connect university_db
GRANT USAGE, CREATE ON SCHEMA public TO university_user;
\connect postgres
