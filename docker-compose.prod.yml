version: '3.8'

services:
  ###################
  # Databases & Infrastructure
  ###################
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-postgres123}
    ports:
      - "5432:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./init-scripts/postgres:/docker-entrypoint-initdb.d:ro
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5
      start_period: 10s
    deploy:
      resources:
        limits:
          memory: 1G
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis123}
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          memory: 512M
    restart: unless-stopped
      
  rabbitmq:
    image: rabbitmq:3-management-alpine
    environment:
      RABBITMQ_DEFAULT_USER: ${RABBITMQ_USER:-rabbitmq_user}
      RABBITMQ_DEFAULT_PASS: ${RABBITMQ_PASS:-rabbitmq_pass}
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq-data:/var/lib/rabbitmq
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "-q", "ping"]
      interval: 5s
      timeout: 5s
      retries: 5
      start_period: 10s
    deploy:
      resources:
        limits:
          memory: 512M
    restart: unless-stopped

  mongodb-chat:
    image: mongo:6-jammy
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_USER:-chat_user}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_PASS:-chat_pass}
      MONGO_INITDB_DATABASE: chat_db
    volumes:
      - mongodb-chat-data:/data/db
    healthcheck:
      test: echo 'db.runCommand("ping").ok' | mongosh localhost:27017/test --quiet
      interval: 5s
      timeout: 5s
      retries: 5
      start_period: 10s
    deploy:
      resources:
        limits:
          memory: 1G
    restart: unless-stopped

  ###################
  # Core Services
  ###################
  audit-logging:
    build:
      dockerfile: apps/core/audit-logging/Dockerfile
    volumes: []

  identity-service:
    build:
      dockerfile: apps/core/identity-service/Dockerfile
    volumes: []

  payment-service:
    build:
      dockerfile: apps/core/payment-service/Dockerfile
    volumes: []

  ###################
  # API Gateways
  ###################
  agency-apigw:
    build:
      dockerfile: apps/gateways/agency-apigw/Dockerfile
    volumes: []

  manager-apigw:
    build:
      dockerfile: apps/gateways/manager-apigw/Dockerfile
    volumes: []

  student-apigw:
    build:
      dockerfile: apps/gateways/student-apigw/Dockerfile
    volumes: []

  super-admin-apigw:
    build:
      dockerfile: apps/gateways/super-admin-apigw/Dockerfile
    volumes: []

  university-apigw:
    build:
      dockerfile: apps/gateways/university-apigw/Dockerfile
    volumes: []

  ###################
  # Domain Services
  ###################
  agency-service:
    build:
      dockerfile: apps/services/agency-service/Dockerfile
    volumes: []

  auth-service:
    build:
      dockerfile: apps/services/auth-service/Dockerfile
    volumes: []

  events-meetings-service:
    build:
      dockerfile: apps/services/events-meetings-service/Dockerfile
    volumes: []

  help-service:
    build:
      dockerfile: apps/services/help-service/Dockerfile
    volumes: []

  messaging-service:
    build:
      dockerfile: apps/services/messaging-service/Dockerfile
    volumes: []

  settings-service:
    build:
      dockerfile: apps/services/settings-service/Dockerfile
    volumes: []

  students-service:
    build:
      dockerfile: apps/services/students-service/Dockerfile
    volumes: []

volumes:
  postgres-data:
  mongodb-chat-data:
  redis-data:
  rabbitmq-data:

