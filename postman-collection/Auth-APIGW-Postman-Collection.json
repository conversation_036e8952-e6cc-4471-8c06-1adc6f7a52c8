{"info": {"name": "Auth-APIGW Complete API Collection", "description": "Complete collection of all Auth-APIGW endpoints with authentication, role management, departments, employees, and password reset functionality", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:4006/api/auth", "type": "string"}, {"key": "auth_token", "value": "", "type": "string"}, {"key": "refresh_token", "value": "", "type": "string"}, {"key": "reset_token", "value": "", "type": "string"}], "item": [{"name": "🔐 Authentication", "item": [{"name": "User Registration", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"nationality\": \"US\",\n  \"organizationName\": \"Example Corp\",\n  \"password\": \"SecurePass123!\",\n  \"phone\": \"+1234567890\",\n  \"roleName\": \"Employee\",\n  \"departmentName\": \"Engineering\"\n}"}, "url": {"raw": "{{base_url}}/register", "host": ["{{base_url}}"], "path": ["register"]}, "description": "Register a new user with organization, role, and department"}}, {"name": "User Login", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.accessToken) {", "        pm.collectionVariables.set('auth_token', response.accessToken);", "    }", "    if (response.refreshToken) {", "        pm.collectionVariables.set('refresh_token', response.refreshToken);", "    }", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"SuperAdmin123!\",\n  \"ipAddress\": \"127.0.0.1\"\n}"}, "url": {"raw": "{{base_url}}/login", "host": ["{{base_url}}"], "path": ["login"]}, "description": "Login user and get access token"}}, {"name": "Token Validation", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/validate", "host": ["{{base_url}}"], "path": ["validate"]}, "description": "Validate current access token"}}, {"name": "User <PERSON>", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/logout", "host": ["{{base_url}}"], "path": ["logout"]}, "description": "Logout user and invalidate token"}}, {"name": "Refresh <PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.accessToken) {", "        pm.collectionVariables.set('auth_token', response.accessToken);", "    }", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"refreshToken\": \"{{refresh_token}}\"\n}"}, "url": {"raw": "{{base_url}}/refresh", "host": ["{{base_url}}"], "path": ["refresh"]}, "description": "Refresh access token using refresh token"}}]}, {"name": "🔑 Password Reset", "item": [{"name": "Forgot Password", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.resetToken) {", "        pm.collectionVariables.set('reset_token', response.resetToken);", "    }", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{base_url}}/forgot-password", "host": ["{{base_url}}"], "path": ["forgot-password"]}, "description": "Request password reset for user email"}}, {"name": "Verify Reset To<PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"resetToken\": \"{{reset_token}}\"\n}"}, "url": {"raw": "{{base_url}}/verify-reset-token", "host": ["{{base_url}}"], "path": ["verify-reset-token"]}, "description": "Verify if reset token is valid"}}, {"name": "Reset Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"resetToken\": \"{{reset_token}}\",\n  \"newPassword\": \"NewSecurePass123!\",\n  \"confirmPassword\": \"NewSecurePass123!\"\n}"}, "url": {"raw": "{{base_url}}/reset-password", "host": ["{{base_url}}"], "path": ["reset-password"]}, "description": "Reset user password with valid token"}}]}, {"name": "🔐 OTP Authentication", "item": [{"name": "Generate OTP", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"type\": \"login\"\n}"}, "url": {"raw": "{{base_url}}/generate-otp", "host": ["{{base_url}}"], "path": ["generate-otp"]}, "description": "Generate OTP for user authentication"}}, {"name": "Verify OTP", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"otp\": \"123456\",\n  \"type\": \"login\"\n}"}, "url": {"raw": "{{base_url}}/verify-otp", "host": ["{{base_url}}"], "path": ["verify-otp"]}, "description": "Verify OTP for user authentication"}}, {"name": "SSO Authentication", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"provider\": \"google\",\n  \"token\": \"google_oauth_token\",\n  \"email\": \"<EMAIL>\",\n  \"name\": \"User Name\"\n}"}, "url": {"raw": "{{base_url}}/sso", "host": ["{{base_url}}"], "path": ["sso"]}, "description": "SSO authentication with Google"}}]}, {"name": "👥 Role Management", "item": [{"name": "Create Role with Details", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Manager\",\n  \"description\": \"Manager role with specific permissions\",\n  \"organizationId\": 1,\n  \"permissions\": [\n    {\"moduleId\": 1, \"featureId\": 1, \"isAllowed\": true}\n  ]\n}"}, "url": {"raw": "{{base_url}}/role", "host": ["{{base_url}}"], "path": ["role"]}, "description": "Create a new role with permissions"}}, {"name": "Get Role Details by Name", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/role-details/SuperAdmin", "host": ["{{base_url}}"], "path": ["role-details", "SuperAdmin"]}, "description": "Get detailed information about a specific role"}}, {"name": "Get Roles with Details", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/roles", "host": ["{{base_url}}"], "path": ["roles"]}, "description": "Get all roles with their permissions and users"}}, {"name": "List Roles", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/roles?organizationId=1&includeSystemRoles=true&page=1&limit=10", "host": ["{{base_url}}"], "path": ["roles"], "query": [{"key": "organizationId", "value": "1"}, {"key": "includeSystemRoles", "value": "true"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}, "description": "List roles with pagination and filters"}}, {"name": "Get Role by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/roles/1", "host": ["{{base_url}}"], "path": ["roles", "1"]}, "description": "Get specific role by ID"}}, {"name": "Update Role", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Manager\",\n  \"description\": \"Updated manager role description\"\n}"}, "url": {"raw": "{{base_url}}/roles/1", "host": ["{{base_url}}"], "path": ["roles", "1"]}, "description": "Update role information"}}, {"name": "<PERSON><PERSON>", "request": {"method": "PUT", "header": [], "url": {"raw": "{{base_url}}/role/1/NewRoleName", "host": ["{{base_url}}"], "path": ["role", "1", "NewRoleName"]}, "description": "<PERSON><PERSON> a role"}}, {"name": "Delete Role", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/role/1", "host": ["{{base_url}}"], "path": ["role", "1"]}, "description": "Delete a role"}}, {"name": "Delete Organization Role", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/roles/1", "host": ["{{base_url}}"], "path": ["roles", "1"]}, "description": "Delete an organization role"}}]}, {"name": "📦 Module Management", "item": [{"name": "Create Mo<PERSON>le", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"New Module\",\n  \"description\": \"Module description\",\n  \"features\": [\n    {\n      \"name\": \"Feature 1\",\n      \"subFeatures\": [\n        {\"name\": \"Sub Feature 1\"}\n      ]\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/modules", "host": ["{{base_url}}"], "path": ["modules"]}, "description": "Create a new module with features"}}, {"name": "Bulk Create Modules", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "[\n  {\n    \"name\": \"Module 1\",\n    \"description\": \"First module\",\n    \"features\": [\n      {\"name\": \"Feature 1\", \"subFeatures\": []}\n    ]\n  },\n  {\n    \"name\": \"Module 2\",\n    \"description\": \"Second module\",\n    \"features\": [\n      {\"name\": \"Feature 2\", \"subFeatures\": []}\n    ]\n  }\n]"}, "url": {"raw": "{{base_url}}/modules/bulk", "host": ["{{base_url}}"], "path": ["modules", "bulk"]}, "description": "Create multiple modules at once"}}, {"name": "List Modules", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/modules", "host": ["{{base_url}}"], "path": ["modules"]}, "description": "Get all modules with features and sub-features"}}]}, {"name": "🏢 Department Management", "item": [{"name": "Create Department", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "[\n  {\n    \"name\": \"Engineering\",\n    \"parent\": \"\"\n  },\n  {\n    \"name\": \"Frontend\",\n    \"parent\": \"Engineering\"\n  }\n]"}, "url": {"raw": "{{base_url}}/departments", "host": ["{{base_url}}"], "path": ["departments"]}, "description": "Create departments with hierarchy"}}, {"name": "Assign Department", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"userId\": 1,\n  \"departmentId\": 1\n}"}, "url": {"raw": "{{base_url}}/departments/assign", "host": ["{{base_url}}"], "path": ["departments", "assign"]}, "description": "Assign user to department"}}, {"name": "List Departments", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/departments?includeHierarchy=true&organizationId=1", "host": ["{{base_url}}"], "path": ["departments"], "query": [{"key": "includeHierarchy", "value": "true"}, {"key": "organizationId", "value": "1"}]}, "description": "Get all departments with parent-child hierarchy information"}}, {"name": "List Departments with Users", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/departments-with-users", "host": ["{{base_url}}"], "path": ["departments-with-users"]}, "description": "Get departments with their assigned users"}}]}, {"name": "👨‍💼 Employee Management", "item": [{"name": "Create Employee", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"phone\": \"+1234567890\",\n  \"nationality\": \"US\",\n  \"password\": \"EmployeePass123!\",\n  \"roleId\": 2,\n  \"departmentIds\": [1],\n  \"jobType\": \"Full-time\",\n  \"jobStatus\": \"Active\"\n}"}, "url": {"raw": "{{base_url}}/employees", "host": ["{{base_url}}"], "path": ["employees"]}, "description": "Create a new employee"}}, {"name": "Get Employee by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/employees/1", "host": ["{{base_url}}"], "path": ["employees", "1"]}, "description": "Get employee details by ID"}}, {"name": "List Employees", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/employees?page=1&limit=10&search=john&jobType=Full-time&jobStatus=Active", "host": ["{{base_url}}"], "path": ["employees"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "search", "value": "john"}, {"key": "jobType", "value": "Full-time"}, {"key": "jobStatus", "value": "Active"}]}, "description": "List employees with filters and pagination"}}, {"name": "Update Employee", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON> Employee\",\n  \"phone\": \"+1234567891\",\n  \"jobStatus\": \"Inactive\"\n}"}, "url": {"raw": "{{base_url}}/employees/1", "host": ["{{base_url}}"], "path": ["employees", "1"]}, "description": "Update employee information"}}, {"name": "Delete Employee", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/employees/1", "host": ["{{base_url}}"], "path": ["employees", "1"]}, "description": "Delete an employee"}}]}, {"name": "🏢 Organization Management", "item": [{"name": "Create Organization Role", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Organization Manager\",\n  \"description\": \"Manager role for specific organization\",\n  \"permissions\": [\n    {\"moduleId\": 1, \"featureId\": 1, \"isAllowed\": true}\n  ]\n}"}, "url": {"raw": "{{base_url}}/organizations/1/roles", "host": ["{{base_url}}"], "path": ["organizations", "1", "roles"]}, "description": "Create role for specific organization"}}, {"name": "Get Organization Roles", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/organizations/1/roles?includeSystemRoles=true", "host": ["{{base_url}}"], "path": ["organizations", "1", "roles"], "query": [{"key": "includeSystemRoles", "value": "true"}]}, "description": "Get all roles for specific organization"}}, {"name": "Create Organization Department", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Organization Engineering\",\n  \"parentId\": null\n}"}, "url": {"raw": "{{base_url}}/organizations/1/departments", "host": ["{{base_url}}"], "path": ["organizations", "1", "departments"]}, "description": "Create department for specific organization"}}, {"name": "Get Organization Departments", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/organizations/1/departments?includeHierarchy=true", "host": ["{{base_url}}"], "path": ["organizations", "1", "departments"], "query": [{"key": "includeHierarchy", "value": "true"}]}, "description": "Get departments for specific organization"}}, {"name": "List Organization Departments", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/org-departments?organizationId=1&parentId=1&includeChildren=true&page=1&limit=10", "host": ["{{base_url}}"], "path": ["org-departments"], "query": [{"key": "organizationId", "value": "1"}, {"key": "parentId", "value": "1"}, {"key": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "true"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}, "description": "List organization departments with pagination"}}, {"name": "Get Organization Department by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/org-departments/1", "host": ["{{base_url}}"], "path": ["org-departments", "1"]}, "description": "Get specific organization department"}}, {"name": "Update Organization Department", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Engineering Department\",\n  \"parentId\": 2\n}"}, "url": {"raw": "{{base_url}}/org-departments/1", "host": ["{{base_url}}"], "path": ["org-departments", "1"]}, "description": "Update organization department"}}, {"name": "Delete Organization Department", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/org-departments/1", "host": ["{{base_url}}"], "path": ["org-departments", "1"]}, "description": "Delete organization department"}}]}]}