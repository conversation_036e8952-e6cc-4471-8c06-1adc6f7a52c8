# PowerShell script to initialize multiple PostgreSQL databases and users
# Requires: psql to be available in PATH

$env:PGUSER = $env:POSTGRES_USER
$MaxRetries = 30
$DelaySeconds = 2

# Wait for PostgreSQL to be ready
Write-Host "⏳ Waiting for PostgreSQL to be ready..."
$ready = $false
for ($i = 0; $i -lt $MaxRetries; $i++) {
    try {
        & pg_isready -U $env:PGUSER > $null
        if ($LASTEXITCODE -eq 0) {
            $ready = $true
            break
        }
    } catch {}
    Write-Host "PostgreSQL is unavailable - sleeping..."
    Start-Sleep -Seconds $DelaySeconds
}

if (-not $ready) {
    Write-Error "❌ PostgreSQL did not become ready in time."
    exit 1
}

Write-Host "✅ PostgreSQL is up - executing database initialization"

# Function to create database and user
function Create-DbAndUser {
    param (
        [string]$DbName,
        [string]$UserName,
        [string]$Password
    )

    $sql = @"
DO \$\$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_user WHERE usename = '$UserName') THEN
        CREATE USER $UserName WITH PASSWORD '$Password';
    END IF;

    IF NOT EXISTS (SELECT FROM pg_database WHERE datname = '$DbName') THEN
        CREATE DATABASE $DbName;
    END IF;
END
\$\$;

GRANT ALL PRIVILEGES ON DATABASE $DbName TO $UserName;
"@

    & psql -v ON_ERROR_STOP=1 -U $env:PGUSER -c $sql
    if ($LASTEXITCODE -ne 0) {
        Write-Error "❌ Failed to create $DbName or $UserName"
        exit 1
    } else {
        Write-Host "✅ Created $DbName and granted privileges to $UserName"
    }
}

# List of DBs and users
$databases = @(
    @{ db = "audit_db"; user = "audit_user"; pass = "audit_pass" },
    @{ db = "identity_db"; user = "identity_user"; pass = "identity_pass" },
    @{ db = "payment_db"; user = "payment_user"; pass = "payment_pass" },
    @{ db = "agency_db"; user = "agency_user"; pass = "agency_pass" },
    @{ db = "analytics_db"; user = "analytics_user"; pass = "analytics_pass" },
    @{ db = "auth_db"; user = "auth_user"; pass = "auth_pass" },
    @{ db = "events_db"; user = "events_user"; pass = "events_pass" },
    @{ db = "help_db"; user = "help_user"; pass = "help_pass" },
    @{ db = "settings_db"; user = "settings_user"; pass = "settings_pass" },
    @{ db = "students_db"; user = "students_user"; pass = "students_pass" },
    @{ db = "university_db"; user = "university_user"; pass = "university_pass" }
)
