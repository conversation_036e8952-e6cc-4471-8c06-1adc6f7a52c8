# ApplyGoal Backend Services Guide

This document provides information on how to run different services in the ApplyGoal backend application.

## Service Organization

The ApplyGoal backend is organized into three main categories of services:

1. **Core Services** - Fundamental services that provide core functionality
2. **API Gateways** - Entry points for different user types
3. **Domain Services** - Business logic services for specific domains

## Port Assignments

To avoid port conflicts, each service has been assigned a specific port:

- Main application: 3000
- Core services: 3001-3099
- API Gateways: 4001-4099
- Domain services: 5001-5099

The specific port assignments can be found in `scripts/service-ports.js`.

## Running Services

### Individual Services

You can run individual services using the following npm scripts:

```bash
# Core Services
npm run start:core:audit       # Audit Logging Service (port 3001)
npm run start:core:identity    # Identity Service (port 3002)
npm run start:core:payment     # Payment Service (port 3003)

# API Gateways
npm run start:gateways:agency     # Agency API Gateway (port 4001)
npm run start:gateways:manager    # Manager API Gateway (port 4002)
npm run start:gateways:student    # Student API Gateway (port 4003)
npm run start:gateways:admin      # Super Admin API Gateway (port 4004)
npm run start:gateways:university # University API Gateway (port 4005)

# Domain Services
npm run start:services:agency     # Agency Service (port 5001)
npm run start:services:analytics  # Analytics Service (port 5002)
npm run start:services:auth       # Auth Service (port 5003)
npm run start:services:events     # Events/Meetings Service (port 5004)
npm run start:services:help       # Help Service (port 5005)
npm run start:services:messaging  # Messaging Service (port 5006)
npm run start:services:settings   # Settings Service (port 5007)
npm run start:services:students   # Students Service (port 5008)
npm run start:services:university # University Service (port 5009)
```

### Running Groups of Services

You can run groups of services using the following npm scripts:

```bash
# Run all core services
npm run start:core:all

# Run all API gateways
npm run start:gateways:all

# Run all domain services
npm run start:services:all

# Run all services
npm run start:all
```

### Main Application

To run the main application:

```bash
npm run start
```

### Listing All Services

To list all available services:

```bash
npm run list:services
```

## Development Workflow

For typical development, you might want to run:

1. The specific service you're working on
2. Any dependencies that service has
3. The appropriate API gateway

For example, if you're working on the Agency Service:

```bash
npm run start:services:agency
npm run start:gateways:agency
```

### Custom Service Runner

You can also use the custom service runner to run multiple services at once with proper port configuration:

```bash
# Run specific services
npm run services agency-service auth-service agency-apigw

# List available services
npm run services
```

This will start all specified services in parallel with proper port configuration and color-coded output.

## Adding New Services

When adding new services to the project:

1. Assign a port in the appropriate range in `scripts/service-ports.js`
2. Add a new npm script in `package.json` to run the service
3. Update this documentation

## Troubleshooting

If you encounter port conflicts, ensure no other applications are using the assigned ports. You can modify the port assignments in both the npm scripts and the `scripts/service-ports.js` file.
