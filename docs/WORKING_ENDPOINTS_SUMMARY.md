# Auth Service - Working Endpoints Summary

## 🔐 Authentication

### Login (Working ✅)
```bash
curl -X POST http://localhost:4006/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "superadmin123"
  }'
```
**Response:** Returns access token for authenticated requests

### Token Validation (Issues ❌)
```bash
curl -X GET http://localhost:4006/api/auth/validate \
  -H "Authorization: Bearer YOUR_TOKEN"
```
**Status:** Currently failing - needs investigation

## 📋 Module Management

### Create Module (Working ✅)
```bash
curl -X POST http://localhost:4006/api/auth/modules \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Module",
    "description": "Test module description"
  }'
```

### List Modules (Working ✅)
```bash
curl -X GET http://localhost:4006/api/auth/modules \
  -H "Authorization: Bearer YOUR_TOKEN"
```
**Result:** Successfully lists 15 modules

### Bulk Create Modules (Issues ❌)
```bash
curl -X POST http://localhost:4006/api/auth/modules/bulk \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "modules": [
      {"name": "Module 1", "description": "Description 1"},
      {"name": "Module 2", "description": "Description 2"}
    ]
  }'
```
**Status:** Currently failing

## 👥 Role Management

### Create Role (Working ✅)
```bash
curl -X POST http://localhost:4006/api/auth/role \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Role",
    "description": "Test role description",
    "permissions": [
      {"featureId": 1, "hasPermission": true},
      {"featureId": 2, "hasPermission": true}
    ]
  }'
```

### List Roles (Working ✅)
```bash
curl -X GET http://localhost:4006/api/auth/roles \
  -H "Authorization: Bearer YOUR_TOKEN"
```
**Result:** Successfully lists roles

### Get Role by ID (Issues ❌)
```bash
curl -X GET http://localhost:4006/api/auth/roles/1 \
  -H "Authorization: Bearer YOUR_TOKEN"
```
**Status:** Currently failing

### Update Role (Issues ❌)
```bash
curl -X PUT http://localhost:4006/api/auth/roles/1 \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Updated Role",
    "description": "Updated description"
  }'
```
**Status:** Currently failing

## 🏢 Department Management

### List Departments (Issues ❌)
```bash
curl -X GET http://localhost:4006/api/auth/departments \
  -H "Authorization: Bearer YOUR_TOKEN"
```
**Status:** Currently failing - returns 0 departments

### Create Departments (Issues ❌)
```bash
curl -X POST http://localhost:4006/api/auth/departments \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "departments": [
      {"name": "Engineering", "parent": ""},
      {"name": "Frontend", "parent": "Engineering"}
    ]
  }'
```
**Status:** Currently failing

### List Departments with Users (Working ✅)
```bash
curl -X GET http://localhost:4006/api/auth/departments-with-users \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 👨‍💼 Employee Management

### List Employees (Working ✅)
```bash
curl -X GET http://localhost:4006/api/auth/employees \
  -H "Authorization: Bearer YOUR_TOKEN"
```
**Result:** Successfully lists 2 employees

### Create Employee (Issues ❌)
```bash
curl -X POST http://localhost:4006/api/auth/employees \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Employee123!",
    "name": "John Employee",
    "phone": "******-0123",
    "departmentName": "Engineering",
    "organizationName": "ApplyGoal",
    "employeeRoleName": "Employee",
    "firstName": "John",
    "lastName": "Employee",
    "jobType": "full-time",
    "jobStatus": "active",
    "dateOfBirth": "1990-01-01",
    "bloodGroup": "O+",
    "gender": "male"
  }'
```
**Status:** Returns success message but creation may fail

### Get Employee by ID (Needs Testing)
```bash
curl -X GET http://localhost:4006/api/auth/employees/1 \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Update Employee (Needs Testing)
```bash
curl -X PUT http://localhost:4006/api/auth/employees/1 \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "firstName": "Updated John",
    "lastName": "Updated Employee"
  }'
```

## 🏛️ Organization Management

### Create Organization Role (Issues ❌)
```bash
curl -X POST http://localhost:4006/api/auth/organizations/1/roles \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Org Role",
    "description": "Organization specific role"
  }'
```
**Status:** Currently failing

### Get Organization Roles (Issues ❌)
```bash
curl -X GET http://localhost:4006/api/auth/organizations/1/roles \
  -H "Authorization: Bearer YOUR_TOKEN"
```
**Status:** Currently failing

### Create Organization Department (Issues ❌)
```bash
curl -X POST http://localhost:4006/api/auth/organizations/1/departments \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "HR Department",
    "description": "Human Resources"
  }'
```
**Status:** Currently failing

## 📊 Test Results Summary

### ✅ **Fully Working Endpoints:**
1. `POST /login` - User authentication
2. `POST /modules` - Create single module
3. `GET /modules` - List modules
4. `POST /role` - Create role with permissions
5. `GET /roles` - List roles
6. `GET /employees` - List employees
7. `GET /departments-with-users` - List departments with users

### ❌ **Endpoints with Issues:**
1. `GET /validate` - Token validation
2. `POST /modules/bulk` - Bulk module creation
3. `GET /departments` - List departments
4. `POST /departments` - Create departments
5. `GET /roles/{id}` - Get specific role
6. `PUT /roles/{id}` - Update role
7. `POST /employees` - Create employee (partial failure)
8. All organization-specific endpoints

### 🔧 **Working Authentication:**
- **Email:** `<EMAIL>`
- **Password:** `superadmin123`
- **Token:** Valid for 1 hour after login

## 🚀 **Quick Test Script:**
```bash
# Get token
TOKEN=$(curl -s -X POST http://localhost:4006/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"superadmin123"}' \
  | grep -o '"accessToken":"[^"]*"' | cut -d'"' -f4)

# Test working endpoints
curl -H "Authorization: Bearer $TOKEN" http://localhost:4006/api/auth/modules
curl -H "Authorization: Bearer $TOKEN" http://localhost:4006/api/auth/roles
curl -H "Authorization: Bearer $TOKEN" http://localhost:4006/api/auth/employees
```

## 📝 **Notes:**
1. Department management appears to have database/permission issues
2. Organization-specific operations may require different organization IDs
3. Employee creation has validation issues but listing works
4. Token validation endpoint needs investigation
5. Some endpoints may require specific permissions that the test user doesn't have
