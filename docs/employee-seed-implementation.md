# Employee Seed Implementation

## Overview
Updated the auth-service seed service to automatically create a complete employee record for the Super Admin user during database seeding.

## Changes Made

### 1. Updated Seed Service (`apps/services/auth-service/src/app/seed/seed.service.ts`)

#### Added Imports
```typescript
import { EmployeePersonal } from '../employee/models/employee-personal.model';
import { EmployeeAddress } from '../employee/models/employee-address.model';
import { EmployeeEmergencyContact } from '../employee/models/employee-emergency-contact.model';
import { EmployeeIdentityDoc } from '../employee/models/employee-identity-doc.model';
import { EmployeeBankAccount } from '../employee/models/employee-bank-account.model';
```

#### Added Model Injections
```typescript
@InjectModel(EmployeePersonal) private employeeModel: typeof EmployeePersonal,
@InjectModel(EmployeeAddress) private employeeAddressModel: typeof EmployeeAddress,
@InjectModel(EmployeeEmergencyContact) private employeeEmergencyContactModel: typeof EmployeeEmergencyContact,
@InjectModel(EmployeeIdentityDoc) private employeeIdentityDocModel: typeof EmployeeIdentityDoc,
@InjectModel(EmployeeBankAccount) private employeeBankAccountModel: typeof EmployeeBankAccount
```

#### Updated Seed Flow
Added `await this.seedSuperAdminEmployee(tx);` to the main seed method after creating the Super Admin user.

#### New Method: `seedSuperAdminEmployee(tx)`
Creates a complete employee profile for the Super Admin user including:

**Employee Personal Information:**
- First Name: "Super"
- Last Name: "Admin"
- Joining Date: Current date
- Job Type: "Full-time"
- Job Status: "Active"
- Date of Birth: "1990-01-01"
- Blood Group: "O+"
- Gender: "Other"
- Organization ID: ApplyGoal organization ID

**Employee Address:**
- Address Type: "Work"
- Address Line: "ApplyGoal Headquarters"
- Country: "Canada"
- State: "Ontario"
- City: "Toronto"
- Postal Code: "M5V 3A8"

**Emergency Contact:**
- Contact Type: "Emergency"
- Contact Name: "ApplyGoal Support"
- Contact Phone: "******-APPLYGOAL"
- Contact Email: "<EMAIL>"
- Relationship: "Organization"

**Identity Document:**
- Document Type: "Admin ID"
- Nationality: "Canadian"
- Issue Date: Current date
- Expiry Date: 10 years from current date

**Bank Account:**
- Account Holder: "Super Admin"
- Account Number: "ADMIN-001"
- Bank Name: "ApplyGoal Bank"
- Branch Name: "Main Branch"

## Features

### 1. **Idempotent Operation**
- Checks if Super Admin user exists before proceeding
- Checks if employee record already exists to avoid duplicates
- Safe to run multiple times

### 2. **Complete Profile**
- Creates all related employee data (address, emergency contact, identity doc, bank account)
- Uses realistic sample data appropriate for a Super Admin

### 3. **Organization Integration**
- Links employee to the ApplyGoal organization
- Uses organization ID for the orgId field

### 4. **Transaction Safety**
- All operations are performed within the existing database transaction
- Ensures data consistency

### 5. **Logging**
- Provides clear logging messages for each step
- Indicates when operations are skipped due to existing data

## Usage

The employee record will be automatically created when running the seed command:

```bash
# Run the seed service
npm run seed

# Or through the application startup if auto-seeding is enabled
npm run start:dev
```

## Verification

After seeding, you can verify the employee record was created by:

1. **Using the Employee API endpoints:**
   ```bash
   # Get the Super Admin employee record (assuming user ID 1)
   curl -X GET "http://localhost:4006/api/auth/employees/1" \
     -H "Authorization: Bearer YOUR_JWT_TOKEN"
   ```

2. **Checking the database directly:**
   ```sql
   SELECT * FROM employee_personal WHERE userId = 1;
   SELECT * FROM employee_address WHERE userId = 1;
   SELECT * FROM employee_emergency_contact WHERE userId = 1;
   SELECT * FROM employee_identity_doc WHERE userId = 1;
   SELECT * FROM employee_bank_account WHERE userId = 1;
   ```

3. **Using the List Employees endpoint:**
   ```bash
   curl -X GET "http://localhost:4006/api/auth/employees" \
     -H "Authorization: Bearer YOUR_JWT_TOKEN"
   ```

## Benefits

1. **Complete Test Data**: Provides a fully populated employee record for testing
2. **API Testing**: Enables immediate testing of all employee CRUD operations
3. **Realistic Data**: Uses appropriate sample data for a Super Admin role
4. **Development Ready**: No manual setup required for employee functionality testing

## Notes

- The employee record is only created if the Super Admin user exists
- All employee data uses sample values appropriate for testing
- The implementation follows the same patterns as other seed operations
- Employee models were already registered in the seed module, so no additional module changes were needed

## Next Steps

With this implementation, you can now:
1. Test all employee API endpoints immediately after seeding
2. Use the Super Admin employee as a template for creating other employee records
3. Verify the complete employee management workflow end-to-end
