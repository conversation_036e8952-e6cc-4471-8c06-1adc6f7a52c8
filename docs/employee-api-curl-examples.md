# Employee API Curl Examples

This document contains curl examples for all employee management endpoints in the auth-apigw.

## Base URL
```
BASE_URL=http://localhost:4006/api
```

## Authentication
All endpoints require authentication. Replace `YOUR_JWT_TOKEN` with a valid JWT token.

## 1. Create Employee

### Basic Employee Creation
```bash
curl -X POST "${BASE_URL}/auth/employees" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "User-Agent: curl/7.68.0" \
  -d '{
    "userId": 123,
    "firstName": "<PERSON>",
    "lastName": "Doe",
    "joiningDate": "2024-01-15",
    "jobType": "Full-time",
    "jobStatus": "Active",
    "dateOfBirth": "1990-05-15",
    "bloodGroup": "O+",
    "gender": "Male",
    "agencyId": 1,
    "orgId": "ORG001"
  }'
```

### Employee Creation with Complete Details
```bash
curl -X POST "${BASE_URL}/auth/employees" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "User-Agent: curl/7.68.0" \
  -d '{
    "userId": 124,
    "firstName": "Jane",
    "lastName": "Smith",
    "joiningDate": "2024-02-01",
    "jobType": "Part-time",
    "jobStatus": "Active",
    "dateOfBirth": "1992-08-20",
    "bloodGroup": "A+",
    "gender": "Female",
    "agencyId": 1,
    "orgId": "ORG001",
    "addresses": [
      {
        "addressType": "Home",
        "addressLine": "123 Main Street, Apt 4B",
        "country": "USA",
        "state": "California",
        "city": "San Francisco",
        "postalCode": "94102"
      },
      {
        "addressType": "Work",
        "addressLine": "456 Business Ave, Suite 200",
        "country": "USA",
        "state": "California",
        "city": "San Francisco",
        "postalCode": "94105"
      }
    ],
    "emergencyContacts": [
      {
        "contactType": "Primary",
        "contactName": "Robert Smith",
        "contactPhone": "******-0123",
        "contactEmail": "<EMAIL>",
        "relationship": "Spouse"
      }
    ],
    "identityDocs": [
      {
        "docType": "Passport",
        "docNumber": "P123456789",
        "docUrl": "https://example.com/docs/passport.pdf",
        "expiryDate": "2030-12-31"
      }
    ],
    "bankAccounts": [
      {
        "bankName": "Chase Bank",
        "accountNumber": "**********",
        "routingNumber": "*********",
        "accountType": "Checking"
      }
    ]
  }'
```

## 2. Get Employee by ID

```bash
curl -X GET "${BASE_URL}/auth/employees/123" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "User-Agent: curl/7.68.0"
```

## 3. List Employees

### Basic List (All Employees)
```bash
curl -X GET "${BASE_URL}/auth/employees" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "User-Agent: curl/7.68.0"
```

### List with Pagination
```bash
curl -X GET "${BASE_URL}/auth/employees?page=1&limit=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "User-Agent: curl/7.68.0"
```

### List with Search
```bash
curl -X GET "${BASE_URL}/auth/employees?search=John" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "User-Agent: curl/7.68.0"
```

### List with Filters
```bash
curl -X GET "${BASE_URL}/auth/employees?jobType=Full-time&jobStatus=Active&agencyId=1" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "User-Agent: curl/7.68.0"
```

### List with All Parameters
```bash
curl -X GET "${BASE_URL}/auth/employees?page=1&limit=5&search=John&jobType=Full-time&jobStatus=Active&agencyId=1&orgId=ORG001" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "User-Agent: curl/7.68.0"
```

## 4. Update Employee

### Basic Update
```bash
curl -X PUT "${BASE_URL}/auth/employees/123" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "User-Agent: curl/7.68.0" \
  -d '{
    "firstName": "John Updated",
    "lastName": "Doe Updated",
    "jobStatus": "On Leave"
  }'
```

### Complete Update
```bash
curl -X PUT "${BASE_URL}/auth/employees/123" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "User-Agent: curl/7.68.0" \
  -d '{
    "firstName": "John",
    "lastName": "Doe",
    "joiningDate": "2024-01-15",
    "jobType": "Full-time",
    "jobStatus": "Active",
    "dateOfBirth": "1990-05-15",
    "bloodGroup": "O+",
    "gender": "Male",
    "agencyId": 2,
    "orgId": "ORG002",
    "addresses": [
      {
        "id": 1,
        "addressType": "Home",
        "addressLine": "789 New Street, Apt 5C",
        "country": "USA",
        "state": "California",
        "city": "Los Angeles",
        "postalCode": "90210"
      }
    ],
    "emergencyContacts": [
      {
        "id": 1,
        "contactType": "Primary",
        "contactName": "Mary Doe",
        "contactPhone": "******-0456",
        "contactEmail": "<EMAIL>",
        "relationship": "Wife"
      }
    ],
    "identityDocs": [
      {
        "id": 1,
        "docType": "Driver License",
        "docNumber": "DL987654321",
        "docUrl": "https://example.com/docs/license.pdf",
        "expiryDate": "2028-05-15"
      }
    ],
    "bankAccounts": [
      {
        "id": 1,
        "bankName": "Bank of America",
        "accountNumber": "**********",
        "routingNumber": "*********",
        "accountType": "Savings"
      }
    ]
  }'
```

## 5. Delete Employee

```bash
curl -X DELETE "${BASE_URL}/auth/employees/123" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "User-Agent: curl/7.68.0"
```

## Environment Variables Setup

You can set up environment variables to make testing easier:

```bash
# Set base URL
export BASE_URL="http://localhost:4006/api"

# Set your JWT token (replace with actual token)
export JWT_TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."

# Now you can use them in curl commands
curl -X GET "${BASE_URL}/auth/employees" \
  -H "Authorization: Bearer ${JWT_TOKEN}" \
  -H "User-Agent: curl/7.68.0"
```

## Expected Response Formats

### Success Response for Create/Get/Update Employee
```json
{
  "success": true,
  "message": "Employee created successfully",
  "employee": {
    "userId": 123,
    "firstName": "John",
    "lastName": "Doe",
    "joiningDate": "2024-01-15",
    "jobType": "Full-time",
    "jobStatus": "Active",
    "dateOfBirth": "1990-05-15",
    "bloodGroup": "O+",
    "gender": "Male",
    "agencyId": 1,
    "orgId": "ORG001",
    "addresses": [...],
    "emergencyContacts": [...],
    "identityDocs": [...],
    "bankAccounts": [...],
    "createdAt": "2024-01-15T10:30:00Z",
    "updatedAt": "2024-01-15T10:30:00Z"
  }
}
```

### Success Response for List Employees
```json
{
  "success": true,
  "message": "Employees retrieved successfully",
  "employees": [...],
  "total": 25,
  "page": 1,
  "limit": 10
}
```

### Success Response for Delete Employee
```json
{
  "success": true,
  "message": "Employee removed successfully"
}
```

### Error Response
```json
{
  "success": false,
  "message": "Error description here"
}
```

## Notes

1. All endpoints require the `SS_EMPLOYEE_MANAGEMENT` permission
2. The `User-Agent` header is recommended for audit logging
3. The server will automatically capture the client's IP address
4. All date fields should be in ISO 8601 format (YYYY-MM-DD)
5. The `userId` field in create requests should reference an existing user ID
6. When updating, you can provide partial data - only provided fields will be updated
