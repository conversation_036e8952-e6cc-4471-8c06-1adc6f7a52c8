#!/bin/bash

echo "🔐 Testing Password Reset Functionality..."

echo "1. Restarting auth-service to apply password reset changes..."
docker compose stop auth-service
docker compose rm -f auth-service
docker compose up -d auth-service

echo "2. Waiting for auth-service to be healthy..."
sleep 15

for i in {1..10}; do
    if curl -f -s http://localhost:5003/health > /dev/null; then
        echo "✅ Auth service is healthy"
        break
    else
        echo "⏳ Waiting for auth-service... (attempt $i/10)"
        sleep 3
    fi
done

echo "3. Getting Super Admin token..."
TOKEN=$(curl -s -X POST "http://localhost:4006/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SuperAdmin@123",
    "ipAddress": "127.0.0.1"
  }' | jq -r '.accessToken')

if [ "$TOKEN" = "null" ] || [ -z "$TOKEN" ]; then
    echo "❌ Failed to get token. Check login credentials."
    exit 1
fi

echo "✅ Got Super Admin token: ${TOKEN:0:50}..."

echo ""
echo "4. Testing Forgot Password..."
FORGOT_RESPONSE=$(curl -s -w "%{http_code}" -X POST "http://localhost:4006/api/auth/forgot-password" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>"
  }')

FORGOT_HTTP_CODE="${FORGOT_RESPONSE: -3}"
FORGOT_BODY="${FORGOT_RESPONSE%???}"

echo "HTTP Status: $FORGOT_HTTP_CODE"

if [ "$FORGOT_HTTP_CODE" = "200" ] || [ "$FORGOT_HTTP_CODE" = "201" ]; then
    echo "✅ Forgot password request is working!"
    echo "Response: $FORGOT_BODY" | jq . 2>/dev/null || echo "$FORGOT_BODY"
    
    # Extract reset token if available (development mode)
    RESET_TOKEN=$(echo "$FORGOT_BODY" | jq -r '.resetToken' 2>/dev/null)
    if [ "$RESET_TOKEN" != "null" ] && [ -n "$RESET_TOKEN" ]; then
        echo "🔑 Reset token (dev mode): $RESET_TOKEN"
    fi
else
    echo "❌ Forgot password failed. HTTP $FORGOT_HTTP_CODE"
    echo "Response: $FORGOT_BODY"
fi

echo ""
echo "5. Testing Forgot Password with Non-existent Email..."
FORGOT_INVALID_RESPONSE=$(curl -s -w "%{http_code}" -X POST "http://localhost:4006/api/auth/forgot-password" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>"
  }')

FORGOT_INVALID_HTTP_CODE="${FORGOT_INVALID_RESPONSE: -3}"
FORGOT_INVALID_BODY="${FORGOT_INVALID_RESPONSE%???}"

echo "HTTP Status: $FORGOT_INVALID_HTTP_CODE"

if [ "$FORGOT_INVALID_HTTP_CODE" = "200" ] || [ "$FORGOT_INVALID_HTTP_CODE" = "201" ]; then
    echo "✅ Forgot password with invalid email handled correctly (security)"
    echo "Response: $FORGOT_INVALID_BODY" | jq . 2>/dev/null || echo "$FORGOT_INVALID_BODY"
else
    echo "❌ Forgot password with invalid email failed. HTTP $FORGOT_INVALID_HTTP_CODE"
    echo "Response: $FORGOT_INVALID_BODY"
fi

if [ "$RESET_TOKEN" != "null" ] && [ -n "$RESET_TOKEN" ]; then
    echo ""
    echo "6. Testing Verify Reset Token..."
    VERIFY_RESPONSE=$(curl -s -w "%{http_code}" -X POST "http://localhost:4006/api/auth/verify-reset-token" \
      -H "Content-Type: application/json" \
      -d "{
        \"email\": \"<EMAIL>\",
        \"resetToken\": \"$RESET_TOKEN\"
      }")

    VERIFY_HTTP_CODE="${VERIFY_RESPONSE: -3}"
    VERIFY_BODY="${VERIFY_RESPONSE%???}"

    echo "HTTP Status: $VERIFY_HTTP_CODE"

    if [ "$VERIFY_HTTP_CODE" = "200" ]; then
        echo "✅ Verify reset token is working!"
        echo "Response: $VERIFY_BODY" | jq . 2>/dev/null || echo "$VERIFY_BODY"
    else
        echo "❌ Verify reset token failed. HTTP $VERIFY_HTTP_CODE"
        echo "Response: $VERIFY_BODY"
    fi

    echo ""
    echo "7. Testing Verify Reset Token with Invalid Token..."
    VERIFY_INVALID_RESPONSE=$(curl -s -w "%{http_code}" -X POST "http://localhost:4006/api/auth/verify-reset-token" \
      -H "Content-Type: application/json" \
      -d '{
        "email": "<EMAIL>",
        "resetToken": "invalid-token-12345"
      }')

    VERIFY_INVALID_HTTP_CODE="${VERIFY_INVALID_RESPONSE: -3}"
    VERIFY_INVALID_BODY="${VERIFY_INVALID_RESPONSE%???}"

    echo "HTTP Status: $VERIFY_INVALID_HTTP_CODE"

    if [ "$VERIFY_INVALID_HTTP_CODE" = "200" ]; then
        echo "✅ Verify invalid reset token handled correctly"
        echo "Response: $VERIFY_INVALID_BODY" | jq . 2>/dev/null || echo "$VERIFY_INVALID_BODY"
    else
        echo "❌ Verify invalid reset token failed. HTTP $VERIFY_INVALID_HTTP_CODE"
        echo "Response: $VERIFY_INVALID_BODY"
    fi

    echo ""
    echo "8. Testing Reset Password..."
    RESET_RESPONSE=$(curl -s -w "%{http_code}" -X POST "http://localhost:4006/api/auth/reset-password" \
      -H "Content-Type: application/json" \
      -d "{
        \"email\": \"<EMAIL>\",
        \"resetToken\": \"$RESET_TOKEN\",
        \"newPassword\": \"NewPassword@123\",
        \"confirmPassword\": \"NewPassword@123\"
      }")

    RESET_HTTP_CODE="${RESET_RESPONSE: -3}"
    RESET_BODY="${RESET_RESPONSE%???}"

    echo "HTTP Status: $RESET_HTTP_CODE"

    if [ "$RESET_HTTP_CODE" = "200" ]; then
        echo "✅ Reset password is working!"
        echo "Response: $RESET_BODY" | jq . 2>/dev/null || echo "$RESET_BODY"
        
        echo ""
        echo "9. Testing Login with New Password..."
        LOGIN_NEW_RESPONSE=$(curl -s -w "%{http_code}" -X POST "http://localhost:4006/api/auth/login" \
          -H "Content-Type: application/json" \
          -d '{
            "email": "<EMAIL>",
            "password": "NewPassword@123",
            "ipAddress": "127.0.0.1"
          }')

        LOGIN_NEW_HTTP_CODE="${LOGIN_NEW_RESPONSE: -3}"
        LOGIN_NEW_BODY="${LOGIN_NEW_RESPONSE%???}"

        echo "HTTP Status: $LOGIN_NEW_HTTP_CODE"

        if [ "$LOGIN_NEW_HTTP_CODE" = "200" ]; then
            echo "✅ Login with new password is working!"
            echo "Password reset flow completed successfully!"
        else
            echo "❌ Login with new password failed. HTTP $LOGIN_NEW_HTTP_CODE"
            echo "Response: $LOGIN_NEW_BODY"
        fi

        echo ""
        echo "10. Resetting password back to original..."
        # Get new token with new password
        NEW_TOKEN=$(echo "$LOGIN_NEW_BODY" | jq -r '.accessToken' 2>/dev/null)
        
        # Request another password reset
        RESTORE_FORGOT_RESPONSE=$(curl -s -X POST "http://localhost:4006/api/auth/forgot-password" \
          -H "Content-Type: application/json" \
          -d '{
            "email": "<EMAIL>"
          }')

        RESTORE_RESET_TOKEN=$(echo "$RESTORE_FORGOT_RESPONSE" | jq -r '.resetToken' 2>/dev/null)
        
        if [ "$RESTORE_RESET_TOKEN" != "null" ] && [ -n "$RESTORE_RESET_TOKEN" ]; then
            # Reset back to original password
            curl -s -X POST "http://localhost:4006/api/auth/reset-password" \
              -H "Content-Type: application/json" \
              -d "{
                \"email\": \"<EMAIL>\",
                \"resetToken\": \"$RESTORE_RESET_TOKEN\",
                \"newPassword\": \"SuperAdmin@123\",
                \"confirmPassword\": \"SuperAdmin@123\"
              }" > /dev/null
            echo "✅ Password restored to original"
        fi
    else
        echo "❌ Reset password failed. HTTP $RESET_HTTP_CODE"
        echo "Response: $RESET_BODY"
    fi

    echo ""
    echo "11. Testing Reset Password with Mismatched Passwords..."
    MISMATCH_RESPONSE=$(curl -s -w "%{http_code}" -X POST "http://localhost:4006/api/auth/reset-password" \
      -H "Content-Type: application/json" \
      -d "{
        \"email\": \"<EMAIL>\",
        \"resetToken\": \"$RESET_TOKEN\",
        \"newPassword\": \"Password123!\",
        \"confirmPassword\": \"DifferentPassword123!\"
      }")

    MISMATCH_HTTP_CODE="${MISMATCH_RESPONSE: -3}"
    MISMATCH_BODY="${MISMATCH_RESPONSE%???}"

    echo "HTTP Status: $MISMATCH_HTTP_CODE"

    if [ "$MISMATCH_HTTP_CODE" = "400" ]; then
        echo "✅ Password mismatch validation is working!"
        echo "Response: $MISMATCH_BODY" | jq . 2>/dev/null || echo "$MISMATCH_BODY"
    else
        echo "❌ Password mismatch validation failed. HTTP $MISMATCH_HTTP_CODE"
        echo "Response: $MISMATCH_BODY"
    fi
else
    echo "⚠️ No reset token available for further testing (production mode)"
fi

echo ""
echo "📋 Summary:"
echo "Forgot Password: $([ "$FORGOT_HTTP_CODE" = "200" ] || [ "$FORGOT_HTTP_CODE" = "201" ] && echo "✅ WORKING" || echo "❌ FAILED ($FORGOT_HTTP_CODE)")"
echo "Forgot Password (Invalid Email): $([ "$FORGOT_INVALID_HTTP_CODE" = "200" ] || [ "$FORGOT_INVALID_HTTP_CODE" = "201" ] && echo "✅ WORKING" || echo "❌ FAILED ($FORGOT_INVALID_HTTP_CODE)")"

if [ "$RESET_TOKEN" != "null" ] && [ -n "$RESET_TOKEN" ]; then
    echo "Verify Reset Token: $([ "$VERIFY_HTTP_CODE" = "200" ] && echo "✅ WORKING" || echo "❌ FAILED ($VERIFY_HTTP_CODE)")"
    echo "Verify Invalid Token: $([ "$VERIFY_INVALID_HTTP_CODE" = "200" ] && echo "✅ WORKING" || echo "❌ FAILED ($VERIFY_INVALID_HTTP_CODE)")"
    echo "Reset Password: $([ "$RESET_HTTP_CODE" = "200" ] && echo "✅ WORKING" || echo "❌ FAILED ($RESET_HTTP_CODE)")"
    echo "Login with New Password: $([ "$LOGIN_NEW_HTTP_CODE" = "200" ] && echo "✅ WORKING" || echo "❌ FAILED ($LOGIN_NEW_HTTP_CODE)")"
    echo "Password Mismatch Validation: $([ "$MISMATCH_HTTP_CODE" = "400" ] && echo "✅ WORKING" || echo "❌ FAILED ($MISMATCH_HTTP_CODE)")"
fi

echo ""
echo "🔍 Debug commands if issues persist:"
echo "docker compose logs -f auth-service | grep -i password"
echo "docker compose logs -f auth-apigw | grep -i password"

echo ""
echo "Manual test commands:"
echo "# Forgot password"
echo "curl -X POST 'http://localhost:4006/api/auth/forgot-password' \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -d '{\"email\": \"<EMAIL>\"}'"
echo ""
echo "# Verify reset token"
echo "curl -X POST 'http://localhost:4006/api/auth/verify-reset-token' \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -d '{\"email\": \"<EMAIL>\", \"resetToken\": \"YOUR_TOKEN\"}'"
echo ""
echo "# Reset password"
echo "curl -X POST 'http://localhost:4006/api/auth/reset-password' \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -d '{\"email\": \"<EMAIL>\", \"resetToken\": \"YOUR_TOKEN\", \"newPassword\": \"NewPass123!\", \"confirmPassword\": \"NewPass123!\"}'"
