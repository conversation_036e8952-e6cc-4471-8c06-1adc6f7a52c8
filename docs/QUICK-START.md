# ApplyGoal Backend Quick Start Guide

This guide provides a quick overview of how to get started with the ApplyGoal Backend project.

## Setup Options

### Option 1: Local Development Setup

1. **Clone the repository**
```sh
git clone https://github.com/your-username/apply-goal-backend.git
cd apply-goal-backend
```

2. **Install dependencies**
```sh
npm install
```

3. **Start the application**
```sh
# Start the main application
npm start

# Or start all services
npm run start:all
```

### Option 2: Docker Setup (Recommended)

#### A. Automated Setup (Using Scripts)

1. **Clone the repository**
```sh
git clone https://github.com/your-username/apply-goal-backend.git
cd apply-goal-backend
```

2. **Make scripts executable**
```sh
chmod +x scripts/start-system.sh
chmod +x scripts/stop-system.sh
```

3. **Start the entire system**
```sh
./scripts/start-system.sh
```

The startup script will automatically:
1. Start Infrastructure Services:
   - PostgreSQL (port 5432)
   - Redis (port 6379)
   - RabbitMQ (ports 5672, 15672)
   - MongoDB (port 27017)

2. Start Core Services:
   - Audit Logging Service
   - Identity Service
   - Payment Service

3. Start Domain Services:
   - Agency Service
   - Analytics Service
   - Auth Service
   - Events & Meetings Service
   - Help Service
   - Messaging Service
   - Settings Service
   - Students Service
   - University Service

4. Start API Gateways:
   - Agency API Gateway
   - Manager API Gateway
   - Student API Gateway
   - Super Admin API Gateway
   - University API Gateway

4. **Stop the system**
```sh
./scripts/stop-system.sh
```

#### B. Manual Docker Setup

1. **Start Infrastructure Services**
```sh
# Start infrastructure services
docker compose up -d postgres redis rabbitmq mongodb-chat

# Verify services are healthy
docker compose ps
```

2. **Start Core Services**
```sh
# Start core services
docker compose up -d audit-logging identity-service payment-service

# Wait for services to be healthy
docker compose ps
```

3. **Start Domain Services**
```sh
# Start domain services
docker compose up -d \
    agency-service \
    analytics-service \
    auth-service \
    events-meetings-service \
    help-service \
    messaging-service \
    settings-service \
    students-service \
    university-service

# Verify services
docker compose ps
```

4. **Start API Gateways**
```sh
# Start API gateways
docker compose up -d \
    agency-apigw \
    manager-apigw \
    student-apigw \
    super-admin-apigw \
    university-apigw

# Verify all services are running
docker compose ps
```

5. **Stop All Services**
```sh
# Stop services
docker compose down

# Optional: Clean up volumes
docker compose down -v

# Optional: Clean up images
docker image prune -a -f
```

## Service Architecture

### Port Reference

| Service Type | Port Range | Examples |
|--------------|------------|----------|
| Main App     | 3000       | Main application |
| Core Services| 3001-3099  | Identity (3001), Payment (3002) |
| API Gateways | 4001-4099  | Agency API (4001), Manager API (4002) |
| Domain Services | 5001-5099 | Agency (5001), Events (5004) |

### Infrastructure Services

| Service   | Port(s)      | Admin Interface |
|-----------|--------------|-----------------|
| PostgreSQL| 5432         | N/A |
| Redis     | 6379         | N/A |
| RabbitMQ  | 5672, 15672  | http://localhost:15672 |
| MongoDB   | 27017        | http://localhost:8081 |

### Service URLs (Docker)

- Agency API: http://agency-api.localhost
- Manager API: http://manager-api.localhost
- Student API: http://student-api.localhost
- Admin API: http://admin-api.localhost
- University API: http://university-api.localhost
- Traefik Dashboard: http://traefik.localhost:8080

## Common Development Tasks

### Running Individual Services

```sh
# Run a specific service
npm run start:services:agency

# Run multiple specific services
npm run services agency-service auth-service agency-apigw

# Run all core services
npm run start:core:all

# Run all API gateways
npm run start:gateways:all

# Run all domain services
npm run start:services:all
```

### Docker Commands

```sh
# View running services
docker compose ps

# View service logs
docker compose logs -f [service-name]

# View logs for specific service
docker compose logs -f agency-service

# View logs for multiple services
docker compose logs -f agency-service auth-service

# Restart specific service
docker compose restart [service-name]

# Check service health
docker compose ps [service-name]

# View resource usage
docker stats

# Enter service container
docker compose exec [service-name] sh

# View service configuration
docker compose config [service-name]
```

### Service Management

```sh
# Restart specific service
docker compose restart [service-name]

# Rebuild and restart service
docker compose up -d --build [service-name]

# Scale service (if supported)
docker compose up -d --scale [service-name]=2

# Update service configuration
docker compose up -d --force-recreate [service-name]
```

### Testing

```sh
# Run tests for a specific service
npx nx test agency-service

# Run all tests
npx nx run-many --target=test --all
```

### Creating New Components

```sh
# Create a new service
npx nx g @nx/nest:app new-service --directory=services

# Create a new library
npx nx g @nx/node:lib new-lib --directory=shared
```

After creating a new service:
1. Assign a port in the appropriate range
2. Add Docker configuration
3. Update documentation
4. Add health checks

### Building for Production

```sh
# Build a specific service
npx nx build agency-service --prod

# Build all services
npx nx run-many --target=build --all --prod

# Build Docker images
docker compose build
```

## Monitoring & Debugging

- Access Traefik Dashboard: http://traefik.localhost:8080
- RabbitMQ Management: http://localhost:15672
- MongoDB Admin: http://localhost:8081

## Documentation

For more detailed information, refer to:
- [README.md](README.md) - Main project documentation
- [SERVICES.md](SERVICES.md) - Detailed service documentation

## Need Help?

If you encounter any issues or have questions:
1. Check the existing documentation
2. Review the [NestJS documentation](https://docs.nestjs.com/)
3. Review the [Nx documentation](https://nx.dev/)
4. Contact the project maintainers


