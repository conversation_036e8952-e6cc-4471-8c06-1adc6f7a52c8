#!/bin/bash

echo "🔧 Fixing Employee API Permissions..."

echo "1. Stopping auth-service to re-seed database..."
docker compose stop auth-service

echo "2. Re-seeding the database with latest permissions..."
docker compose run --rm auth-service npm run seed

echo "3. Starting auth-service..."
docker compose start auth-service

echo "4. Waiting for auth-service to be healthy..."
sleep 10

echo "5. Testing auth-service health..."
curl -f http://localhost:5003/health || echo "❌ Auth service not healthy yet"

echo "6. Getting fresh Super Admin token..."
TOKEN=$(curl -s -X POST "http://localhost:4006/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SuperAdmin@123",
    "ipAddress": "127.0.0.1"
  }' | jq -r '.accessToken')

if [ "$TOKEN" = "null" ] || [ -z "$TOKEN" ]; then
    echo "❌ Failed to get token. Check login credentials."
    exit 1
fi

echo "✅ Got fresh token: ${TOKEN:0:50}..."

echo "7. Testing employee API access..."
RESPONSE=$(curl -s -w "%{http_code}" -X GET "http://localhost:4006/api/auth/employees" \
  -H "Authorization: Bearer $TOKEN" \
  -H "User-Agent: curl/7.68.0")

HTTP_CODE="${RESPONSE: -3}"
BODY="${RESPONSE%???}"

echo "HTTP Status: $HTTP_CODE"

if [ "$HTTP_CODE" = "200" ]; then
    echo "✅ Employee API is now working!"
    echo "Response: $BODY"
else
    echo "❌ Employee API still not working. HTTP $HTTP_CODE"
    echo "Response: $BODY"
    echo ""
    echo "🔍 Additional debugging needed. Check:"
    echo "1. Database seeding logs: docker compose logs auth-service | grep -i seed"
    echo "2. Permission validation logs: docker compose logs auth-apigw | grep -i permission"
    echo "3. Token validation: curl -X GET 'http://localhost:4006/api/auth/validate' -H 'Authorization: Bearer $TOKEN'"
fi

echo ""
echo "🎯 Your fresh token for testing:"
echo "export TOKEN='$TOKEN'"
echo ""
echo "Test commands:"
echo "curl -X GET 'http://localhost:4006/api/auth/employees' -H 'Authorization: Bearer \$TOKEN'"
echo "curl -X POST 'http://localhost:4006/api/auth/employees' -H 'Authorization: Bearer \$TOKEN' -H 'Content-Type: application/json' -d '{\"userId\": 123, \"firstName\": \"Test\", \"lastName\": \"Employee\"}'"
