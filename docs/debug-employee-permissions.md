# Employee API 401 Permission Debug Guide

## Issue Analysis
Employee APIs are returning 401 even with valid JWT token. Module and department authorization work, but employee-specific permissions fail.

## Root Cause
The employee endpoints require `UserPermissions.SS_EMPLOYEE_MANAGEMENT` permission which maps to:
```
'SystemSettings:Employeemanagement'
```

## Debugging Steps

### 1. Check Your Current Token Permissions
First, decode your JWT token to see what permissions it contains:

```bash
# Get a fresh token and decode it
TOKEN=$(curl -s -X POST "http://localhost:4006/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SuperAdmin@123",
    "ipAddress": "127.0.0.1"
  }' | jq -r '.accessToken')

echo "Token: $TOKEN"

# Decode the token (you can use jwt.io or a JWT decoder)
# Check if 'SystemSettings:Employeemanagement' is in the permissions array
```

### 2. Validate Token via API
```bash
curl -X GET "http://localhost:4006/api/auth/validate" \
  -H "Authorization: Bearer $TOKEN" | jq .
```

### 3. Check Database for Permission
```sql
-- Check if the permission exists in features table
SELECT f.id, f.name, m.name as module_name 
FROM features f 
JOIN modules m ON f.moduleId = m.id 
WHERE f.name = 'Employee management' AND m.name = 'System Settings';

-- Check if Super Admin role has this permission
SELECT r.name as role_name, m.name as module_name, f.name as feature_name, rfp.hasPermission
FROM roles r
JOIN role_feature_permissions rfp ON r.id = rfp.roleId
JOIN features f ON rfp.featureId = f.id
JOIN modules m ON f.moduleId = m.id
WHERE r.name = 'Super Admin' 
AND f.name = 'Employee management' 
AND m.name = 'System Settings';
```

### 4. Check User's Actual Permissions
```sql
-- Get Super Admin user's permissions
SELECT DISTINCT 
    CONCAT(m.name, ':', f.name) as permission_string
FROM users u
JOIN user_roles ur ON u.id = ur.userId
JOIN roles r ON ur.roleId = r.id
JOIN role_feature_permissions rfp ON r.id = rfp.roleId
JOIN features f ON rfp.featureId = f.id
JOIN modules m ON f.moduleId = m.id
WHERE u.email = '<EMAIL>'
AND rfp.hasPermission = true
ORDER BY permission_string;
```

## Quick Fixes

### Option 1: Re-seed the Database
If the permission is missing, re-run the seed:

```bash
# Stop services
docker compose stop auth-service

# Re-seed the database
docker compose exec auth-service npm run seed

# Restart service
docker compose start auth-service
```

### Option 2: Temporary Fix - Use Different Permission
If you need immediate access, temporarily change the employee endpoints to use a permission that works:

```typescript
// In auth.controller.ts, temporarily change from:
@Permissions(UserPermissions.SS_EMPLOYEE_MANAGEMENT)

// To one that works, like:
@Permissions(UserPermissions.SS_ROLE_AND_PERMISSION_SETUP)
// or
@Permissions(UserPermissions.SS_CRM_CONFIGURATION)
```

### Option 3: Add Permission Manually
```sql
-- If the feature doesn't exist, add it
INSERT INTO features (name, moduleId, createdAt, updatedAt) 
SELECT 'Employee management', m.id, NOW(), NOW()
FROM modules m 
WHERE m.name = 'System Settings'
AND NOT EXISTS (
    SELECT 1 FROM features f 
    WHERE f.name = 'Employee management' AND f.moduleId = m.id
);

-- Grant permission to Super Admin
INSERT INTO role_feature_permissions (roleId, featureId, hasPermission, createdAt, updatedAt)
SELECT r.id, f.id, true, NOW(), NOW()
FROM roles r, features f, modules m
WHERE r.name = 'Super Admin'
AND f.name = 'Employee management'
AND f.moduleId = m.id
AND m.name = 'System Settings'
AND NOT EXISTS (
    SELECT 1 FROM role_feature_permissions rfp
    WHERE rfp.roleId = r.id AND rfp.featureId = f.id
);
```

### Option 4: Check Permission String Format
The issue might be in the permission string format. Check if it should be:
- `'SystemSettings:Employeemanagement'` (current)
- `'SystemSettings:Employee management'` (with space)
- `'System Settings:Employee management'` (both with spaces)

## Testing After Fix

```bash
# Get fresh token after any database changes
TOKEN=$(curl -s -X POST "http://localhost:4006/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SuperAdmin@123",
    "ipAddress": "127.0.0.1"
  }' | jq -r '.accessToken')

# Test employee endpoint
curl -X GET "http://localhost:4006/api/auth/employees" \
  -H "Authorization: Bearer $TOKEN" \
  -H "User-Agent: curl/7.68.0"
```

## Debug Logs

Enable debug logging to see what's happening:

```bash
# Check auth-apigw logs for permission validation
docker compose logs -f auth-apigw | grep -i "permission\|employee\|401"

# Check if the permission guard is being triggered
docker compose logs -f auth-apigw | grep -i "insufficient\|forbidden"
```

## Expected Permission Flow

1. **JWT Token** contains permissions array with `'SystemSettings:Employeemanagement'`
2. **Permissions Guard** extracts required permissions from `@Permissions` decorator
3. **Role Service** validates user permissions against required permissions
4. **Access Granted** if permission found, **401/403** if not

## Most Likely Solutions

1. **Re-seed database** - This will ensure the permission exists and is assigned to Super Admin
2. **Get fresh token** - After re-seeding, get a new token that includes the new permissions
3. **Check permission string format** - Ensure the format matches between seed data and constants

Try the re-seeding approach first, as this is most likely to resolve the issue completely.
