# PowerShell script to configure <PERSON><PERSON> and create default bucket + policy

# Configuration
$BucketName = "applygoal-files"
$AccessKey = "minioadmin"
$SecretKey = "minioadmin123"
$MinioURL = "http://127.0.0.1:9000"  # Use IPv4 instead of localhost to avoid IPv6 issues
$AliasName = "local"
$MaxRetries = 10
$DelaySeconds = 3

# Function to wait until MinIO is reachable
function Wait-ForMinio {
    $retry = 0
    while ($retry -lt $MaxRetries) {
        try {
            $response = Invoke-WebRequest "$MinioURL/minio/health/ready" -UseBasicParsing -TimeoutSec 2
            if ($response.StatusCode -eq 200) {
                Write-Host "MinIO is reachable."
                return
            }
        } catch {
            Write-Host "Waiting for <PERSON><PERSON> to be ready... ($($retry + 1)/$MaxRetries)"
            Start-Sleep -Seconds $DelaySeconds
            $retry++
        }
    }
    Write-Error "MinIO is not reachable after $MaxRetries attempts."
    exit 1
}

# Wait until <PERSON><PERSON> is ready
Wait-ForMinio

# Add <PERSON><PERSON> alias
mc alias set $AliasName $MinioURL $AccessKey $SecretKey
if ($LASTEXITCODE -ne 0) {
    Write-Error "Failed to set <PERSON><PERSON> alias."
    exit 1
}

# Check if the bucket exists
$bucketExists = mc ls $AliasName 2>&1 | Select-String $BucketName

# Create bucket if it doesn't exist
if (-not $bucketExists) {
    mc mb "$AliasName/$BucketName"
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Created bucket: $BucketName"
    } else {
        Write-Error "Failed to create bucket: $BucketName"
        exit 1
    }
} else {
    Write-Host "Bucket '$BucketName' already exists."
}

# Set public read-only policy
mc anonymous set download "$AliasName/$BucketName"
if ($LASTEXITCODE -eq 0) {
    Write-Host "Set public read access to: $BucketName"
} else {
    Write-Error "Failed to set public read access."
    exit 1
}
