# Auth Service API Endpoints and Curl Commands

Base URL: `http://localhost:4006/api/auth`

## Authentication Endpoints

### 1. Register User

**Endpoint:** `POST /register`
**Description:** Register a new user
**Authentication:** Public

```bash
curl -X POST http://localhost:4006/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "name": "<PERSON>",
    "email": "<EMAIL>",
    "password": "Password123!",
    "phone": "******-0123",
    "nationality": "US",
    "organizationName": "ApplyGoal"
  }'
```

### 2. Login User

**Endpoint:** `POST /login`
**Description:** Login user and get access token
**Authentication:** Public

```bash
curl -X POST http://localhost:4006/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Password123!"
  }'
```

### 3. Logout User

**Endpoint:** `POST /logout`
**Description:** Logout user and invalidate token
**Authentication:** Required

```bash
curl -X POST http://localhost:4006/api/auth/logout \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 4. Validate Token

**Endpoint:** `GET /validate`
**Description:** Validate access token
**Authentication:** Required

```bash
curl -X GET http://localhost:4006/api/auth/validate \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 5. Refresh Token

**Endpoint:** `POST /refresh`
**Description:** Refresh access token using refresh token
**Authentication:** Public

```bash
curl -X POST http://localhost:4006/api/auth/refresh \
  -H "Content-Type: application/json" \
  -d '{
    "refreshToken": "YOUR_REFRESH_TOKEN"
  }'
```

### 6. SSO Login

**Endpoint:** `POST /sso`
**Description:** Login using SSO provider (Google)
**Authentication:** Public

```bash
curl -X POST http://localhost:4006/api/auth/sso \
  -H "Content-Type: application/json" \
  -d '{
    "provider": "google",
    "token": "GOOGLE_ID_TOKEN"
  }'
```

### 7. Generate OTP

**Endpoint:** `POST /generate-otp`
**Description:** Generate OTP for user
**Authentication:** Public

```bash
curl -X POST http://localhost:4006/api/auth/generate-otp \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "type": "email_verification"
  }'
```

### 8. Verify OTP

**Endpoint:** `POST /verify-otp`
**Description:** Verify OTP for user
**Authentication:** Public

```bash
curl -X POST http://localhost:4006/api/auth/verify-otp \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "otp": "123456"
  }'
```

## Role Management Endpoints

### 9. Create Role with Details

**Endpoint:** `POST /role`
**Description:** Create a new role with permissions
**Authentication:** Required (SS_ROLE_AND_PERMISSION_SETUP)

```bash
curl -X POST http://localhost:4006/api/auth/role \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Manager",
    "description": "Manager role with specific permissions",
    "permissions": [
      {"featureId": 1, "hasPermission": true},
      {"featureId": 2, "hasPermission": true}
    ]
  }'
```

### 10. Get Role Details by Name

**Endpoint:** `GET /role-details/{roleName}`
**Description:** Get role details by role name
**Authentication:** Required (SS_ROLE_AND_PERMISSION_SETUP)

```bash
curl -X GET http://localhost:4006/api/auth/role-details/Manager \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 11. Get All Roles with Details

**Endpoint:** `GET /roles`
**Description:** Get all roles with their details
**Authentication:** Required (SS_ROLE_AND_PERMISSION_SETUP)

```bash
curl -X GET http://localhost:4006/api/auth/roles \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 12. Rename Role

**Endpoint:** `PUT /role/{id}/{name}`
**Description:** Rename an existing role
**Authentication:** Required (SS_ROLE_AND_PERMISSION_SETUP)

```bash
curl -X PUT http://localhost:4006/api/auth/role/1/NewRoleName \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 13. Delete Role

**Endpoint:** `DELETE /role/{id}`
**Description:** Delete a role
**Authentication:** Required (SS_ROLE_AND_PERMISSION_SETUP)

```bash
curl -X DELETE http://localhost:4006/api/auth/role/1 \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 14. List Roles (Alternative)

**Endpoint:** `GET /roles`
**Description:** List roles with pagination and filters
**Authentication:** Required (SS_ROLE_AND_PERMISSION_SETUP)

```bash
curl -X GET "http://localhost:4006/api/auth/roles?organizationId=1&includeSystemRoles=true&page=1&limit=10" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 15. Get Role by ID

**Endpoint:** `GET /roles/{id}`
**Description:** Get specific role by ID
**Authentication:** Required (SS_ROLE_AND_PERMISSION_SETUP)

```bash
curl -X GET http://localhost:4006/api/auth/roles/1 \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 16. Update Role

**Endpoint:** `PUT /roles/{id}`
**Description:** Update role details
**Authentication:** Required (SS_ROLE_AND_PERMISSION_SETUP)

```bash
curl -X PUT http://localhost:4006/api/auth/roles/1 \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Updated Manager",
    "description": "Updated description",
    "permissions": [
      {"featureId": 1, "hasPermission": true}
    ]
  }'
```

## Organization Role Management

### 17. Create Organization Role

**Endpoint:** `POST /organizations/{orgId}/roles`
**Description:** Create role for specific organization
**Authentication:** Required (SS_ROLE_AND_PERMISSION_SETUP)

```bash
curl -X POST http://localhost:4006/api/auth/organizations/1/roles \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Org Manager",
    "description": "Organization specific manager role",
    "permissions": [
      {"featureId": 1, "hasPermission": true}
    ]
  }'
```

### 18. Get Organization Roles

**Endpoint:** `GET /organizations/{orgId}/roles`
**Description:** Get all roles for specific organization
**Authentication:** Required (SS_ROLE_AND_PERMISSION_SETUP)

```bash
curl -X GET "http://localhost:4006/api/auth/organizations/1/roles?includeSystemRoles=true&page=1&limit=10" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## Module Management Endpoints

### 19. Create Module

**Endpoint:** `POST /modules`
**Description:** Create a new module
**Authentication:** Required (SS_ROLE_AND_PERMISSION_SETUP)

```bash
curl -X POST http://localhost:4006/api/auth/modules \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "User Management",
    "description": "Module for user management features"
  }'
```

### 20. Bulk Create Modules

**Endpoint:** `POST /modules/bulk`
**Description:** Create multiple modules at once
**Authentication:** Required (SS_ROLE_AND_PERMISSION_SETUP)

```bash
curl -X POST http://localhost:4006/api/auth/modules/bulk \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "modules": [
      {"name": "User Management", "description": "User management features"},
      {"name": "Report Management", "description": "Report management features"}
    ]
  }'
```

### 21. List Modules

**Endpoint:** `GET /modules`
**Description:** Get all modules
**Authentication:** Required (SS_ROLE_AND_PERMISSION_SETUP)

```bash
curl -X GET http://localhost:4006/api/auth/modules \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## Department Management Endpoints

### 22. Create Department

**Endpoint:** `POST /departments`
**Description:** Create new departments (bulk)
**Authentication:** Required (SS_ROLE_AND_PERMISSION_SETUP)

```bash
curl -X POST http://localhost:4006/api/auth/departments \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "departments": [
      {"name": "Engineering", "parent": ""},
      {"name": "Frontend Team", "parent": "Engineering"},
      {"name": "Backend Team", "parent": "Engineering"}
    ]
  }'
```

### 23. Assign Department to User

**Endpoint:** `POST /departments/assign`
**Description:** Assign department to a user
**Authentication:** Required (SS_ROLE_AND_PERMISSION_SETUP)

```bash
curl -X POST http://localhost:4006/api/auth/departments/assign \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "userId": 1,
    "departmentId": 1
  }'
```

### 24. List Departments

**Endpoint:** `GET /departments`
**Description:** Get all departments
**Authentication:** Required (SS_ROLE_AND_PERMISSION_SETUP)

```bash
curl -X GET http://localhost:4006/api/auth/departments \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 25. List Departments with Users

**Endpoint:** `GET /departments-with-users`
**Description:** Get departments with their assigned users
**Authentication:** Required (SS_ROLE_AND_PERMISSION_SETUP)

```bash
curl -X GET http://localhost:4006/api/auth/departments-with-users \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## Organization Department Management

### 26. Create Organization Department

**Endpoint:** `POST /organizations/{orgId}/departments`
**Description:** Create department for specific organization
**Authentication:** Required (SS_ROLE_AND_PERMISSION_SETUP)

```bash
curl -X POST http://localhost:4006/api/auth/organizations/1/departments \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "HR Department",
    "description": "Human Resources Department",
    "parentId": null
  }'
```

### 27. Get Organization Departments

**Endpoint:** `GET /organizations/{orgId}/departments`
**Description:** Get departments for specific organization
**Authentication:** Required (SS_ROLE_AND_PERMISSION_SETUP)

```bash
curl -X GET "http://localhost:4006/api/auth/organizations/1/departments?includeHierarchy=true" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 28. List Organization Departments

**Endpoint:** `GET /org-departments`
**Description:** List organization departments with filters
**Authentication:** Required (SS_ROLE_AND_PERMISSION_SETUP)

```bash
curl -X GET "http://localhost:4006/api/auth/org-departments?organizationId=1&page=1&limit=10" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 29. Get Organization Department by ID

**Endpoint:** `GET /org-departments/{id}`
**Description:** Get specific organization department
**Authentication:** Required (SS_ROLE_AND_PERMISSION_SETUP)

```bash
curl -X GET http://localhost:4006/api/auth/org-departments/1 \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 30. Update Organization Department

**Endpoint:** `PUT /org-departments/{id}`
**Description:** Update organization department
**Authentication:** Required (SS_ROLE_AND_PERMISSION_SETUP)

```bash
curl -X PUT http://localhost:4006/api/auth/org-departments/1 \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Updated HR Department",
    "description": "Updated Human Resources Department"
  }'
```

### 31. Delete Organization Department

**Endpoint:** `DELETE /org-departments/{id}`
**Description:** Delete organization department
**Authentication:** Required (SS_ROLE_AND_PERMISSION_SETUP)

````bash
curl -X DELETE http://localhost:4006/api/auth/org-departments/1 \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

## Employee Management Endpoints

### 32. Create Employee
**Endpoint:** `POST /employees`
**Description:** Create a new employee with user account
**Authentication:** Required (SS_EMPLOYEE_MANAGEMENT)

```bash
curl -X POST http://localhost:4006/api/auth/employees \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Employee123!",
    "name": "John Employee",
    "phone": "******-0123",
    "departmentName": "Engineering",
    "organizationName": "ApplyGoal",
    "employeeRoleName": "Employee",
    "firstName": "John",
    "lastName": "Employee",
    "jobType": "full-time",
    "jobStatus": "active",
    "dateOfBirth": "1990-01-01",
    "bloodGroup": "O+",
    "gender": "male"
  }'
````

### 33. Get Employee by ID

**Endpoint:** `GET /employees/{id}`
**Description:** Get employee details by ID
**Authentication:** Required (SS_EMPLOYEE_MANAGEMENT)

```bash
curl -X GET http://localhost:4006/api/auth/employees/1 \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 34. List Employees

**Endpoint:** `GET /employees`
**Description:** Get all employees with pagination
**Authentication:** Required (SS_EMPLOYEE_MANAGEMENT)

```bash
curl -X GET "http://localhost:4006/api/auth/employees?page=1&limit=10&search=john" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 35. Update Employee

**Endpoint:** `PUT /employees/{id}`
**Description:** Update employee details
**Authentication:** Required (SS_EMPLOYEE_MANAGEMENT)

```bash
curl -X PUT http://localhost:4006/api/auth/employees/1 \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "firstName": "Updated John",
    "lastName": "Updated Employee",
    "jobType": "part-time",
    "jobStatus": "active"
  }'
```

### 36. Delete Employee

**Endpoint:** `DELETE /employees/{id}`
**Description:** Remove employee
**Authentication:** Required (SS_EMPLOYEE_MANAGEMENT)

```bash
curl -X DELETE http://localhost:4006/api/auth/employees/1 \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## Password Reset Endpoints

### 37. Forgot Password

**Endpoint:** `POST /forgot-password`
**Description:** Request password reset
**Authentication:** Public

```bash
curl -X POST http://localhost:4006/api/auth/forgot-password \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>"
  }'
```

### 38. Verify Reset Token

**Endpoint:** `POST /verify-reset-token`
**Description:** Verify password reset token
**Authentication:** Public

```bash
curl -X POST http://localhost:4006/api/auth/verify-reset-token \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "resetToken": "RESET_TOKEN_HERE"
  }'
```

### 39. Reset Password

**Endpoint:** `POST /reset-password`
**Description:** Reset password using token
**Authentication:** Public

```bash
curl -X POST http://localhost:4006/api/auth/reset-password \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "resetToken": "RESET_TOKEN_HERE",
    "newPassword": "NewPassword123!"
  }'
```

## Additional Endpoints

### 40. Get Metrics

**Endpoint:** `GET /metrics`
**Description:** Get application metrics (Prometheus format)
**Authentication:** Required

```bash
curl -X GET http://localhost:4006/api/metrics \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 41. File Upload

**Endpoint:** `POST /upload`
**Description:** Upload files
**Authentication:** Required

```bash
curl -X POST http://localhost:4006/api/upload \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -F "file=@/path/to/your/file.jpg"
```

## Notes

1. **Authentication**: Most endpoints require a valid JWT token in the Authorization header
2. **Permissions**: Some endpoints require specific permissions (e.g., SS_ROLE_AND_PERMISSION_SETUP, SS_EMPLOYEE_MANAGEMENT)
3. **Base URL**: All endpoints are prefixed with `http://localhost:4006/api/auth`
4. **Content-Type**: POST/PUT requests typically require `Content-Type: application/json`
5. **Response Format**: All responses follow a consistent format with `success`, `message`, and data fields

## Testing Flow

1. Register a user or use existing credentials
2. Login to get access token
3. Use the token to access protected endpoints
4. Test department and role management flows
5. Test employee management flows

```

```
