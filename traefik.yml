# Traefik Static Configuration
# Enable API
api:
  dashboard: true
  insecure: true

# Enable entrypoints
entryPoints:
  web:
    address: ":80"
    # Enable access logs for web entrypoint
    forwardedHeaders:
      insecure: true
  metrics:
    address: ":8082"
  dashboard:
    address: ":8081"

# Global HTTP configuration
global:
  checkNewVersion: true
  sendAnonymousUsage: false

# Enable access logs
accessLog:
  filePath: "/var/log/traefik/access.log"
  format: json
  bufferingSize: 100
  fields:
    defaultMode: keep
    headers:
      defaultMode: drop
      names:
        User-Agent: keep
        Authorization: redact
        Content-Type: keep

# Enable metrics
metrics:
  prometheus:
    entryPoint: metrics
    addEntryPointsLabels: true
    addServicesLabels: true
    buckets:
      - 0.1
      - 0.3
      - 1.2
      - 5.0

# Enable tracing
tracing:
  jaeger:
    samplingServerURL: "http://jaeger:16686/sampling"
    samplingType: "const"
    samplingParam: 1.0
    localAgentHostPort: "jaeger:6831"
    propagation: "jaeger"
    traceContextHeaderName: "uber-trace-id"

# Configure log levels
log:
  level: INFO
  format: json
  filePath: "/var/log/traefik/traefik.log"

providers:
  docker:
    exposedByDefault: false
    network: proxy
    watch: true

# certificatesResolvers:
#   letsencrypt:
#     acme:
#       email: "<EMAIL>"
#       storage: "/etc/traefik/acme.json"
#       httpChallenge:
#         entryPoint: web






