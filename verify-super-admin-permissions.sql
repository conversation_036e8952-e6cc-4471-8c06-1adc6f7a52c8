-- Super Admin Permissions Verification Script
-- Run this after seeding to verify Super Admin has all required permissions

-- 1. Check if Super Admin role exists
SELECT 'Super Admin Role Check' as check_type, 
       CASE WHEN COUNT(*) > 0 THEN 'PASS' ELSE 'FAIL' END as status,
       COUNT(*) as count
FROM roles 
WHERE name = 'Super Admin';

-- 2. Check if Employee management feature exists
SELECT 'Employee Management Feature Check' as check_type,
       CASE WHEN COUNT(*) > 0 THEN 'PASS' ELSE 'FAIL' END as status,
       COUNT(*) as count
FROM features 
WHERE name = 'Employee management';

-- 3. Check Super Admin has Employee management permission
SELECT 'Super Admin Employee Management Permission' as check_type,
       CASE WHEN COUNT(*) > 0 THEN 'PASS' ELSE 'FAIL' END as status,
       COUNT(*) as count
FROM roles r
JOIN role_feature_permissions rfp ON r.id = rfp.roleId
JOIN features f ON rfp.featureId = f.id
WHERE r.name = 'Super Admin' 
AND f.name = 'Employee management'
AND rfp.hasPermission = true;

-- 4. Count total permissions for Super Admin
SELECT 'Super Admin Total Permissions Count' as check_type,
       'INFO' as status,
       COUNT(*) as count
FROM roles r
JOIN role_feature_permissions rfp ON r.id = rfp.roleId
JOIN features f ON rfp.featureId = f.id
WHERE r.name = 'Super Admin' 
AND rfp.hasPermission = true;

-- 5. List all modules Super Admin has access to
SELECT 'Super Admin Module Access' as check_type,
       m.name as module_name,
       COUNT(f.id) as features_count
FROM roles r
JOIN role_feature_permissions rfp ON r.id = rfp.roleId
JOIN features f ON rfp.featureId = f.id
JOIN modules m ON f.moduleId = m.id
WHERE r.name = 'Super Admin' 
AND rfp.hasPermission = true
GROUP BY m.name
ORDER BY m.name;

-- 6. Check if Super Admin user exists and has employee record
SELECT 'Super Admin User & Employee Check' as check_type,
       CASE 
         WHEN u.id IS NOT NULL AND ep.userId IS NOT NULL THEN 'PASS - User and Employee exist'
         WHEN u.id IS NOT NULL AND ep.userId IS NULL THEN 'PARTIAL - User exists but no Employee record'
         ELSE 'FAIL - No Super Admin user'
       END as status,
       u.id as user_id,
       ep.userId as employee_user_id
FROM users u
LEFT JOIN employee_personal ep ON u.id = ep.userId
WHERE u.name = 'Super Admin';

-- 7. Detailed Super Admin permissions by module
SELECT 
    m.name as module_name,
    f.name as feature_name,
    rfp.hasPermission as has_permission
FROM roles r
JOIN role_feature_permissions rfp ON r.id = rfp.roleId
JOIN features f ON rfp.featureId = f.id
JOIN modules m ON f.moduleId = m.id
WHERE r.name = 'Super Admin'
ORDER BY m.name, f.name;

-- 8. Check for any missing critical permissions (should return 0 rows)
SELECT 'Missing Critical Permissions' as check_type,
       'These permissions are missing for Super Admin:' as status,
       m.name as module_name,
       f.name as feature_name
FROM modules m
JOIN features f ON m.id = f.moduleId
LEFT JOIN (
    SELECT f.id as feature_id
    FROM roles r
    JOIN role_feature_permissions rfp ON r.id = rfp.roleId
    JOIN features f ON rfp.featureId = f.id
    WHERE r.name = 'Super Admin' AND rfp.hasPermission = true
) sa_perms ON f.id = sa_perms.feature_id
WHERE sa_perms.feature_id IS NULL
ORDER BY m.name, f.name;

-- 9. Summary report
SELECT 
    'SUMMARY REPORT' as report_type,
    (SELECT COUNT(*) FROM modules) as total_modules,
    (SELECT COUNT(*) FROM features) as total_features,
    (SELECT COUNT(*) 
     FROM roles r
     JOIN role_feature_permissions rfp ON r.id = rfp.roleId
     WHERE r.name = 'Super Admin' AND rfp.hasPermission = true
    ) as super_admin_permissions,
    (SELECT CASE WHEN COUNT(*) > 0 THEN 'YES' ELSE 'NO' END
     FROM users u
     JOIN employee_personal ep ON u.id = ep.userId
     WHERE u.name = 'Super Admin'
    ) as has_employee_record;
