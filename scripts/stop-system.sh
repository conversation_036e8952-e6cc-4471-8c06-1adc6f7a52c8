#!/bin/bash

# Color definitions
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored status messages
print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Clear screen and show banner
clear
print_status "$BLUE" "=================================="
print_status "$BLUE" "   ApplyGoal System Shutdown"
print_status "$BLUE" "=================================="

# Shutdown all services
print_status "$YELLOW" "\nStopping all services..."
docker compose down

# Optional: Clean up volumes
read -p "Do you want to clean up volumes? (y/N) " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]
then
    print_status "$YELLOW" "\nCleaning up volumes..."
    docker compose down -v
    docker volume prune -f
fi

# Optional: Clean up images
read -p "Do you want to clean up unused images? (y/N) " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]
then
    print_status "$YELLOW" "\nCleaning up images..."
    docker image prune -a -f
fi

print_status "$GREEN" "\n=================================="
print_status "$GREEN" "   System Shutdown Complete"
print_status "$GREEN" "=================================="