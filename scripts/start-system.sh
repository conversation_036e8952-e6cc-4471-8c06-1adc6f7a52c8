#!/bin/bash

# Color definitions
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored status messages
print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to check if a service is healthy
check_service_health() {
    local service=$1
    local max_attempts=$2
    local attempt=1

    print_status "$YELLOW" "Waiting for $service to be healthy..."
    
    while [ $attempt -le $max_attempts ]; do
        if docker compose ps $service | grep -q "(healthy)"; then
            print_status "$GREEN" "✓ $service is healthy"
            return 0
        fi
        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    print_status "$RED" "✗ $service failed to become healthy"
    return 1
}

# Function to start services and wait for health
start_services() {
    local services=("$@")
    local failed=false

    for service in "${services[@]}"; do
        print_status "$BLUE" "\nStarting $service..."
        docker compose up -d $service
        
        if ! check_service_health $service 30; then
            failed=true
        fi
    done

    if [ "$failed" = true ]; then
        print_status "$RED" "\nSome services failed to start properly"
        exit 1
    fi
}

# Main script

# Clear screen and show banner
clear
print_status "$BLUE" "=================================="
print_status "$BLUE" "   ApplyGoal System Startup"
print_status "$BLUE" "=================================="

# 1. Start Infrastructure Services
print_status "$YELLOW" "\n[Phase 1/4] Starting Infrastructure Services..."
infrastructure_services=(
    "postgres"
    "redis"
    "rabbitmq"
    "mongodb-chat"
)
start_services "${infrastructure_services[@]}"

# 2. Start Core Services
print_status "$YELLOW" "\n[Phase 2/4] Starting Core Services..."
core_services=(
    "audit-logging"
    "identity-service"
    "payment-service"
)
start_services "${core_services[@]}"

# 3. Start Domain Services
print_status "$YELLOW" "\n[Phase 3/4] Starting Domain Services..."
domain_services=(
    "agency-service"
    "analytics-service"
    "auth-service"
    "events-meetings-service"
    "help-service"
    "messaging-service"
    "settings-service"
    "students-service"
    "university-service"
)
start_services "${domain_services[@]}"

# 4. Start API Gateways
print_status "$YELLOW" "\n[Phase 4/4] Starting API Gateways..."
gateway_services=(
    "agency-apigw"
    "manager-apigw"
    "student-apigw"
    "super-admin-apigw"
    "university-apigw"
)
start_services "${gateway_services[@]}"

# Final status
print_status "$GREEN" "\n=================================="
print_status "$GREEN" "   All Services Started Successfully"
print_status "$GREEN" "=================================="

# Print service access information
print_status "$BLUE" "\nService Access Information:"
print_status "$NC" "- Agency API:      http://agency-api.localhost"
print_status "$NC" "- Manager API:     http://manager-api.localhost"
print_status "$NC" "- Student API:     http://student-api.localhost"
print_status "$NC" "- Admin API:       http://admin-api.localhost"
print_status "$NC" "- University API:  http://university-api.localhost"
print_status "$NC" "- Traefik Dashboard: http://traefik.localhost:8080"

# Print monitoring command
print_status "$BLUE" "\nTo monitor all services:"
print_status "$NC" "docker compose logs -f"

# Print shutdown command
print_status "$BLUE" "\nTo shut down all services:"
print_status "$NC" "docker compose down"
