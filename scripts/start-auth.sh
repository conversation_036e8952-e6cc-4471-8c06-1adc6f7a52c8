#!/bin/bash
# chmod +x ./scripts/start-auth.sh
# ./scripts/start-auth.sh



set -e

echo "♻️ Cleaning up Docker environment..."
docker compose down -v
docker system prune -f
docker volume prune -f
docker network prune -f
docker image prune -a -f

echo "🚀 Starting setup..."

# Step 1: Start monitoring tools
echo "📊 Starting monitoring services: jaeger, prometheus, grafana..."
docker compose up -d jaeger prometheus grafana

# Step 2: Start reverse proxy
echo "🌐 Starting Traefik reverse proxy..."
docker compose up -d traefik

# Step 3: Start infrastructure services
echo "🧱 Starting core services: postgres, redis, rabbitmq, mongodb-chat..."
docker compose up -d postgres redis rabbitmq mongodb-chat

# Step 4: Wait for services to be healthy
echo "⏳ Waiting for dependent services to become healthy..."
docker compose wait || echo "⚠️ 'docker compose wait' is not supported, continuing anyway..."

# Step 5: Start app microservices
echo "🔧 Starting services: audit-logging, messaging-service, auth-service, auth-apigw..."
docker compose up -d audit-logging messaging-service auth-service auth-apigw

echo "✅ Setup complete! All services are up and running."

# Optional: Show running containers
docker ps
