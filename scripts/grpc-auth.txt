Absolutely! Since your `.proto` file is located at:

```
libs/shared/dto/src/lib/auth/auth.proto
```

I'll update the `grpcurl` commands accordingly with that path.

---

## ✅ **gRPC Calls Using Local Proto File**

All commands use:

* Proto path: `libs/shared/dto/src/lib/auth/auth.proto`
* Target server: `localhost:50052`
* Package: `auth`
* Service: `AuthService`

---

### 🔐 **Authentication**

```bash
grpcurl -proto libs/shared/dto/src/lib/auth/auth.proto -plaintext -d '{"firstName": "ridwan", "lastName": "hasan", "email": "<EMAIL>", "password": "secret", "phone":"+8801763873729", "socialLink":"", "socialToken":"", "roleName":"University Admin", "ipAddress":"127.0.0.1", "userAgent":""}' localhost:50052 auth.AuthService/Register

grpcurl -proto libs/shared/dto/src/lib/auth/auth.proto -plaintext -d '{"username": "john", "password": "pass"}' localhost:50052 auth.AuthService/Login

grpcurl -proto libs/shared/dto/src/lib/auth/auth.proto -plaintext -d '{"accessToken": "some-token"}' localhost:50052 auth.AuthService/Logout

grpcurl -proto libs/shared/dto/src/lib/auth/auth.proto -plaintext -d '{"token": "some-token"}' localhost:50052 auth.AuthService/ValidateToken

grpcurl -proto libs/shared/dto/src/lib/auth/auth.proto -plaintext -d '{"refreshToken": "some-refresh"}' localhost:50052 auth.AuthService/RefreshToken


```

---

### 👤 **User Management**

```bash
grpcurl -proto libs/shared/dto/src/lib/auth/auth.proto -plaintext -d '{"username":"john","first_name":"John","last_name":"Doe","email":"<EMAIL>","password":"secret"}' localhost:50052 auth.AuthService/CreateUser

grpcurl -proto libs/shared/dto/src/lib/auth/auth.proto -plaintext -d '{"id": 1}' localhost:50052 auth.AuthService/GetUser

grpcurl -proto libs/shared/dto/src/lib/auth/auth.proto -plaintext -d '{"id": 1, "first_name": "Johnny"}' localhost:50052 auth.AuthService/UpdateUser

grpcurl -proto libs/shared/dto/src/lib/auth/auth.proto -plaintext -d '{"id": 1}' localhost:50052 auth.AuthService/DeleteUser

grpcurl -proto libs/shared/dto/src/lib/auth/auth.proto -plaintext -d '{"page": 1, "limit": 10}' localhost:50052 auth.AuthService/ListUsers
```

---

### 🛡️ **Role Management**

```bash
grpcurl -proto libs/shared/dto/src/lib/auth/auth.proto -plaintext -d '{"name":"admin","status":"active"}' localhost:50052 auth.AuthService/CreateRole

grpcurl -proto libs/shared/dto/src/lib/auth/auth.proto -plaintext -d '{"id":1}' localhost:50052 auth.AuthService/GetRole

grpcurl -proto libs/shared/dto/src/lib/auth/auth.proto -plaintext -d '{"id":1,"name":"superadmin"}' localhost:50052 auth.AuthService/UpdateRole

grpcurl -proto libs/shared/dto/src/lib/auth/auth.proto -plaintext -d '{"id":1}' localhost:50052 auth.AuthService/DeleteRole

grpcurl -proto libs/shared/dto/src/lib/auth/auth.proto -plaintext -d '{"page":1,"limit":5}' localhost:50052 auth.AuthService/ListRoles
```

---

### 🔏 **Permission Management**

```bash
grpcurl -proto libs/shared/dto/src/lib/auth/auth.proto -plaintext -d '{"name":"read:users","status":"active"}' localhost:50052 auth.AuthService/CreatePermission

grpcurl -proto libs/shared/dto/src/lib/auth/auth.proto -plaintext -d '{"id":1}' localhost:50052 auth.AuthService/GetPermission

grpcurl -proto libs/shared/dto/src/lib/auth/auth.proto -plaintext -d '{"id":1,"name":"write:users"}' localhost:50052 auth.AuthService/UpdatePermission

grpcurl -proto libs/shared/dto/src/lib/auth/auth.proto -plaintext -d '{"id":1}' localhost:50052 auth.AuthService/DeletePermission

grpcurl -proto libs/shared/dto/src/lib/auth/auth.proto -plaintext -d '{"page":1,"limit":5}' localhost:50052 auth.AuthService/ListPermissions
```

---

### 👥 **User-Role Management**

```bash
grpcurl -proto libs/shared/dto/src/lib/auth/auth.proto -plaintext -d '{"user_id":1,"role_id":2}' localhost:50052 auth.AuthService/AssignRoleToUser

grpcurl -proto libs/shared/dto/src/lib/auth/auth.proto -plaintext -d '{"user_id":1,"role_id":2}' localhost:50052 auth.AuthService/RemoveRoleFromUser
```

---

### 🔐 **Role-Permission Management**

```bash
grpcurl -proto libs/shared/dto/src/lib/auth/auth.proto -plaintext -d '{"role_id":2,"permission_id":5}' localhost:50052 auth.AuthService/AssignPermissionToRole

grpcurl -proto libs/shared/dto/src/lib/auth/auth.proto -plaintext -d '{"role_id":2,"permission_id":5}' localhost:50052 auth.AuthService/RemovePermissionFromRole
```

---

If you're using `protoPath` dependencies (i.e. other imported `.proto` files), you may need to add `-import-path` like:

```bash
-import-path libs/shared/dto/src
```

Let me know if you'd like a script for automating all these requests 🧪💻.
