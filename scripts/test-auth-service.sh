#!/bin/bash

# CONFIGURATION
PROTO_FILE="$(pwd)/libs/shared/dto/src/lib/auth/auth.proto"
GRPC_SERVER="localhost:50052"
SERVICE="auth.AuthService"
PLAIN="--plaintext"

echo "🔄 Starting gRPC tests against $GRPC_SERVER using $PROTO_FILE"
echo "============================================================"

# LOGIN
echo "🔐 Login"
grpcurl $PLAIN -proto $PROTO_FILE -d '{"username": "john", "password": "pass"}' $GRPC_SERVER $SERVICE/Login
echo ""

# LOGOUT
echo "🚪 Logout"
grpcurl $PLAIN -proto $PROTO_FILE -d '{"access_token": "sample-token"}' $GRPC_SERVER $SERVICE/Logout
echo ""

# VALIDATE TOKEN
echo "🔍 Validate Token"
grpcurl $PLAIN -proto $PROTO_FILE -d '{"token": "sample-token"}' $GRPC_SERVER $SERVICE/ValidateToken
echo ""

# REFRESH TOKEN
echo "♻️ Refresh Token"
grpcurl $PLAIN -proto $PROTO_FILE -d '{"refresh_token": "sample-refresh"}' $GRPC_SERVER $SERVICE/RefreshToken
echo ""

# CREATE USER
echo "👤 Create User"
grpcurl $PLAIN -proto $PROTO_FILE -d '{
  "username":"john",
  "first_name":"John",
  "last_name":"Doe",
  "email":"<EMAIL>",
  "password":"secret"
}' $GRPC_SERVER $SERVICE/CreateUser
echo ""

# GET USER
echo "📄 Get User"
grpcurl $PLAIN -proto $PROTO_FILE -d '{"id": 1}' $GRPC_SERVER $SERVICE/GetUser
echo ""

# LIST USERS
echo "📜 List Users"
grpcurl $PLAIN -proto $PROTO_FILE -d '{"page": 1, "limit": 5}' $GRPC_SERVER $SERVICE/ListUsers
echo ""

# CREATE ROLE
echo "🛡️ Create Role"
grpcurl $PLAIN -proto $PROTO_FILE -d '{"name": "admin", "status": "active"}' $GRPC_SERVER $SERVICE/CreateRole
echo ""

# LIST ROLES
echo "📜 List Roles"
grpcurl $PLAIN -proto $PROTO_FILE -d '{"page": 1, "limit": 5}' $GRPC_SERVER $SERVICE/ListRoles
echo ""

# CREATE PERMISSION
echo "🔐 Create Permission"
grpcurl $PLAIN -proto $PROTO_FILE -d '{"name": "read:users", "status": "active"}' $GRPC_SERVER $SERVICE/CreatePermission
echo ""

# ASSIGN ROLE TO USER
echo "📌 Assign Role to User"
grpcurl $PLAIN -proto $PROTO_FILE -d '{"user_id": 1, "role_id": 2}' $GRPC_SERVER $SERVICE/AssignRoleToUser
echo ""

# ASSIGN PERMISSION TO ROLE
echo "📌 Assign Permission to Role"
grpcurl $PLAIN -proto $PROTO_FILE -d '{"role_id": 2, "permission_id": 1}' $GRPC_SERVER $SERVICE/AssignPermissionToRole
echo ""

echo "✅ gRPC tests complete."
