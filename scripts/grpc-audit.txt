Absolutely ✅ — here are the **standalone `grpcurl` commands** (no shell variables) for each RPC method in the `AuditService` using `audit.proto` located at `libs/shared/dto/src/lib/audit/audit.proto`.

Assumptions:

* gRPC server: `localhost:50051`
* Service name: `audit.AuditService`
* No TLS (`-plaintext`)
* Full path to `.proto` file used in each command

---

### 🔹 CreateAuditLog

```bash
grpcurl -plaintext \
  -proto libs/shared/dto/src/lib/audit/audit.proto \
  -d '{
    "userId": "123",
    "userRole": "admin",
    "actions": "LOGIN",
    "serviceName": "auth-service",
    "resourceType": "USER",
    "resourceId": "123",
    "description": "User logged in",
    "metadata": {
      "ipAddress": "127.0.0.1",
      "device": "mac"
    },
    "ipAddress": "127.0.0.1",
    "userAgent": "PostmanRuntime/7.32.2",
    "source": "frontend"
  }' \
  localhost:50051 audit.AuditService/CreateAuditLog
```

---

### 🔹 GetAuditLog

```bash
grpcurl -plaintext \
  -proto libs/shared/dto/src/lib/audit/audit.proto \
  -d '{"id": "audit-log-id"}' \
  localhost:50051 audit.AuditService/GetAuditLog
```

---

### 🔹 ListAuditLogs

```bash
grpcurl -plaintext \
  -proto libs/shared/dto/src/lib/audit/audit.proto \
  -d '{
    "pageSize": 10,
    "pageToken": "",
    "orderBy": "createdAt desc"
  }' \
  localhost:50051 audit.AuditService/ListAuditLogs
```

---

### 🔹 SearchAuditLogs

```bash
grpcurl -plaintext \
  -proto libs/shared/dto/src/lib/audit/audit.proto \
  -d '{
    "userId": "123",
    "serviceName": "auth-service",
    "action": "LOGIN",
    "resourceType": "USER",
    "resourceId": "123",
    "startDate": 1672531200000,
    "endDate": 1680000000000,
    "pageSize": 10,
    "pageToken": ""
  }' \
  localhost:50051 audit.AuditService/SearchAuditLogs
```
