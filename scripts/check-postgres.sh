#!/bin/bash

# Color definitions
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Database list
DATABASES=(
    "audit_db"
    "identity_db"
    "payment_db"
    "agency_db"
    "analytics_db"
    "auth_db"
    "events_db"
    "help_db"
    "settings_db"
    "students_db"
    "university_db"
)

echo -e "${YELLOW}Checking PostgreSQL connection...${NC}"

# Check if PostgreSQL container is running
if ! docker compose ps postgres | grep -q "Up"; then
    echo -e "${RED}PostgreSQL container is not running!${NC}"
    exit 1
fi

# Check if PostgreSQL is accepting connections
if ! docker compose exec postgres pg_isready -U postgres; then
    echo -e "${RED}PostgreSQL is not ready to accept connections!${NC}"
    exit 1
fi

echo -e "${GREEN}PostgreSQL is running and accepting connections${NC}"
echo -e "\n${YELLOW}Checking databases...${NC}"

# Check each database
for db in "${DATABASES[@]}"; do
    echo -n "Checking $db... "
    if docker compose exec postgres psql -U postgres -lqt | cut -d \| -f 1 | grep -qw "$db"; then
        # Try to connect to the database
        if docker compose exec postgres psql -U postgres -d "$db" -c '\q' > /dev/null 2>&1; then
            echo -e "${GREEN}OK${NC}"
        else
            echo -e "${RED}EXISTS BUT CANNOT CONNECT${NC}"
        fi
    else
        echo -e "${RED}NOT FOUND${NC}"
    fi
done

# Show database sizes
echo -e "\n${YELLOW}Database Sizes:${NC}"
docker compose exec postgres psql -U postgres -c "
SELECT 
    datname as database,
    pg_size_pretty(pg_database_size(datname)) as size
FROM pg_database
WHERE datname IN ($(printf "'%s'," "${DATABASES[@]}" | sed 's/,$//'))"

# Show user access
echo -e "\n${YELLOW}Database Users:${NC}"
docker compose exec postgres psql -U postgres -c "
SELECT 
    usename as username,
    datname as database
FROM pg_user
JOIN pg_database ON true
WHERE datname IN ($(printf "'%s'," "${DATABASES[@]}" | sed 's/,$//'))"

echo -e "\n${YELLOW}Connection Info:${NC}"
echo "Host: localhost"
echo "Port: 5433"
echo "Default User: postgres"


# chmod +x scripts/check-postgres.sh
# ./scripts/check-postgres.sh