#!/usr/bin/env node

/**
 * Run Services Script
 * 
 * This script allows running multiple services with proper port configuration.
 * Usage: node scripts/run-services.js service1 service2 service3
 * 
 * Example: node scripts/run-services.js agency-service auth-service agency-apigw
 */

const { spawn } = require('child_process');
const SERVICE_PORTS = require('./service-ports');

// Get services from command line arguments
const services = process.argv.slice(2);

if (services.length === 0) {
  console.log('Please specify at least one service to run.');
  console.log('Usage: node scripts/run-services.js service1 service2 service3');
  console.log('\nAvailable services:');
  Object.keys(SERVICE_PORTS).forEach(service => {
    console.log(`- ${service} (port: ${SERVICE_PORTS[service]})`);
  });
  process.exit(1);
}

// Validate services
const invalidServices = services.filter(service => !SERVICE_PORTS[service]);
if (invalidServices.length > 0) {
  console.error(`Error: Unknown service(s): ${invalidServices.join(', ')}`);
  console.log('\nAvailable services:');
  Object.keys(SERVICE_PORTS).forEach(service => {
    console.log(`- ${service} (port: ${SERVICE_PORTS[service]})`);
  });
  process.exit(1);
}

// Run each service
console.log(`Starting ${services.length} service(s)...`);

const processes = services.map(service => {
  const port = SERVICE_PORTS[service];
  console.log(`Starting ${service} on port ${port}...`);
  
  const process = spawn('npx', ['nx', 'serve', service, `--port=${port}`], {
    stdio: 'pipe',
    shell: true
  });
  
  // Prefix output with service name
  process.stdout.on('data', (data) => {
    const lines = data.toString().split('\n');
    lines.forEach(line => {
      if (line.trim()) {
        console.log(`[${service}] ${line}`);
      }
    });
  });
  
  process.stderr.on('data', (data) => {
    const lines = data.toString().split('\n');
    lines.forEach(line => {
      if (line.trim()) {
        console.error(`[${service}] ${line}`);
      }
    });
  });
  
  process.on('close', (code) => {
    console.log(`[${service}] process exited with code ${code}`);
  });
  
  return { service, process };
});

// Handle process termination
process.on('SIGINT', () => {
  console.log('\nShutting down all services...');
  processes.forEach(({ service, process }) => {
    console.log(`Stopping ${service}...`);
    process.kill('SIGINT');
  });
  process.exit(0);
});
