/**
 * Service Ports Configuration
 * 
 * This file documents the port assignments for all services in the ApplyGoal backend.
 * 
 * Port Ranges:
 * - 3000: Main application
 * - 3001-3099: Core services
 * - 4001-4099: API Gateways
 * - 5001-5099: Domain services
 */

const SERVICE_PORTS = {
  // Main application
  'apply-goal-backend': 3000,
  
  // Core services (3001-3099)
  'audit-logging': 3001,
  'identity-service': 3002,
  'payment-service': 3003,
  
  // API Gateways (4001-4099)
  'agency-apigw': 4001,
  'manager-apigw': 4002,
  'student-apigw': 4003,
  'super-admin-apigw': 4004,
  'university-apigw': 4005,
  
  // Domain services (5001-5099)
  'agency-service': 5001,
  'analytics-service': 5002,
  'auth-service': 5003,
  'events-meetings-service': 5004,
  'help-service': 5005,
  'messaging-service': 5006,
  'settings-service': 5007,
  'students-service': 5008,
  'university-service': 5009
};

module.exports = SERVICE_PORTS;
