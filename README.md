# ApplyGoal Backend

<a alt="Nx logo" href="https://nx.dev" target="_blank" rel="noreferrer"><img src="https://raw.githubusercontent.com/nrwl/nx/master/images/nx-logo.png" width="45"></a>

ApplyGoal Backend is a microservices-based application built with NestJS and organized using Nx workspace. This project provides the backend infrastructure for an educational application focused on university applications and student management.

## Table of Contents

- [ApplyGoal Backend](#applygoal-backend)
  - [Table of Contents](#table-of-contents)
  - [Project Overview](#project-overview)
  - [Architecture](#architecture)
  - [Getting Started](#getting-started)
    - [Prerequisites](#prerequisites)
    - [Installation](#installation)
    - [Running the Application](#running-the-application)
    - [Additional Documentation](#additional-documentation)
  - [Services](#services)
    - [Core Services](#core-services)
    - [API Gateways](#api-gateways)
    - [Domain Services](#domain-services)
  - [Development](#development)
    - [Running Specific Services](#running-specific-services)
    - [Adding New Services](#adding-new-services)
    - [Testing](#testing)
  - [Project Structure](#project-structure)
  - [Deployment](#deployment)
    - [Building for Production](#building-for-production)
    - [CI/CD](#cicd)
  - [Contributing](#contributing)
  - [Useful Resources](#useful-resources)

## Project Overview

ApplyGoal Backend is designed to facilitate:

- Student application processes to universities
- Agency management for educational consultants
- University administration
- Payment processing for services
- Communication between stakeholders
- Event scheduling and management
- Analytics for tracking outcomes and performance

## Architecture

The application follows a microservices architecture with the following components:

- **Core Services**: Fundamental services that provide core functionality
- **API Gateways**: Entry points for different user types
- **Domain Services**: Business logic services for specific domains
- **Shared Libraries**: Common code shared across services

The microservices architecture allows different components to be developed, deployed, and scaled independently.

## Getting Started

### Prerequisites

- Node.js (v18 or later)
- npm (v8 or later)
- Git

### Installation

1. Clone the repository:

```sh
git clone https://github.com/your-username/apply-goal-backend.git
cd apply-goal-backend
```

2. Install dependencies:

```sh
npm install
```

### Running the Application

To run the entire application:

```sh
npm run start:all
```

To run just the main application:

```sh
npm start
# or
npx nx serve apply-goal-backend
```

The application will be available at `http://localhost:3000/api`.

### Additional Documentation

- [QUICK-START.md](QUICK-START.md) - Quick start guide for new developers
- [DEVELOPMENT.md](DEVELOPMENT.md) - Detailed development guide
- [SERVICES.md](SERVICES.md) - Information about running specific services

## Services

For detailed information about running specific services, see [SERVICES.md](SERVICES.md).

### Core Services

- **Audit Logging** (Port: 3001): Tracks system activities and changes
- **Identity Service** (Port: 3002): Handles user authentication and identity management
- **Payment Service** (Port: 3003): Manages payment processing

### API Gateways

- **Agency API Gateway** (Port: 4001): Entry point for agency users
- **Manager API Gateway** (Port: 4002): Entry point for manager users
- **Student API Gateway** (Port: 4003): Entry point for student users
- **Super Admin API Gateway** (Port: 4004): Entry point for admin users
- **University API Gateway** (Port: 4005): Entry point for university users

### Domain Services

- **Agency Service** (Port: 5001): Manages agency-related functionality
- **Analytics Service** (Port: 5002): Provides data analysis capabilities
- **Auth Service** (Port: 5003): Handles authentication and authorization
- **Events/Meetings Service** (Port: 5004): Handles scheduling and event management
- **Help Service** (Port: 5005): Offers support functionality
- **Messaging Service** (Port: 5006): Manages communication between users
- **Settings Service** (Port: 5007): Handles application settings
- **Students Service** (Port: 5008): Handles student data and operations
- **University Service** (Port: 5009): Manages university-related functionality

## Development

### Running Specific Services

You can run individual services or groups of services using npm scripts:

```sh
# Run a specific service
npm run start:services:agency

# Run all core services
npm run start:core:all

# Run all API gateways
npm run start:gateways:all

# Run all domain services
npm run start:services:all

# Run multiple specific services
npm run services agency-service auth-service agency-apigw
```

### Adding New Services

To generate a new service:

```sh
npx nx g @nx/nest:app new-service --directory=services
```

After creating a new service:

1. Assign a port in the appropriate range in `scripts/service-ports.js`
2. Add a new npm script in `package.json` to run the service
3. Update the documentation in SERVICES.md

### Testing

To run tests for all services:

```sh
npx nx run-many --target=test --all
```

To run tests for a specific service:

```sh
npx nx test service-name
```

## Project Structure

```
apply-goal-backend/
├── apps/                      # Application services
│   ├── apply-goal-backend/    # Main application
│   ├── core/                  # Core services
│   │   ├── audit-logging/
│   │   ├── identity-service/
│   │   └── payment-service/
│   ├── gateways/              # API gateways
│   │   ├── agency-apigw/
│   │   ├── manager-apigw/
│   │   ├── student-apigw/
│   │   ├── super-admin-apigw/
│   │   └── university-apigw/
│   └── services/              # Domain services
│       ├── agency-service/
│       ├── analytics-service/
│       ├── auth-service/
│       └── ...
├── libs/                      # Shared libraries
│   └── shared/
│       ├── config/
│       ├── dto/
│       ├── logging/
│       └── utils/
├── scripts/                   # Helper scripts
├── package.json
├── nx.json
└── README.md
```

## Deployment

### Building for Production

To create production builds for all services:

```sh
npx nx run-many --target=build --all --prod
```

To build a specific service:

```sh
npx nx build service-name --prod
```

### CI/CD

This project includes a GitHub Actions workflow for continuous integration. See `.github/workflows/ci.yml` for details.

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## Useful Resources

- [NestJS Documentation](https://docs.nestjs.com/)
- [Nx Documentation](https://nx.dev/)
- [TypeScript Documentation](https://www.typescriptlang.org/docs/)
- [Jest Documentation](https://jestjs.io/docs/getting-started)

## Proto FIle Formatter

Use Clang-Format
The easiest “drop-in” is to treat .proto like C-style code and run it through clang-format:

Install clang-format on your machine

macOS: brew install clang-format

Ubuntu/Debian: sudo apt install clang-format

Windows: install LLVM from https://llvm.org/builds/

Install the “Clang-Format” VS Code extension (by xaver)

Marketplace: xaver.clang-format
stackoverflow.com

Tell VS Code to use clang-format for .proto
In your workspace or user settings.json:

jsonc
Copy
Edit
{
// map the Proto language ID (often "protobuf" or "proto3") to clang-format
"[proto3]": {
"editor.defaultFormatter": "xaver.clang-format"
},
// Optional: auto-format on save for .proto
"editor.formatOnSave": true
}
(Optional) Add a .clang-format at your repo root to tweak style:

yaml
Copy
Edit
BasedOnStyle: Google
IndentWidth: 2
ColumnLimit: 100
