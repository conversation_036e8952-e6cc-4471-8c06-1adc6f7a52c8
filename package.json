{"name": "@apply-goal-backend/source", "version": "0.0.0", "license": "MIT", "private": true, "scripts": {"start": "nx serve apply-goal-backend", "start:core:all": "nx run-many --target=serve --projects=audit-logging,identity-service,payment-service --parallel=3", "start:core:audit": "nx serve audit-logging --port=3001", "start:core:identity": "nx serve identity-service --port=3002", "start:core:payment": "nx serve payment-service --port=3003", "start:gateways:all": "nx run-many --target=serve --projects=agency-apigw,manager-apigw,student-apigw,super-admin-apigw,university-apigw --parallel=5", "start:gateways:agency": "nx serve agency-apigw --port=4001", "start:gateways:manager": "nx serve manager-apigw --port=4002", "start:gateways:student": "nx serve student-apigw --port=4003", "start:gateways:admin": "nx serve super-admin-apigw --port=4004", "start:gateways:university": "nx serve university-apigw --port=4005", "start:services:all": "nx run-many --target=serve --projects=agency-service,analytics-service,auth-service,events-meetings-service,help-service,messaging-service,settings-service,students-service,university-service --parallel=9", "start:services:agency": "nx serve agency-service --port=5001", "start:services:analytics": "nx serve analytics-service --port=5002", "start:services:auth": "nx serve auth-service --port=5003", "start:services:events": "nx serve events-meetings-service --port=5004", "start:services:help": "nx serve help-service --port=5005", "start:services:messaging": "nx serve messaging-service --port=5006", "start:services:settings": "nx serve settings-service --port=5007", "start:services:students": "nx serve students-service --port=5008", "start:services:university": "nx serve university-service --port=5009", "start:all": "nx run-many --target=serve --all --parallel=20", "list:services": "nx show projects", "services": "node scripts/run-services.js", "clean": "pnpm -r exec rm -rf node_modules && rm -rf node_modules", "clean:dist": "pnpm -r exec rm -rf dist", "format": "nx format:write", "prepare": "husky install", "test": "nx run-many --target=test --all", "test:affected": "nx affected --target=test", "test:watch": "nx test --watch", "test:coverage": "nx test --coverage", "test:debug": "nx test --runInBand --detect<PERSON><PERSON>Handles", "test:e2e": "nx run-many --target=e2e --all", "test:e2e:affected": "nx affected --target=e2e", "test:e2e:watch": "nx e2e --watch", "test:e2e:debug": "nx e2e --runInBand --detectOpenHandles", "test:messaging": "nx test messaging-service", "test:messaging:watch": "nx test messaging-service --watch", "test:messaging:coverage": "nx test messaging-service --coverage", "test:messaging:e2e": "nx e2e messaging-service-e2e", "test:messaging:e2e:watch": "nx e2e messaging-service-e2e --watch", "test:messaging:e2e:debug": "nx e2e messaging-service-e2e --runInBand --detectOpenHandles", "test:services:all": "nx run-many --target=test --projects=agency-service,analytics-service,auth-service,events-meetings-service,help-service,messaging-service,settings-service,students-service,university-service --parallel=9", "test:services:e2e": "nx run-many --target=e2e --projects=agency-service-e2e,analytics-service-e2e,auth-service-e2e,events-meetings-service-e2e,help-service-e2e,messaging-service-e2e,settings-service-e2e,students-service-e2e,university-service-e2e --parallel=9", "test:core:all": "nx run-many --target=test --projects=audit-logging,identity-service,payment-service --parallel=3", "test:core:e2e": "nx run-many --target=e2e --projects=audit-logging-e2e,identity-service-e2e,payment-service-e2e --parallel=3", "test:gateways:all": "nx run-many --target=test --projects=agency-apigw,manager-apigw,student-apigw,super-admin-apigw,university-apigw --parallel=5", "test:gateways:e2e": "nx run-many --target=e2e --projects=agency-apigw-e2e,manager-apigw-e2e,student-apigw-e2e,super-admin-apigw-e2e,university-apigw-e2e --parallel=5", "test:libs": "nx run-many --target=test --projects=utils,health-check,auth,common,config,database,decorators,dto,interfaces,logging,monitoring --parallel=3", "test:ci": "nx affected --target=test --base=origin/master --head=HEAD", "test:e2e:ci": "nx affected --target=e2e --base=origin/master --head=HEAD", "build:libs": "nx run-many --target=build --projects=utils,health-check,auth,common,config,database,decorators,dto,interfaces,logging,monitoring,messaging --parallel=3", "build:lib:utils": "nx build utils", "build:lib:health-check": "nx build health-check", "build:lib:auth": "nx build auth", "build:lib:common": "nx build common", "build:lib:config": "nx build config", "build:lib:database": "nx build database", "build:lib:decorators": "nx build decorators", "build:lib:dto": "nx build dto", "build:lib:interfaces": "nx build interfaces", "build:lib:logging": "nx build logging", "build:lib:monitoring": "nx build monitoring", "build:lib:messaging": "nx build messaging", "watch:libs": "nx run-many --target=build --projects=utils,health-check,auth,common,config,database,decorators,dto,interfaces,logging,monitoring --parallel=3 --watch", "build:libs:core": "nx run-many --target=build --projects=utils,common,interfaces,config --parallel=2", "build:libs:infrastructure": "nx run-many --target=build --projects=database,logging,monitoring,health-check --parallel=2", "build:libs:business": "nx run-many --target=build --projects=auth,decorators,dto --parallel=2", "build:libs:sequential": "npm run build:libs:core && npm run build:libs:infrastructure && npm run build:libs:business", "build:core:all": "nx run-many --target=build --projects=audit-logging,identity-service,payment-service --parallel=3", "build:core:audit": "nx build audit-logging", "build:core:identity": "nx build identity-service", "build:core:payment": "nx build payment-service", "build:gateways:all": "nx run-many --target=build --projects=agency-apigw,manager-apigw,student-apigw,super-admin-apigw,university-apigw --parallel=5", "build:gateways:agency": "nx build agency-apigw", "build:gateways:manager": "nx build manager-apigw", "build:gateways:student": "nx build student-apigw", "build:gateways:admin": "nx build super-admin-apigw", "build:gateways:university": "nx build university-apigw", "build:services:all": "nx run-many --target=build --projects=agency-service,analytics-service,auth-service,events-meetings-service,help-service,messaging-service,settings-service,students-service,university-service --parallel=9", "build:services:agency": "nx build agency-service", "build:services:analytics": "nx build analytics-service", "build:services:auth": "nx build auth-service", "build:services:events": "nx build events-meetings-service", "build:services:help": "nx build help-service", "build:services:messaging": "nx build messaging-service", "build:services:settings": "nx build settings-service", "build:services:students": "nx build students-service", "build:services:university": "nx build university-service", "build:prod:all": "nx run-many --target=build --all --prod --parallel=20", "build:prod:core": "nx run-many --target=build --projects=audit-logging,identity-service,payment-service --prod --parallel=3", "build:prod:gateways": "nx run-many --target=build --projects=agency-apigw,manager-apigw,student-apigw,super-admin-apigw,university-apigw --prod --parallel=5", "build:prod:services": "nx run-many --target=build --projects=agency-service,analytics-service,auth-service,events-meetings-service,help-service,messaging-service,settings-service,students-service,university-service --prod --parallel=9", "build:dev:all": "nx run-many --target=build --all --configuration=development --parallel=20", "build:with-deps": "nx run-many --target=build --all --with-deps --parallel=20", "clean:build": "npm run clean:dist && npm run build:libs && npm run build:prod:all", "build:watch:core": "nx run-many --target=build --projects=audit-logging,identity-service,payment-service --watch", "build:watch:gateways": "nx run-many --target=build --projects=agency-apigw,manager-apigw,student-apigw,super-admin-apigw,university-apigw --watch", "build:watch:services": "nx run-many --target=build --projects=agency-service,analytics-service,auth-service,events-meetings-service,help-service,messaging-service,settings-service,students-service,university-service --watch", "test:messaging:e2e:force": "FORCE_EXIT=true nx e2e messaging-service-e2e --runInBand"}, "packageManager": "pnpm@8.15.1", "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "dependencies": {"@aws-sdk/client-s3": "3.812.0", "@golevelup/nestjs-rabbitmq": "6.0.0", "@grpc/grpc-js": "1.13.3", "@grpc/proto-loader": "0.7.15", "@grpc/reflection": "1.0.4", "@nestjs-modules/ioredis": "2.0.2", "@nestjs/common": "^10.0.2", "@nestjs/config": "4.0.2", "@nestjs/core": "^10.0.2", "@nestjs/jwt": "11.0.0", "@nestjs/microservices": "11.1.0", "@nestjs/mongoose": "11.0.3", "@nestjs/platform-express": "^10.4.17", "@nestjs/platform-socket.io": "11.1.1", "@nestjs/sequelize": "11.0.0", "@nestjs/websockets": "11.1.1", "@opentelemetry/api": "^1.7.0", "@opentelemetry/exporter-trace-otlp-http": "^0.45.1", "@opentelemetry/instrumentation": "^0.45.1", "@opentelemetry/instrumentation-grpc": "^0.45.1", "@opentelemetry/resources": "^1.19.0", "@opentelemetry/sdk-node": "^0.45.1", "@opentelemetry/semantic-conventions": "^1.19.0", "amqp-connection-manager": "4.1.14", "amqplib": "0.10.3", "axios": "^1.6.0", "bcrypt": "5.1.1", "brcypt": "1.0.1", "class-transformer": "^0.5.1", "class-validator": "0.14.1", "dotenv": "16.5.0", "express": "^4.18.2", "google-auth-library": "9.15.1", "google-protobuf": "3.21.4", "googleapis": "148.0.0", "handlebars": "4.7.8", "ioredis": "^5.6.1", "jsonwebtoken": "9.0.2", "long": "5.3.2", "mongoose": "8.15.0", "multer": "2.0.0", "multer-s3": "3.0.1", "nodemailer": "7.0.3", "pg": "^8.14.1", "pg-hstore": "^2.3.4", "prom-client": "^14.2.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.0", "sequelize": "^6.37.7", "sequelize-typescript": "^2.1.6", "sharp": "0.34.1", "socket.io": "4.8.1", "umzug": "^3.2.1", "uuid": "11.1.0"}, "devDependencies": {"@eslint/js": "^9.8.0", "@nestjs/schematics": "^10.0.1", "@nestjs/testing": "^10.0.2", "@nx/eslint": "20.8.0", "@nx/eslint-plugin": "20.8.0", "@nx/jest": "20.8.1", "@nx/js": "20.8.1", "@nx/nest": "20.8.1", "@nx/node": "20.8.1", "@nx/web": "20.8.0", "@nx/webpack": "20.8.1", "@nx/workspace": "20.8.0", "@swc-node/register": "~1.9.1", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@types/amqplib": "0.10.4", "@types/bcrypt": "5.0.2", "@types/express": "^4.17.21", "@types/express-serve-static-core": "5.0.6", "@types/google-protobuf": "3.15.12", "@types/ioredis": "5.0.0", "@types/jest": "^29.5.12", "@types/long": "5.0.0", "@types/multer": "1.4.12", "@types/node": "~18.16.9", "@types/nodemailer": "6.4.14", "@types/sequelize": "^4.28.20", "eslint": "^9.8.0", "eslint-config-prettier": "^10.0.0", "grpc-tools": "1.13.0", "husky": "^8.0.0", "jest": "^29.7.0", "jest-environment-node": "^29.7.0", "jsonc-eslint-parser": "^2.1.0", "nx": "20.8.0", "prettier": "^2.6.2", "protoc-gen-ts": "0.8.7", "ts-jest": "^29.1.0", "ts-node": "10.9.1", "ts-proto": "2.7.0", "tslib": "^2.3.0", "typescript": "5.7.3", "typescript-eslint": "^8.19.0", "webpack-cli": "^5.1.4"}}