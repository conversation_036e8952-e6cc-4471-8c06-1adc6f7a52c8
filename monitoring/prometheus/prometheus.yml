global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'traefik'
    static_configs:
      - targets: ['traefik:8082']
    metrics_path: /metrics

  - job_name: 'docker'
    docker_sd_configs:
      - host: unix:///var/run/docker.sock
        filters:
          - name: name
            values: ['.*']
    relabel_configs:
      - source_labels: [__meta_docker_container_name]
        target_label: container_name
      - source_labels: [__meta_docker_container_label_com_docker_compose_service]
        target_label: service_name

  - job_name: 'grpc_services'
    scrape_interval: 5s
    static_configs:
      - targets:
        - 'agency-service:5001'
        - 'messaging-service:5006'
        - 'events-meetings-service:5004'
        - 'analytics-service:5002'
        - 'auth-service:5003'
        - 'help-service:5005'
        - 'settings-service:5007'
        - 'students-service:5008'
        - 'university-service:5009'
        - 'audit-logging:3001'
        - 'identity-service:3002'
        - 'payment-service:3003'
    metrics_path: '/api/metrics'
    scrape_timeout: 3s

  - job_name: 'auth_gateway'
    scrape_interval: 5s
    static_configs:
      - targets:
        - 'auth-apigw:4006'
    metrics_path: '/api/metrics'
    scrape_timeout: 3s

