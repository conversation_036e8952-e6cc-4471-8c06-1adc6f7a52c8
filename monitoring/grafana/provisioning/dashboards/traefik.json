{"annotations": {"list": []}, "editable": true, "panels": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "targets": [{"expr": "sum(rate(traefik_router_requests_total[5m])) by (router)", "legendFormat": "{{router}}"}], "title": "Requests per Router", "type": "timeseries"}, {"targets": [{"expr": "sum(rate(traefik_service_request_duration_seconds_sum[5m])) / sum(rate(traefik_service_request_duration_seconds_count[5m]))", "legendFormat": "Average Response Time"}], "title": "Average Response Time", "type": "timeseries"}], "title": "Traefik Dashboard", "uid": "traefik"}