{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2015", "module": "esnext", "lib": ["es2020", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "paths": {"@apply-goal-backend/auth": ["libs/shared/auth/src/index.ts"], "@apply-goal-backend/common": ["libs/shared/common/src/index.ts"], "@apply-goal-backend/config": ["libs/shared/config/src/index.ts"], "@apply-goal-backend/database": ["libs/shared/database/src/index.ts"], "@apply-goal-backend/decorators": ["libs/shared/decorators/src/index.ts"], "@apply-goal-backend/dto": ["libs/shared/dto/src/index.ts"], "@apply-goal-backend/health-check": ["libs/shared/health-check/src/index.ts"], "@apply-goal-backend/interfaces": ["libs/shared/interfaces/src/index.ts"], "@apply-goal-backend/libs": ["libs/src/index.ts"], "@apply-goal-backend/logging": ["libs/shared/logging/src/index.ts"], "@apply-goal-backend/messaging": ["libs/shared/messaging/src/index.ts"], "@apply-goal-backend/monitoring": ["libs/shared/monitoring/src/index.ts"], "@apply-goal-backend/shared": ["lib/shared/src/index.ts"], "@apply-goal-backend/utils": ["libs/shared/utils/src/index.ts"]}, "types": ["node", "express"]}, "exclude": ["node_modules", "tmp"]}