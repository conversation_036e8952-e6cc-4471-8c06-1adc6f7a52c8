{"$schema": "./node_modules/nx/schemas/nx-schema.json", "defaultBase": "master", "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "production": ["default", "!{projectRoot}/.eslintrc.json", "!{projectRoot}/eslint.config.mjs", "!{projectRoot}/**/?(*.)+(spec|test).[jt]s?(x)?(.snap)", "!{projectRoot}/tsconfig.spec.json", "!{projectRoot}/jest.config.[jt]s", "!{projectRoot}/src/test-setup.[jt]s", "!{projectRoot}/test-setup.[jt]s"], "sharedGlobals": ["{workspaceRoot}/.github/workflows/ci.yml"]}, "nxCloudId": "67fe389ee94071427f038f4c", "plugins": [{"plugin": "@nx/webpack/plugin", "options": {"buildTargetName": "build", "serveTargetName": "serve", "previewTargetName": "preview", "buildDepsTargetName": "build-deps", "watchDepsTargetName": "watch-deps"}}, {"plugin": "@nx/eslint/plugin", "options": {"targetName": "lint"}}, {"plugin": "@nx/jest/plugin", "options": {"targetName": "test"}, "exclude": ["apps/apply-goal-backend-e2e/**/*", "apps/core/audit-logging-e2e/**/*", "apps/core/identity-service-e2e/**/*", "apps/core/payment-service-e2e/**/*", "apps/services/agency-service-e2e/**/*", "apps/services/analytics-service-e2e/**/*", "apps/services/auth-service-e2e/**/*", "apps/services/events-meetings-service-e2e/**/*", "apps/services/help-service-e2e/**/*", "apps/services/messaging-service-e2e/**/*", "apps/services/settings-service-e2e/**/*", "apps/services/students-service-e2e/**/*", "apps/services/university-service-e2e/**/*", "apps/gateways/super-admin-apigw-e2e/**/*", "apps/gateways/agency-apigw-e2e/**/*", "apps/gateways/manager-apigw-e2e/**/*", "apps/gateways/university-apigw-e2e/**/*", "apps/gateways/student-apigw-e2e/**/*", "apps/gateways/auth-apigw-e2e/**/*"]}], "targetDefaults": {"@nx/js:tsc": {"cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production"]}}}