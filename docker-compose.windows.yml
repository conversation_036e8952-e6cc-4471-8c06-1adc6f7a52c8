version: '3.8'

services:
  # API Gateway Example (auth-apigw)
  auth-apigw:
    build:
      context: .
      dockerfile: apps/gateways/auth-apigw/Dockerfile.dev
    ports:
      - "4006:4006"
    environment:
      - PORT=4006
      - NODE_ENV=development
      - METRICS_PORT=4006
      - REDIS_HOST=localhost
      - REDIS_PORT=6379
      - REDIS_DB=3
      - HASH_SALT=10
      - S3_ENDPOINT=http://localhost:9000
      - S3_PUBLIC_ENDPOINT=http://localhost:9000
      - S3_BUCKET=applygoal-files
      - S3_ACCESS_KEY=minioadmin
      - S3_SECRET_KEY=minioadmin123
      - S3_REGION=us-east-1
      - S3_FORCE_PATH_STYLE=true
      - RABBITMQ_URI=amqp://rabbitmq_user:rabbitmq_pass@localhost:5672
    volumes:
      - .:/app
      - /app/node_modules
      - /app/dist
    depends_on:
      auth-service:
        condition: service_started
    networks:
      - default

  # Backend Service Example (auth-service)
  auth-service:
    build:
      context: .
      dockerfile: apps/services/auth-service/Dockerfile.dev
    ports:
      - "5003:5003"
    environment:
      - PORT=5003
      - NODE_ENV=development
      - DB_HOST=localhost
      - DB_PORT=5432
      - DB_NAME=auth_db
      - DB_USER=auth_user
      - DB_PASSWORD=auth_pass
      - ACCESS_TOKEN_EXPIRY=1h
      - REFRESH_TOKEN_EXPIRY=7d
      - REFRESH_EXPIRY_MS=604800000
      - ACCESS_EXPIRY_MS=3600000
      - RABBITMQ_EXCHANGE=messaging_exchange
      - RABBITMQ_QUEUE=messaging_emails_queue
      - RABBITMQ_USER=rabbitmq_user
      - RABBITMQ_PASS=rabbitmq_pass
      - RABBITMQ_URI=amqp://rabbitmq_user:rabbitmq_pass@localhost:5672
    volumes:
      - .:/app
      - /app/node_modules
    networks:
      - default

networks:
  default:
    driver: bridge