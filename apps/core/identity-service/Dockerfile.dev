# --------------------------------------------
# Dev Dockerfile for identity-service (NestJS + Nx)
# Supports hot reload and Traefik integration
# --------------------------------------------

FROM node:18-alpine

# Set working directory inside container
WORKDIR /app

# Install curl (needed for healthcheck)
RUN apk --no-cache add curl bash

# Install pnpm
RUN corepack enable && corepack prepare pnpm@8.15.1 --activate

# Set pnpm store directory
ENV PNPM_HOME="/root/.local/share/pnpm"
ENV PATH="${PATH}:${PNPM_HOME}"

# Copy only essential config files first to leverage caching
COPY pnpm-lock.yaml ./
COPY package.json ./
COPY nx.json ./
COPY tsconfig*.json ./
COPY eslint.config.mjs ./

# Copy project configuration
COPY apps/core/identity-service/project.json ./apps/core/identity-service/
COPY libs/*/project.json ./libs/
COPY libs/shared/health-check/package.json ./libs/shared/health-check/

# Enable pnpm workspace mode
COPY pnpm-workspace.yaml ./

# Install dependencies with store cache
RUN --mount=type=cache,id=pnpm,target=/root/.local/share/pnpm/store \
    pnpm install --frozen-lockfile

# Copy source code after deps to avoid cache invalidation
COPY apps/core/identity-service ./apps/core/identity-service
COPY libs ./libs

# Build shared libraries first
RUN pnpm nx run-many --target=build --projects=utils,health-check,auth,common,config,database,decorators,dto,interfaces,logging --parallel=3


# Healthcheck for Docker & Traefik
HEALTHCHECK --interval=5s --timeout=10s --start-period=30s --retries=3 \
  CMD curl -f http://localhost:3002/health || exit 1

# Expose internal port for the service
EXPOSE 3002

# Run service in development mode with hot reload
CMD ["pnpm", "nx", "serve", "identity-service", "--configuration=development", "--host=0.0.0.0"]


