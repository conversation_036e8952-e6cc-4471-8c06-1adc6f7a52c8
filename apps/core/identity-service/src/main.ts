/**
 * This is not a production server yet!
 * This is only a minimal backend to get started.
 */

import { Logger } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app/app.module';
import express from 'express';
import { healthRouter } from './routes/health.routes';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Add express middleware for health routes
  app.use(express.json());
  app.use('/health', healthRouter);

  const globalPrefix = 'api';
  app.setGlobalPrefix(globalPrefix);
  const port = process.env.PORT || 3000;
  await app.listen(port);
  Logger.log(
    `🚀 Application is running on: http://localhost:${port}/${globalPrefix}`
  );
}

bootstrap();

