import { Injectable, Logger } from '@nestjs/common';
import { Sequelize } from 'sequelize-typescript';
import { join } from 'path';
import { Umzug, SequelizeStorage } from 'umzug';

@Injectable()
export class MigrationService {
  private readonly logger = new Logger(MigrationService.name);
  private readonly umzug: Umzug;

  constructor(private readonly sequelize: Sequelize) {
    this.umzug = new Umzug({
      migrations: {
        glob: ['*-*.ts', { cwd: join(__dirname, '../../migrations') }],
      },
      context: sequelize.getQueryInterface(),
      storage: new SequelizeStorage({ sequelize }),
      logger: console,
    });
  }

  async syncDatabase() {
    try {
      this.logger.log('Starting database sync...');
      await this.sequelize.sync({ force: false });
      this.logger.log('Database sync completed successfully');
    } catch (error) {
      this.logger.error('Database sync failed:', error);
      throw error;
    }
  }

  async migrate() {
    try {
      this.logger.log('Starting database migration...');
      await this.syncDatabase(); // First sync the database
      const migrations = await this.umzug.up(); // Then run migrations
      this.logger.log(`Executed ${migrations.length} migrations`);
      return migrations;
    } catch (error) {
      this.logger.error('Migration failed:', error);
      throw error;
    }
  }

  async getMigrationStatus() {
    try {
      const pending = await this.umzug.pending();
      const executed = await this.umzug.executed();

      return {
        pending: pending.map(m => m.name),
        executed: executed.map(m => m.name),
        isUpToDate: pending.length === 0
      };
    } catch (error) {
      this.logger.error('Failed to get migration status:', error);
      throw error;
    }
  }
}
