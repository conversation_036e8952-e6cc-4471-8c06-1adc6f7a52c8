import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { TracingService } from '@apply-goal-backend/monitoring';
import { context, trace } from '@opentelemetry/api';

@Injectable()
export class TracingInterceptor implements NestInterceptor {
  constructor(private readonly tracingService: TracingService) {}

  intercept(executionContext: ExecutionContext, next: CallHandler): Observable<any> {
    const tracer = trace.getTracer('audit-logging');
    const spanName = this.getSpanName(executionContext);

    return new Observable(subscriber => {
      const span = tracer.startSpan(spanName);
      
      // Add context metadata
      span.setAttributes({
        'service.name': 'audit-logging',
        'handler.type': executionContext.getType(),
        'handler.method': this.getHandlerMethod(executionContext)
      });

      context.with(trace.setSpan(context.active(), span), () => {
        next.handle().pipe(
          tap({
            next: (data) => {
              span.setStatus({ code: 1 }); // OK
              subscriber.next(data);
            },
            error: (error) => {
              span.setStatus({ code: 2, message: error.message }); // ERROR
              span.recordException(error);
              subscriber.error(error);
            },
            complete: () => {
              span.end();
              subscriber.complete();
            },
          }),
        ).subscribe();
      });
    });
  }

  private getSpanName(context: ExecutionContext): string {
    const handler = context.getHandler();
    const controller = context.getClass();
    return `${controller.name}.${handler.name}`;
  }

  private getHandlerMethod(context: ExecutionContext): string {
    if (context.getType() === 'http') {
      return context.switchToHttp().getRequest().method;
    } else if (context.getType() === 'rpc') {
      return context.getHandler().name;
    }
    return 'unknown';
  }
}