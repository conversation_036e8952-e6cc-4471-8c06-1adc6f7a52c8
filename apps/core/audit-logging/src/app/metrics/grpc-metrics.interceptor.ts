import { Injectable, NestInterceptor, Execution<PERSON>ontext, CallH<PERSON><PERSON> } from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { MetricsService } from '@apply-goal-backend/monitoring';

@Injectable()
export class GrpcMetricsInterceptor implements NestInterceptor {
  private grpcRequestCounter: any;
  private grpcRequestDuration: any;
  private grpcErrorCounter: any;
  private grpcActiveRequests: any;
  private grpcMessageSize: any;

  constructor(private metricsService: MetricsService) {
    this.grpcRequestCounter = this.metricsService.createCounter(
      'audit_grpc_requests_total',
      'Total number of gRPC requests',
      ['method', 'status']
    );

    this.grpcRequestDuration = this.metricsService.createHistogram(
      'audit_grpc_request_duration_seconds',
      'gRPC request duration in seconds',
      ['method'],
      [0.01, 0.05, 0.1, 0.5, 1, 2, 5]
    );

    this.grpcErrorCounter = this.metricsService.createCounter(
      'audit_grpc_errors_total',
      'Total number of gRPC errors',
      ['method', 'error_type']
    );

    this.grpcActiveRequests = this.metricsService.createGauge(
      'audit_grpc_active_requests',
      'Number of active gRPC requests',
      ['method']
    );

    this.grpcMessageSize = this.metricsService.createHistogram(
      'audit_grpc_message_size_bytes',
      'Size of gRPC messages in bytes',
      ['method', 'type']  // type can be 'request' or 'response'
    );
  }

  private safeStringify(obj: any): string {
    const seen = new WeakSet();
    return JSON.stringify(obj, (key, value) => {
      if (typeof value === 'object' && value !== null) {
        if (seen.has(value)) {
          return '[Circular]';
        }
        seen.add(value);
      }
      // Filter out non-serializable values
      if (typeof value === 'function' || value instanceof RegExp) {
        return value.toString();
      }
      if (value instanceof Error) {
        return {
          message: value.message,
          name: value.name,
          stack: value.stack
        };
      }
      return value;
    });
  }

  private calculateSize(data: any): number {
    try {
      return Buffer.byteLength(this.safeStringify(data));
    } catch (error) {
      console.warn('Failed to calculate message size:', error);
      return 0;
    }
  }

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const start = Date.now();
    const handler = context.getHandler();
    const method = handler.name;

    // Increment active requests
    this.grpcActiveRequests.inc({ method });

    // Track request size if available
    const rpc = context.switchToRpc();
    if (rpc.getData()) {
      const requestSize = this.calculateSize(rpc.getData());
      this.grpcMessageSize.observe({ method, type: 'request' }, requestSize);
    }

    return next.handle().pipe(
      tap((response) => {
        const duration = (Date.now() - start) / 1000;
        
        // Track successful request
        this.grpcRequestCounter.inc({ method, status: 'success' });
        this.grpcRequestDuration.observe({ method }, duration);
        
        // Track response size
        const responseSize = this.calculateSize(response);
        this.grpcMessageSize.observe({ method, type: 'response' }, responseSize);
        
        // Decrement active requests
        this.grpcActiveRequests.dec({ method });
      }),
      catchError((error) => {
        const duration = (Date.now() - start) / 1000;
        
        // Track failed request
        this.grpcRequestCounter.inc({ method, status: 'error' });
        this.grpcRequestDuration.observe({ method }, duration);
        this.grpcErrorCounter.inc({ 
          method, 
          error_type: error.code || error.status || 'UNKNOWN' 
        });
        
        // Decrement active requests
        this.grpcActiveRequests.dec({ method });
        
        throw error;
      })
    );
  }
}
