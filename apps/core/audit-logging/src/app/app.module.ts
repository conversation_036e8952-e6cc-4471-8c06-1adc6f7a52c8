import { Module, MiddlewareConsumer, NestModule } from '@nestjs/common';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { MetricsController } from './metrics/metrics.controller';
import { MonitoringModule } from '@apply-goal-backend/monitoring';
import { MetricsMiddleware } from './metrics/metrics.middleware';
import { GrpcMetricsInterceptor } from './metrics/grpc-metrics.interceptor';
import { AuditRepository } from './audit/audit.repository';
import { TracingInterceptor } from './metrics/tracing.interceptor';
import { MigrationController } from './migration/migration.controller';
import { MigrationService } from './migration/migration.service';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { SequelizeModule } from '@nestjs/sequelize';
import { AuditModel } from './audit/audit.model';
import { AuditClientService } from './audit/audit-client.service';

@Module({
  imports: [
    ConfigModule.forRoot(),
    MonitoringModule.forRoot({
      metrics: {
        serviceName: 'audit-logging',
        serviceVersion: '1.0.0',
        port: parseInt(process.env.METRICS_PORT || '3001', 10),
        path: '/metrics',
        labels: {
          environment: process.env.NODE_ENV || 'development',
          service_type: 'core',
        },
      },
      tracing: {
        serviceName: 'audit-logging',
        jaegerEndpoint: 'http://jaeger:4318/v1/traces',
      },
    }),
    SequelizeModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        dialect: 'postgres',
        host: configService.get('DB_HOST'),
        port: configService.get('DB_PORT'),
        username: configService.get('DB_USER'),
        password: configService.get('DB_PASSWORD'),
        database: configService.get('DB_NAME'),
        models: [AuditModel],
        autoLoadModels: true,
        synchronize: false, // Set to false since we're using migrations
      }),
    }),
    SequelizeModule.forFeature([AuditModel]),
  ],
  controllers: [AppController, MetricsController, MigrationController],
  providers: [
    AppService,
    MigrationService,
    AuditClientService,
    {
      provide: AuditRepository,
      useClass: AuditRepository,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: GrpcMetricsInterceptor,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: TracingInterceptor,
    },
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(MetricsMiddleware)
      .exclude('/metrics') // Exclude metrics endpoint to avoid recursive tracking
      .forRoutes('*');
  }
}
