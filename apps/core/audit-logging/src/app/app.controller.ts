import { Controller } from '@nestjs/common';
import { GrpcMethod } from '@nestjs/microservices';
import { AppService } from './app.service';
import {
  CreateAuditLogRequest,
  GetAuditLogRequest,
  ListAuditLogsRequest,
  SearchAuditLogsRequest,
  ListAuditLogsResponse,
  AuditLogResponse,
} from './audit/interfaces/audit.interface';

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @GrpcMethod('AuditService', 'CreateAuditLog')
  async createAuditLog(
    request: CreateAuditLogRequest
  ): Promise<AuditLogResponse> {
    return this.appService.createAuditLog(request);
  }

  @GrpcMethod('AuditService', 'GetAuditLog')
  async getAuditLog(request: GetAuditLogRequest): Promise<AuditLogResponse> {
    return this.appService.getAuditLog(request);
  }

  @GrpcMethod('AuditService', 'ListAuditLogs')
  async listAuditLogs(
    request: ListAuditLogsRequest
  ): Promise<ListAuditLogsResponse> {
    return this.appService.listAuditLogs(request);
  }

  @GrpcMethod('AuditService', 'SearchAuditLogs')
  async searchAuditLogs(
    request: SearchAuditLogsRequest
  ): Promise<ListAuditLogsResponse> {
    return this.appService.searchAuditLogs(request);
  }
}
