import express from 'express';
import { <PERSON><PERSON><PERSON><PERSON>er, ServiceHealthController } from '@apply-goal-backend/health-check';
import { DateUtil } from '@apply-goal-backend/utils';

const router = express.Router();

const healthController = new HealthController();
const serviceHealthController = new ServiceHealthController('audit-logging');

// Basic service health check
router.get('/', (req, res) => serviceHealthController.check(req, res));

// Detailed health check (includes DB and Redis status)
router.get('/detailed', (req, res) => healthController.check(req, res));

// test router to test util functions
router.get('/test', (req, res) => {
  res.status(200).json({ message: 'Hello Tester!', time: DateUtil.isValid(new Date()) });
});

export { router as healthRouter };
