import { Table, Column, Model, DataType } from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';

@Table({
  tableName: 'audit_logs',
  timestamps: false,
})
export class AuditModel extends BaseModel {
  @Column({
    type: DataType.BIGINT,
    primaryKey: true,
    autoIncrement: true,
  })
  id: bigint;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  userId: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  userRole: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  actions: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  resourceId: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  resourceType: string;

  @Column({
    type: DataType.STRING(500),
    allowNull: false,
  })
  description: string;

  @Column({
    type: DataType.JSONB,
    allowNull: true,
  })
  metaData: Record<string, any>;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  serviceName: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  source: string;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  timeStamp: Date;

  @Column({
    type: DataType.STRING(500),
    allowNull: true,
  })
  ipAddress: string;

  @Column({
    type: DataType.STRING(500),
    allowNull: true,
  })
  userAgent: string;
}
