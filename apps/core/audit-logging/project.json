{"name": "audit-logging", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/core/audit-logging/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "nx:run-commands", "options": {"command": "webpack-cli build", "args": ["node-env=production"]}, "configurations": {"development": {"args": ["node-env=development"]}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "audit-logging:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "audit-logging:build:development"}, "production": {"buildTarget": "audit-logging:build:production"}}}, "test": {"options": {"passWithNoTests": true}}}}