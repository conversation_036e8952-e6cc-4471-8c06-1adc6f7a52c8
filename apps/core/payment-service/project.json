{"name": "payment-service", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/core/payment-service/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "nx:run-commands", "options": {"command": "webpack-cli build", "args": ["node-env=production"]}, "configurations": {"development": {"args": ["node-env=development"]}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "payment-service:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "payment-service:build:development"}, "production": {"buildTarget": "payment-service:build:production"}}}, "test": {"options": {"passWithNoTests": true}}}}