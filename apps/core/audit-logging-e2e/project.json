{"name": "audit-logging-e2e", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "implicitDependencies": ["audit-logging"], "targets": {"e2e": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{e2eProjectRoot}"], "options": {"jestConfig": "apps/core/audit-logging-e2e/jest.config.ts", "passWithNoTests": true}, "dependsOn": ["audit-logging:build"]}}}