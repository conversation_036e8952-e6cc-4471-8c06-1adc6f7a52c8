import { Injectable } from '@nestjs/common';
import { MetricsService } from '@apply-goal-backend/monitoring';

@Injectable()
export class AppService {
  private requestCounter: any;
  private requestDuration: any;
  private eventCounter: any;
  private meetingCounter: any;
  private participantGauge: any;
  private eventDuration: any;

  constructor(private metricsService: MetricsService) {
    // HTTP metrics
    this.requestCounter = this.metricsService.createCounter(
      'http_requests_total',
      'Total number of HTTP requests processed',
      ['method', 'endpoint', 'status']
    );

    this.requestDuration = this.metricsService.createHistogram(
      'http_request_duration_seconds',
      'HTTP request duration in seconds',
      ['method', 'endpoint']
    );

    // Event metrics
    this.eventCounter = this.metricsService.createCounter(
      'events_total',
      'Total number of events processed',
      ['type', 'status']
    );

    // Meeting metrics
    this.meetingCounter = this.metricsService.createCounter(
      'meetings_total',
      'Total number of meetings processed',
      ['type', 'status']
    );

    this.participantGauge = this.metricsService.createGauge(
      'meeting_participants_current',
      'Current number of participants in meetings',
      ['meeting_id']
    );

    this.eventDuration = this.metricsService.createHistogram(
      'event_duration_minutes',
      'Event duration in minutes',
      ['type'],
      [5, 15, 30, 60, 120, 240, 480]  // buckets for different durations
    );
  }

  // Example method to track HTTP request
  trackHttpRequest(method: string, endpoint: string, status: string, duration: number) {
    this.requestCounter.inc({ method, endpoint, status });
    this.requestDuration.observe({ method, endpoint }, duration);
  }

  // Example method to track event creation
  trackEventCreation(type: string, status: string) {
    this.eventCounter.inc({ type, status });
  }

  // Example method to track meeting metrics
  trackMeeting(type: string, status: string, meetingId: string, participantCount: number) {
    this.meetingCounter.inc({ type, status });
    this.participantGauge.set({ meeting_id: meetingId }, participantCount);
  }

  // Example method to track event duration
  trackEventDuration(type: string, durationMinutes: number) {
    this.eventDuration.observe({ type }, durationMinutes);
  }

  getData(): { message: string } {
    // Example of tracking an HTTP request
    this.trackHttpRequest('GET', '/api', '200', 0.1);
    return { message: 'Hello API' };
  }
}
