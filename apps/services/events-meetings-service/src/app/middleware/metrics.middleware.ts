import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { AppService } from '../app.service';

@Injectable()
export class MetricsMiddleware implements NestMiddleware {
  constructor(private appService: AppService) {}

  use(req: Request, res: Response, next: NextFunction) {
    const start = Date.now();

    res.on('finish', () => {
      const duration = (Date.now() - start) / 1000; // Convert to seconds
      this.appService.trackHttpRequest(
        req.method,
        req.path,
        res.statusCode.toString(),
        duration
      );
    });

    next();
  }
}