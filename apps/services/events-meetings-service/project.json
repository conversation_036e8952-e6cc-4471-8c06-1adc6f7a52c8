{"name": "events-meetings-service", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/services/events-meetings-service/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "nx:run-commands", "options": {"command": "webpack-cli build", "args": ["node-env=production"]}, "configurations": {"development": {"args": ["node-env=development"]}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "events-meetings-service:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "events-meetings-service:build:development"}, "production": {"buildTarget": "events-meetings-service:build:production"}}}, "test": {"options": {"passWithNoTests": true}}}}