{"name": "analytics-service-e2e", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "implicitDependencies": ["analytics-service"], "targets": {"e2e": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{e2eProjectRoot}"], "options": {"jestConfig": "apps/services/analytics-service-e2e/jest.config.ts", "passWithNoTests": true}, "dependsOn": ["analytics-service:build"]}}}