{"name": "agency-service-e2e", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "implicitDependencies": ["agency-service"], "targets": {"e2e": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{e2eProjectRoot}"], "options": {"jestConfig": "apps/services/agency-service-e2e/jest.config.ts", "passWithNoTests": true}, "dependsOn": ["agency-service:build"]}}}