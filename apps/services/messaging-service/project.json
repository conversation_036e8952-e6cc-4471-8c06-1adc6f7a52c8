{"name": "messaging-service", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/services/messaging-service/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "nx:run-commands", "options": {"command": "webpack-cli build", "args": ["node-env=production"]}, "configurations": {"development": {"args": ["node-env=development"]}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "messaging-service:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "messaging-service:build:development"}, "production": {"buildTarget": "messaging-service:build:production"}}}, "test": {"options": {"passWithNoTests": true}}}}