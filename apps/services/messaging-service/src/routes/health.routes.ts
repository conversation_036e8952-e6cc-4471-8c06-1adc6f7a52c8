import express from 'express';
import { <PERSON><PERSON><PERSON>roller, ServiceHealthController } from '@apply-goal-backend/health-check';

const router = express.Router();

const healthController = new HealthController();
const serviceHealthController = new ServiceHealthController('audit-logging');

// Basic service health check
router.get('/', (req, res) => serviceHealthController.check(req, res));

// Detailed health check (includes DB and Redis status)
router.get('/detailed', (req, res) => healthController.check(req, res));

export { router as healthRouter };
