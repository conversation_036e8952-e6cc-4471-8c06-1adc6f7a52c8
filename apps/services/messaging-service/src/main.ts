/**
 * This is not a production server yet!
 * This is only a minimal backend to get started.
 */

import { Logger } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app/app.module';
import express from 'express';
import { healthRouter } from './routes/health.routes';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import { ConfigService } from '@nestjs/config';
import { MetricsService, TracingService } from '@apply-goal-backend/monitoring';

async function bootstrap() {
  const logger = new Logger('Main');
  const app = await NestFactory.create(AppModule);

  // Get monitoring services
  const metricsService = app.get(MetricsService);
  const tracingService = app.get(TracingService);

  // Start tracing
  await tracingService.start();

  // HTTP Middleware (Express)
  app.use(express.json());
  app.use('/health', healthRouter);

  const configService = app.get(ConfigService);
  const rabbitmqUrl =
    configService.get<string>('RABBITMQ_URL') || 'amqp://rabbitmq:5672';
  const rabbitmqUser =
    configService.get<string>('RABBITMQ_USER') || 'rabbitmq_user';
  const rabbitmqPass =
    configService.get<string>('RABBITMQ_PASS') || 'rabbitmq_pass';

  // Setup RabbitMQ Microservice
  const formattedUrl = rabbitmqUrl.includes('@')
    ? rabbitmqUrl
    : `amqp://${rabbitmqUser}:${rabbitmqPass}@${rabbitmqUrl.replace(
        'amqp://',
        ''
      )}`;

  app.connectMicroservice<MicroserviceOptions>({
    transport: Transport.RMQ,
    options: {
      urls: [formattedUrl],
      queue: 'messaging_emails_queue',
      queueOptions: {
        durable: true,
      },
      prefetchCount: 1,
      noAck: false,
    },
  });

  // Log the connection URL (but mask the password for security)
  const logUrl = formattedUrl.replace(/:([^@]+)@/, ':***@');
  logger.log(`Connecting to RabbitMQ at: ${logUrl}`);

  const globalPrefix = 'api';
  app.setGlobalPrefix(globalPrefix);
  const port = process.env.PORT || 5006;

  await app.startAllMicroservices();

  // Start HTTP server
  await app.listen(port);

  Logger.log(
    `🚀 HTTP Application is running on: http://localhost:${port}/${globalPrefix}`
  );
  Logger.log(
    `📊 Metrics server is running on: http://localhost:${port}/metrics`
  );
  logger.log(`📧 Email service connected to RabbitMQ at: ${rabbitmqUrl}`);
}

bootstrap();
