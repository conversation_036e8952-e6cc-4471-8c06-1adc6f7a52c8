import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { MetricsController } from './metrics.controller';
import { MonitoringModule } from '@apply-goal-backend/monitoring';
import { MailerModule } from './mailer/mailer.module';
import { ConfigModule } from '@nestjs/config';
import { ChatModule } from './chat/chat.module';
import { Mongoose } from 'mongoose';
import { MongooseModule } from '@nestjs/mongoose';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    MongooseModule.forRoot(process.env.MONGO_URI || 'mongodb://localhost/chat'),
    MonitoringModule.forRoot({
      metrics: {
        serviceName: 'messaging-service',
        serviceVersion: '1.0.0',
        port: parseInt(process.env.METRICS_PORT || '5006', 10),
        path: '/metrics',
        labels: {
          environment: process.env.NODE_ENV || 'development',
        },
      },
      tracing: {
        serviceName: 'messaging-service',
        jaegerEndpoint:
          process.env.JAEGER_ENDPOINT || 'http://jaeger:4318/v1/traces',
      },
    }),
    MailerModule,
    ChatModule,
  ],
  controllers: [AppController, MetricsController],
  providers: [AppService],
})
export class AppModule {}
