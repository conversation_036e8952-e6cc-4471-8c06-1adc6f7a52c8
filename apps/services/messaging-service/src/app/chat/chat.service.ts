import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Message, MessageDocument } from './schema/message.schema';

@Injectable()
export class ChatService {
  constructor(
    @InjectModel(Message.name) private messageModel: Model<MessageDocument>
  ) {}

  async saveMessage(data: {
    from: string;
    to: string;
    content: string;
  }): Promise<Message> {
    const msg = new this.messageModel({
      from: data.from,
      to: data.to,
      content: data.content,
      timestamp: new Date(),
    });
    return msg.save();
  }

  async getConversation(user1: string, user2: string): Promise<Message[]> {
    return this.messageModel
      .find({
        $or: [
          { from: user1, to: user2 },
          { from: user2, to: user1 },
        ],
      })
      .sort({ timestamp: 1 });
  }
}
