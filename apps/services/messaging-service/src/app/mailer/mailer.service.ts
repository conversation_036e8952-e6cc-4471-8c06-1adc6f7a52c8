import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as nodemailer from 'nodemailer';
import { MailerTemplateService } from './mailer-template.service';
import { MetricsService } from '@apply-goal-backend/monitoring';
import { google } from 'googleapis';

export interface EmailOptions {
  to: string | string[];
  subject: string;
  text?: string;
  html?: string;
  cc?: string | string[];
  bcc?: string | string[];
  attachments?: Array<{
    filename: string;
    content: string | Buffer;
    contentType?: string;
  }>;
  templateId?: string;
  templateData?: Record<string, any>;
}

@Injectable()
export class MailerService {
  private transporter: nodemailer.Transporter;
  private readonly logger = new Logger(MailerService.name);
  private emailCounter: any;
  private emailDuration: any;

  constructor(
    private configService: ConfigService,
    private templateService: MailerTemplateService,
    private metricsService: MetricsService
  ) {
    this.initializeTransporter();
    this.initializeMetrics();
  }

  private initializeMetrics() {
    this.emailCounter = this.metricsService.createCounter(
      'email_sent_total',
      'Total number of emails sent',
      ['status', 'template']
    );

    this.emailDuration = this.metricsService.createHistogram(
      'email_send_duration_seconds',
      'Email sending duration in seconds',
      ['template']
    );
  }

  private initializeTransporter() {
    const clientId = this.configService.get<string>('GMAIL_CLIENT_ID');
    const clientSecret = this.configService.get<string>('GMAIL_CLIENT_SECRET');
    const refreshToken = this.configService.get<string>('GMAIL_REFRESH_TOKEN');
    const senderEmail = this.configService.get<string>('GMAIL_SENDER');

    this.logger.debug(
      `Gmail OAuth2 configuration: clientId=${clientId}, clientSecret=${clientSecret}, refreshToken=${refreshToken}, senderEmail=${senderEmail}`
    );
    if (!clientId || !clientSecret || !refreshToken || !senderEmail) {
      this.logger.warn(
        'Gmail OAuth2 configuration is incomplete. Email sending will be disabled.'
      );
      return;
    }

    const oAuth2Client = new google.auth.OAuth2(
      clientId,
      clientSecret,
      'https://developers.google.com/oauthplayground'
    );
    oAuth2Client.setCredentials({ refresh_token: refreshToken });

    oAuth2Client
      .getAccessToken()
      .then(({ token }) => {
        this.transporter = nodemailer.createTransport({
          service: 'gmail',
          auth: {
            type: 'OAuth2',
            user: senderEmail,
            clientId,
            clientSecret,
            refreshToken,
            accessToken: token,
          },
        });

        this.transporter.verify((error) => {
          if (error) {
            this.logger.error('Gmail OAuth2 SMTP connection error:', error);
          } else {
            this.logger.log('Gmail OAuth2 SMTP transporter is ready ✅');
          }
        });
      })
      .catch((error) => {
        this.logger.error('Failed to fetch access token:', error.message);
      });
  }

  async sendEmail(options: EmailOptions): Promise<boolean> {
    if (!this.transporter) {
      this.logger.warn('Email transporter not initialized. Cannot send email.');
      return false;
    }

    const startTime = Date.now();
    const templateId = options.templateId || 'custom';

    try {
      if (options.templateId && options.templateData) {
        const rendered = await this.templateService.renderTemplate(
          options.templateId,
          options.templateData
        );
        options.html = rendered;
      }

      const mailOptions = {
        from: this.configService.get<string>('GMAIL_SENDER'),
        to: options.to,
        cc: options.cc,
        bcc: options.bcc,
        subject: options.subject,
        text: options.text,
        html: options.html,
        attachments: options.attachments,
      };

      const info = await this.transporter.sendMail(mailOptions);
      this.logger.log(`📨 Email sent: ${info.messageId}`);

      // 🔍 Track metrics
      const duration = (Date.now() - startTime) / 1000;
      this.emailCounter.inc({ status: 'success', template: templateId });
      this.emailDuration.observe({ template: templateId }, duration);

      return true;
    } catch (error) {
      this.logger.error(
        `❌ Failed to send email: ${error.message}`,
        error.stack
      );
      this.emailCounter.inc({ status: 'error', template: templateId });
      return false;
    }
  }
}
