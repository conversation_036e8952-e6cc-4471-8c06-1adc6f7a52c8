import { <PERSON>, Logger } from '@nestjs/common';
import { EventPattern, Payload, Ctx, RmqContext } from '@nestjs/microservices';
import { MailerService, EmailOptions } from './mailer.service';

@Controller()
export class MailerController {
  private readonly logger = new Logger(MailerController.name);

  constructor(private readonly mailerService: MailerService) {}

  @EventPattern('send_email')
  async handleSendEmail(
    @Payload() data: EmailOptions,
    @Ctx() context: RmqContext
  ) {
    const channel = context.getChannelRef();
    const originalMsg = context.getMessage();

    try {
      this.logger.log(`Received send_email event: ${JSON.stringify(data)}`);

      const result = await this.mailerService.sendEmail(data);

      if (result) {
        this.logger.log('Email sent successfully');
        channel.ack(originalMsg);
      } else {
        this.logger.warn('Failed to send email, requeuing message');
        channel.nack(originalMsg, false, true);
      }
    } catch (error) {
      this.logger.error(
        `Error processing email: ${error.message}`,
        error.stack
      );
      channel.nack(originalMsg, false, true);
    }
  }
}
