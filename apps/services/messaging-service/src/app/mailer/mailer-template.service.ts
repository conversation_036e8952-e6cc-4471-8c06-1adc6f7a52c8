import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import * as fs from 'fs';
import * as path from 'path';
import * as Handlebars from 'handlebars';

@Injectable()
export class MailerTemplateService {
  private readonly logger = new Logger(MailerTemplateService.name);
  private readonly templatesDir: string;
  private readonly templateCache: Map<string, Handlebars.TemplateDelegate> =
    new Map();

  constructor() {
    this.templatesDir = path.join(
      process.cwd(),
      'apps/services/messaging-service/src/templates'
    );
    this.registerHelpers();
    this.preloadTemplates();
  }

  private registerHelpers() {
    // Register custom Handlebars helpers
    Handlebars.registerHelper('formatDate', function (date, format) {
      if (!date) return '';
      const d = new Date(date);
      return d.toLocaleDateString();
    });

    Handlebars.registerHelper('uppercase', function (str) {
      return str ? str.toUpperCase() : '';
    });
  }

  private preloadTemplates() {
    try {
      if (!fs.existsSync(this.templatesDir)) {
        fs.mkdirSync(this.templatesDir, { recursive: true });
        this.logger.log(`Created templates directory at ${this.templatesDir}`);

        // Create a sample template
        const samplePath = path.join(this.templatesDir, 'welcome.hbs');
        const sampleContent = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>Welcome to ApplyGoal</title>
</head>
<body>
  <h1>Welcome to ApplyGoal, {{name}}!</h1>
  <p>Thank you for joining our platform. We're excited to have you on board.</p>
  <p>Your account has been created successfully.</p>
  <p>Best regards,<br>The ApplyGoal Team</p>
</body>
</html>`;
        fs.writeFileSync(samplePath, sampleContent);
        this.logger.log('Created sample welcome template');
      }

      const files = fs.readdirSync(this.templatesDir);
      for (const file of files) {
        if (file.endsWith('.hbs')) {
          const templateId = file.replace('.hbs', '');
          const templatePath = path.join(this.templatesDir, file);
          const templateContent = fs.readFileSync(templatePath, 'utf8');
          this.templateCache.set(
            templateId,
            Handlebars.compile(templateContent)
          );
          this.logger.log(`Preloaded template: ${templateId}`);
        }
      }
    } catch (error) {
      this.logger.error(
        `Error preloading templates: ${error.message}`,
        error.stack
      );
    }
  }

  async renderTemplate(
    templateId: string,
    data: Record<string, any>
  ): Promise<string> {
    let template = this.templateCache.get(templateId);
    this.logger.debug(`Rendering template: ${templateId}`, data);
    if (!template) {
      const templatePath = path.join(this.templatesDir, `${templateId}.hbs`);

      if (!fs.existsSync(templatePath)) {
        throw new NotFoundException(`Email template '${templateId}' not found`);
      }

      const templateContent = fs.readFileSync(templatePath, 'utf8');
      template = Handlebars.compile(templateContent);
      this.templateCache.set(templateId, template);
    }

    return template(data);
  }
}
