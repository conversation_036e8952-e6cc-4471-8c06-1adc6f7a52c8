export enum EmailEventType {
  SEND_EMAIL = 'send_email',
  WELCOME_EMAIL = 'send_welcome_email',
  PASSWORD_RESET = 'send_password_reset',
  NOTIFICATION = 'send_notification_email',
  VERIFICATION = 'send_verification_email',
  INVITATION = 'send_invitation_email',
  APPLICATION_STATUS = 'send_application_status_email',
  DOCUMENT_UPLOAD = 'send_document_upload_email',
  MEETING_SCHEDULED = 'send_meeting_scheduled_email',
  MEETING_REMINDER = 'send_meeting_reminder_email',
  MEETING_CANCELED = 'send_meeting_canceled_email'
}