import { Modu<PERSON> } from '@nestjs/common';
import { MailerService } from './mailer.service';
import { MailerController } from './mailer.controller';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { MailerTemplateService } from './mailer-template.service';

@Module({
  imports: [
    ConfigModule,
    ClientsModule.registerAsync([
      {
        name: 'RABBITMQ_CLIENT',
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => {
          const rabbitmqUrl =
            configService.get<string>('RABBITMQ_URL') || 'amqp://rabbitmq:5672';
          const rabbitmqUser =
            configService.get<string>('RABBITMQ_USER') || 'rabbitmq_user';
          const rabbitmqPass =
            configService.get<string>('RABBITMQ_PASS') || 'rabbitmq_pass';

          // Format the URL to include credentials
          const formattedUrl = rabbitmqUrl.includes('@')
            ? rabbitmqUrl
            : `amqp://${rabbitmqUser}:${rabbitmqPass}@${rabbitmqUrl.replace(
                'amqp://',
                ''
              )}`;

          return {
            transport: Transport.RMQ,
            options: {
              urls: [formattedUrl],
              queue: 'messaging_emails_queue',
              queueOptions: {
                durable: true,
              },
              noAck: false,
            },
          };
        },
        inject: [ConfigService],
      },
    ]),
  ],
  controllers: [MailerController],
  providers: [MailerService, MailerTemplateService],
  exports: [MailerService],
})
export class MailerModule {}
