import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { MetricsService } from '@apply-goal-backend/monitoring';
import { ConfigService } from '@nestjs/config';
import * as amqplib from 'amqplib';

@Injectable()
export class AppService implements OnModuleInit {
  private requestCounter: any;
  private requestDuration: any;
  private grpcRequestCounter: any;
  private readonly logger = new Logger(AppService.name);

  constructor(
    private metricsService: MetricsService,
    private configService: ConfigService
  ) {
    // Initialize metrics with service-specific names
    this.requestCounter = this.metricsService.createCounter(
      'http_requests_total',
      'Total number of HTTP requests processed',
      ['method', 'endpoint', 'status']
    );

    this.requestDuration = this.metricsService.createHistogram(
      'http_request_duration_seconds',
      'HTTP request duration in seconds',
      ['method', 'endpoint']
    );

    this.grpcRequestCounter = this.metricsService.createCounter(
      'grpc_requests_total',
      'Total number of gRPC requests processed',
      ['method', 'status']
    );
  }

  async onModuleInit() {
    // Test RabbitMQ connection on startup
    await this.testRabbitMQConnection();
  }

  async testRabbitMQConnection() {
    try {
      const rabbitmqUrl =
        this.configService.get<string>('RABBITMQ_URL') ||
        'amqp://rabbitmq:5672';
      const rabbitmqUser =
        this.configService.get<string>('RABBITMQ_USER') || 'rabbitmq_user';
      const rabbitmqPass =
        this.configService.get<string>('RABBITMQ_PASS') || 'rabbitmq_pass';

      // Format the URL to include credentials
      const formattedUrl = rabbitmqUrl.includes('@')
        ? rabbitmqUrl
        : `amqp://${rabbitmqUser}:${rabbitmqPass}@${rabbitmqUrl.replace(
            'amqp://',
            ''
          )}`;

      // Log the connection URL (but mask the password for security)
      const logUrl = formattedUrl.replace(/:([^@]+)@/, ':***@');
      this.logger.log(`Testing connection to RabbitMQ at: ${logUrl}`);

      // Try to connect
      const connection = await amqplib.connect(formattedUrl);
      this.logger.log('Successfully connected to RabbitMQ');

      // Close the connection
      await connection.close();
    } catch (error) {
      this.logger.error(
        `Failed to connect to RabbitMQ: ${error.message}`,
        error.stack
      );
    }
  }

  getData(): { message: string } {
    this.requestCounter.inc({ method: 'GET', endpoint: '/api', status: '200' });
    return { message: 'Hello API' };
  }

  // Add methods to track gRPC metrics
  trackGrpcRequest(method: string, status: string) {
    this.grpcRequestCounter.inc({ method, status });
  }
}
