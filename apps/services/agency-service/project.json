{"name": "agency-service", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/services/agency-service/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "nx:run-commands", "options": {"command": "webpack-cli build", "args": ["node-env=production"]}, "configurations": {"development": {"args": ["node-env=development"]}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "agency-service:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "agency-service:build:development"}, "production": {"buildTarget": "agency-service:build:production"}}}, "test": {"options": {"passWithNoTests": true}}}}