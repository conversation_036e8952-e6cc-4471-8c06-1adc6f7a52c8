import { Injectable } from '@nestjs/common';
import { MetricsService } from '@apply-goal-backend/monitoring';

@Injectable()
export class AppService {
  private requestCounter: any;
  private requestDuration: any;
  private grpcRequestCounter: any;
  private grpcRequestDuration: any;
  private agencyCounter: any;
  private agencyOperationsCounter: any;
  private agencyProcessingDuration: any;
  private activeAgenciesGauge: any;
  private auditLogRequestCounter: any;
  private auditLogRequestDuration: any;

  constructor(private metricsService: MetricsService) {
    // HTTP metrics
    this.requestCounter = this.metricsService.createCounter(
      'http_requests_total',
      'Total number of HTTP requests processed',
      ['method', 'endpoint', 'status']
    );

    this.requestDuration = this.metricsService.createHistogram(
      'http_request_duration_seconds',
      'HTTP request duration in seconds',
      ['method', 'endpoint']
    );

    // gRPC metrics
    this.grpcRequestCounter = this.metricsService.createCounter(
      'grpc_requests_total',
      'Total number of gRPC requests processed',
      ['method', 'status']
    );

    this.grpcRequestDuration = this.metricsService.createHistogram(
      'grpc_request_duration_seconds',
      'gRPC request duration in seconds',
      ['method']
    );

    // Agency-specific metrics
    this.agencyCounter = this.metricsService.createCounter(
      'agencies_total',
      'Total number of agencies',
      ['status', 'type'] // type: school, university, etc.
    );

    this.agencyOperationsCounter = this.metricsService.createCounter(
      'agency_operations_total',
      'Total number of agency operations',
      ['operation', 'status']
    );

    this.agencyProcessingDuration = this.metricsService.createHistogram(
      'agency_processing_duration_seconds',
      'Agency processing duration in seconds',
      ['operation'],
      [0.1, 0.5, 1, 2, 5, 10]
    );

    this.activeAgenciesGauge = this.metricsService.createGauge(
      'active_agencies_current',
      'Current number of active agencies',
      ['type']
    );

    // Audit log metrics
    this.auditLogRequestCounter = this.metricsService.createCounter(
      'audit_log_requests_total',
      'Total number of audit log requests',
      ['operation', 'status']
    );

    this.auditLogRequestDuration = this.metricsService.createHistogram(
      'audit_log_request_duration_seconds',
      'Audit log request duration in seconds',
      ['operation']
    );
  }

  // HTTP metrics tracking
  trackHttpRequest(method: string, endpoint: string, status: string, duration: number) {
    this.requestCounter.inc({ method, endpoint, status });
    this.requestDuration.observe({ method, endpoint }, duration);
  }

  // gRPC metrics tracking
  trackGrpcRequest(method: string, status: string, duration: number) {
    this.grpcRequestCounter.inc({ method, status });
    this.grpcRequestDuration.observe({ method }, duration);
  }

  // Agency metrics tracking
  trackAgencyOperation(operation: string, status: string, duration: number) {
    this.agencyOperationsCounter.inc({ operation, status });
    this.agencyProcessingDuration.observe({ operation }, duration);
  }

  trackAgencyCreation(status: string, type: string) {
    this.agencyCounter.inc({ status, type });
  }

  updateActiveAgencies(type: string, count: number) {
    this.activeAgenciesGauge.set({ type }, count);
  }

  // Audit log metrics tracking
  trackAuditLogRequest(operation: string, status: string, duration: number) {
    this.auditLogRequestCounter.inc({ operation, status });
    this.auditLogRequestDuration.observe({ operation }, duration);
  }

  getData() {
    return { message: 'Hello from agency-service' };
  }
}
