import { Modu<PERSON> } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { MetricsController } from './metrics.controller';
import { AgencyController } from './agency.controller';
import { AgencyService } from './agency.service';
import { AuditClientService } from './audit.service';
import { MonitoringModule } from '@apply-goal-backend/monitoring';
import { HealthController } from './health.controller';

@Module({
  imports: [
    MonitoringModule.forRoot({
      metrics: {
        serviceName: 'agency-service',
        serviceVersion: '1.0.0',
        port: parseInt(process.env.METRICS_PORT || '5001', 10),
        path: '/metrics',
        labels: {
          environment: process.env.NODE_ENV || 'development'
        }
      },
      tracing: {
        serviceName: 'agency-service',
        jaegerEndpoint: 'http://jaeger:4318/v1/traces'
      }
    })
  ],
  controllers: [App<PERSON>ontroll<PERSON>, MetricsController, HealthController, AgencyController],
  providers: [AppService, AgencyService, AuditClientService],
})
export class AppModule {}
