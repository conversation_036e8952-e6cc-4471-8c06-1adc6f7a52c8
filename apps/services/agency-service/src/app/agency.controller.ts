import { Controller } from '@nestjs/common';
import { GrpcMethod } from '@nestjs/microservices';
import { AgencyService } from './agency.service';
import { AppService } from './app.service';

interface GetAgencyRequest {
  id: string;
  userId: string;
}

interface CreateAgencyRequest {
  name: string;
  type: string;
  userId: string;
}

interface GetAgencyResponse {
  id: string;
  name: string;
  type: string;
}

@Controller()
export class AgencyController {
  constructor(
    private readonly agencyService: AgencyService,
    private readonly appService: AppService
  ) {}

  @GrpcMethod('AgencyService')
  async getAgency(data: GetAgencyRequest): Promise<GetAgencyResponse> {
    const startTime = Date.now();
    try {
      const result = await this.agencyService.getAgency(data.id, data.userId);
      this.appService.trackGrpcRequest('getAgency', 'success', (Date.now() - startTime) / 1000);
      return result;
    } catch (error) {
      this.appService.trackGrpcRequest('getAgency', 'error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }

  @GrpcMethod('AgencyService')
  async createAgency(data: CreateAgencyRequest): Promise<GetAgencyResponse> {
    const startTime = Date.now();
    try {
      const result = await this.agencyService.createAgency(
        { name: data.name, type: data.type },
        data.userId
      );
      this.appService.trackGrpcRequest('createAgency', 'success', (Date.now() - startTime) / 1000);
      return result;
    } catch (error) {
      this.appService.trackGrpcRequest('createAgency', 'error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }

  @GrpcMethod('AgencyService')
  async getAgencyAuditLogs(data: { agencyId: string }) {
    const startTime = Date.now();
    try {
      const result = await this.agencyService.getAgencyAuditLogs(data.agencyId);
      this.appService.trackGrpcRequest('getAgencyAuditLogs', 'success', (Date.now() - startTime) / 1000);
      return result;
    } catch (error) {
      this.appService.trackGrpcRequest('getAgencyAuditLogs', 'error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }
}
