import { Injectable } from '@nestjs/common';
import { AuditClientService } from './audit.service';
import { AppService } from './app.service';
import { firstValueFrom } from 'rxjs';

@Injectable()
export class AgencyService {
  constructor(
    private readonly auditClient: AuditClientService,
    private readonly appService: AppService
  ) {}

  async getAgency(id: string, userId: string) {
    const startTime = Date.now();
    try {
      // Mock implementation - replace with actual database query
      const agency = {
        id,
        name: `Agency ${id}`,
        type: 'university'
      };

      // Track the operation
      this.appService.trackAgencyOperation('get', 'success', (Date.now() - startTime) / 1000);

      // Create audit log
      const auditStartTime = Date.now();
      try {
        await firstValueFrom(this.auditClient.createAuditLog({
          userId,
          serviceName: 'agency-service',
          action: 'GET',
          resourceType: 'AGENCY',
          resourceId: id,
          description: `Retrieved agency with ID ${id}`,
          metadata: {
            agencyName: agency.name,
            agencyType: agency.type
          }
        }));
        this.appService.trackAuditLogRequest('create', 'success', (Date.now() - auditStartTime) / 1000);
      } catch (error) {
        this.appService.trackAuditLogRequest('create', 'error', (Date.now() - auditStartTime) / 1000);
        // Don't rethrow - we don't want audit failures to break the main flow
      }

      return agency;
    } catch (error) {
      this.appService.trackAgencyOperation('get', 'error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }

  async createAgency(data: { name: string; type: string }, userId: string) {
    const startTime = Date.now();
    try {
      // Mock implementation - replace with actual database query
      const id = Math.random().toString(36).substring(7);
      const agency = {
        id,
        name: data.name,
        type: data.type
      };

      // Track metrics
      this.appService.trackAgencyOperation('create', 'success', (Date.now() - startTime) / 1000);
      this.appService.trackAgencyCreation('active', data.type);
      this.appService.updateActiveAgencies(data.type, 1); // In real implementation, get actual count

      // Create audit log
      const auditStartTime = Date.now();
      try {
        await firstValueFrom(this.auditClient.createAuditLog({
          userId,
          serviceName: 'agency-service',
          action: 'CREATE',
          resourceType: 'AGENCY',
          resourceId: id,
          description: `Created new agency: ${data.name}`,
          metadata: {
            agencyName: data.name,
            agencyType: data.type
          }
        }));
        this.appService.trackAuditLogRequest('create', 'success', (Date.now() - auditStartTime) / 1000);
      } catch (error) {
        this.appService.trackAuditLogRequest('create', 'error', (Date.now() - auditStartTime) / 1000);
      }

      return agency;
    } catch (error) {
      this.appService.trackAgencyOperation('create', 'error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }

  async getAgencyAuditLogs(agencyId: string) {
    const startTime = Date.now();
    try {
      const response = await firstValueFrom(this.auditClient.getAuditLog({
        id: agencyId
      }));
      
      this.appService.trackAuditLogRequest('get', 'success', (Date.now() - startTime) / 1000);
      return response.log;
    } catch (error) {
      this.appService.trackAuditLogRequest('get', 'error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }
}
