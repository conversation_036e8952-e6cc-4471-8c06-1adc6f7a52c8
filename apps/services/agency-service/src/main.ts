/**
 * This is not a production server yet!
 * This is only a minimal backend to get started.
 */

import { Logger } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app/app.module';
import express, { Response, Request } from 'express';
import { healthRouter } from './routes/health.routes';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import { join } from 'path';
import { MonitoringModule, MetricsService, TracingService } from '@apply-goal-backend/monitoring';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Get monitoring services
  const metricsService = app.get(MetricsService);
  const tracingService = app.get(TracingService);

  // Start tracing
  await tracingService.start();

  // HTTP Middleware (Express)
  app.use(express.json());
  app.use('/health', healthRouter);

  // Setup gRPC Microservice
  app.connectMicroservice<MicroserviceOptions>({
    transport: Transport.GRPC,
    options: {
      package: 'agency',
      protoPath: join(process.cwd(), 'libs/shared/dto/src/lib/agency/agency.proto'),
      url: '0.0.0.0:50051',
    },
  });

  const globalPrefix = 'api';
  app.setGlobalPrefix(globalPrefix);

  const port = process.env.PORT || 5001;

  await app.startAllMicroservices();
  await app.listen(port);

  Logger.log(`🚀 HTTP Application is running on: http://localhost:${port}/${globalPrefix}`);
  Logger.log(`🚀 gRPC server is running on: 0.0.0.0:50051`);
  Logger.log(`📊 Metrics server is running on: http://localhost:5001/metrics`);
}

bootstrap();
