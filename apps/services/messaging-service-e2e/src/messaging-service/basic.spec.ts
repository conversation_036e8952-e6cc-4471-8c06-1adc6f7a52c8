import axios from 'axios';

describe('Basic API Tests', () => {
  // Add cleanup after all tests
  afterAll(async () => {
    // Close any open connections
    await new Promise<void>((resolve) => {
      setTimeout(() => {
        resolve();
      }, 500);
    });
  });

  it('should check health endpoint', async () => {
    try {
      const res = await axios.get('/health');
      expect(res.status).toBe(200);
    } catch (error) {
      console.error('Health check failed:', error.message);
      throw error;
    }
  });
});
