import * as amqp from 'amqp-connection-manager';
import { EmailEventType } from '../../../messaging-service/src/app/mailer/email-events.enum';
import axios from 'axios';

describe('RabbitMQ Integration', () => {
  let connection;
  let channelWrapper;

  beforeAll(async () => {
    // Connect to RabbitMQ
    connection = amqp.connect([
      'amqp://rabbitmq_user:rabbitmq_pass@localhost:5672',
    ]);

    // Create a channel
    channelWrapper = connection.createChannel({
      json: true,
      setup: (channel) =>
        Promise.all([
          channel.assertExchange('messaging_emails_queue', 'topic', {
            durable: true,
          }),
          channel.assertQueue('messaging_emails_queue', { durable: true }),
          channel.bindQueue(
            'messaging_emails_queue',
            'messaging_emails_queue',
            'send_email'
          ),
        ]),
    });

    // Wait for connection to be established
    await channelWrapper.waitForConnect();
  });

  afterAll(async () => {
    // Close connection
    if (connection) {
      await connection.close();
    }
  });

  it('should process email messages', async () => {
    // First check if service is healthy
    const healthRes = await axios.get('/health');
    expect(healthRes.status).toBe(200);

    // Send test message
    const testMessage = {
      pattern: EmailEventType.SEND_EMAIL,
      data: {
        to: '<EMAIL>',
        subject: 'Test Email',
        templateId: 'test-template',
        templateData: {
          name: 'Test User',
        },
      },
    };

    console.log('Publishing email test message');

    await channelWrapper.publish(
      'messaging_emails_queue',
      'send_email',
      Buffer.from(JSON.stringify(testMessage)),
      { persistent: true }
    );

    console.log('Email message published');

    // Wait for message to be processed
    await new Promise((resolve) => setTimeout(resolve, 2000));

    // Note: In a real test, you would verify the email was sent
    // This could be done by mocking the email service or checking a database
    expect(true).toBe(true);
  }, 30000); // Increase timeout to 30 seconds
});
