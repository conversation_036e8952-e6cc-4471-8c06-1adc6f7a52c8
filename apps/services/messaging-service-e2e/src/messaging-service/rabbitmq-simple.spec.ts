import * as amqp from 'amqp-connection-manager';

describe('RabbitMQ Simple Tests', () => {
  let connection;
  let channelWrapper;
  let consumerTag;
  let queueName;

  beforeAll(async () => {
    // Generate a unique queue name for this test run
    queueName = `test_queue_${Date.now()}`;

    // Connect to RabbitMQ
    connection = amqp.connect([
      'amqp://rabbitmq_user:rabbitmq_pass@localhost:5672',
    ]);

    // Create a channel
    channelWrapper = connection.createChannel({
      json: true,
      setup: async (channel) => {
        // Create a temporary queue
        await channel.assertQueue(queueName, {
          durable: false,
          autoDelete: true,
        });
      },
    });

    // Wait for connection to be established
    await channelWrapper.waitForConnect();
  });

  afterAll(async () => {
    // Cancel consumer if it exists
    if (consumerTag) {
      try {
        await channelWrapper.cancel(consumerTag);
      } catch (error) {
        console.warn('Error canceling consumer:', error.message);
      }
    }

    // Close connection
    if (connection) {
      await connection.close();
    }
  });

  it('should connect to RabbitMQ', () => {
    expect(connection).toBeDefined();
    expect(channelWrapper).toBeDefined();
  });

  it('should be able to publish to a queue', async () => {
    console.log(`Using queue: ${queueName}`);

    // Publish a message
    const message = { test: 'simple-test', timestamp: Date.now() };
    const publishResult = await channelWrapper.sendToQueue(queueName, message);

    console.log(`Published message to ${queueName}, result:`, publishResult);

    expect(publishResult).toBeTruthy();
  }, 10000);

  it('should be able to consume from a queue', async () => {
    // Skip if no connection
    if (!connection || !channelWrapper) {
      console.warn('Skipping test due to connection issues');
      return;
    }

    // First, purge the queue to remove any previous messages
    await channelWrapper.addSetup((channel) => channel.purgeQueue(queueName));
    console.log(`Purged queue ${queueName} to remove previous messages`);

    // Create a message to publish
    const testMessage = { test: 'consume-test', timestamp: Date.now() };

    // Create a promise that will resolve when we receive the message
    const messageReceived = new Promise<any>((resolve) => {
      // Set up a consumer
      channelWrapper
        .consume(queueName, (message) => {
          if (message) {
            // Parse the message content
            const content = JSON.parse(message.content.toString());

            // Acknowledge the message
            channelWrapper.ack(message);

            // Resolve the promise with the message content
            resolve(content);
          }
        })
        .then(({ consumerTag: tag }) => {
          // Save the consumer tag for cleanup
          consumerTag = tag;
        });
    });

    // Publish the test message
    console.log(`Publishing test message to ${queueName}:`, testMessage);
    await channelWrapper.sendToQueue(queueName, testMessage);

    // Wait for the message to be received
    const receivedMessage = await messageReceived;
    console.log('Received message:', receivedMessage);

    // Verify the message content
    expect(receivedMessage).toEqual(testMessage);
  }, 10000);
});
