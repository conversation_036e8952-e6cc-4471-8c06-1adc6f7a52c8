/* eslint-disable */

module.exports = async function () {
  // Put clean up logic here (e.g. stopping services, docker-compose, etc.).
  console.log(globalThis.__TEARDOWN_MESSAGE__);

  // Force close any remaining connections
  await new Promise<void>((resolve) => {
    setTimeout(() => {
      resolve();
    }, 2000); // Give more time for connections to close
  });

  // Always force exit to prevent hanging
  process.exit(0);
};
