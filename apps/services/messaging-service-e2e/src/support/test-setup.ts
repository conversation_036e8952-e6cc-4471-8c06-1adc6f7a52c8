/* eslint-disable */
import axios from 'axios';
import http from 'http';

/**
 * Custom serializer function to handle circular references
 */
function safeStringify(obj: any): string {
  const cache = new Set();
  return JSON.stringify(obj, (key, value) => {
    if (typeof value === 'object' && value !== null) {
      if (cache.has(value)) {
        return '[Circular Reference]';
      }
      cache.add(value);
    }
    return value;
  });
}

module.exports = async function () {
  // Configure axios for tests
  const host = process.env.HOST ?? 'localhost';
  const port = process.env.PORT ?? '5006'; // Use the messaging service port
  axios.defaults.baseURL = `http://${host}:${port}`;

  // Create a custom http agent with keepAlive disabled
  const httpAgent = new http.Agent({ keepAlive: false });
  axios.defaults.httpAgent = httpAgent;

  // Add interceptor to handle circular references
  axios.interceptors.response.use(
    (response) => {
      // Remove the request property which often causes circular references
      if (response.request) {
        delete response.request;
      }

      // Simplify the config object
      if (response.config) {
        response.config = {
          url: response.config.url,
          method: response.config.method,
          headers: response.config.headers,
          baseURL: response.config.baseURL,
          data: response.config.data,
        };
      }

      return response;
    },
    (error) => {
      // Handle error objects specially to avoid circular references
      if (error.response) {
        const { status, statusText, headers, data } = error.response;
        error.response = { status, statusText, headers, data };
      }
      return Promise.reject(error);
    }
  );

  // Override global JSON.stringify for test environment (optional)
  const originalStringify = JSON.stringify;
  global.JSON.stringify = function (value: any, ...args: any[]) {
    try {
      return originalStringify(value, ...args);
    } catch (error) {
      if (error instanceof TypeError && error.message.includes('circular')) {
        return safeStringify(value);
      }
      throw error;
    }
  };
};
