export default {
  displayName: 'messaging-service-e2e',
  preset: '../../../jest.preset.js',
  globalSetup: '<rootDir>/src/support/global-setup.ts',
  globalTeardown: '<rootDir>/src/support/global-teardown.ts',
  setupFiles: ['<rootDir>/src/support/test-setup.ts'],
  testEnvironment: 'node',
  transform: {
    '^.+\\.[tj]s$': [
      'ts-jest',
      {
        tsconfig: '<rootDir>/tsconfig.spec.json',
      },
    ],
  },
  moduleFileExtensions: ['ts', 'js', 'html'],
  coverageDirectory: '../../../coverage/messaging-service-e2e',
  // Add these options to handle circular references and open handles
  testRunner: 'jest-circus/runner',
  maxWorkers: 1, // Reduce worker count to avoid serialization issues
  forceExit: true, // Force exit after tests complete
  detectOpenHandles: true, // Help identify open handles
  testTimeout: 60000, // Increase default timeout for all tests to 60 seconds
};
