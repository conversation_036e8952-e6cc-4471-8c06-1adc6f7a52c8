{"name": "messaging-service-e2e", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "implicitDependencies": ["messaging-service"], "targets": {"e2e": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{e2eProjectRoot}"], "options": {"jestConfig": "apps/services/messaging-service-e2e/jest.config.ts", "passWithNoTests": true}, "dependsOn": ["messaging-service:build"]}}}