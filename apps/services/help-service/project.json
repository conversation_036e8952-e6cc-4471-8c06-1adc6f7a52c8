{"name": "help-service", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/services/help-service/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "nx:run-commands", "options": {"command": "webpack-cli build", "args": ["node-env=production"]}, "configurations": {"development": {"args": ["node-env=development"]}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "help-service:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "help-service:build:development"}, "production": {"buildTarget": "help-service:build:production"}}}, "test": {"options": {"passWithNoTests": true}}}}