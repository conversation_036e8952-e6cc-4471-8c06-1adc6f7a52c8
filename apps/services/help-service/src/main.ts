/**
 * This is not a production server yet!
 * This is only a minimal backend to get started.
 */

import { Logger } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app/app.module';
import express from 'express';
import { healthRouter } from './routes/health.routes';
import { MonitoringModule, MetricsService, TracingService } from '@apply-goal-backend/monitoring';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Get monitoring services
  const metricsService = app.get(MetricsService);
  const tracingService = app.get(TracingService);

  // Start tracing
  await tracingService.start();

  // HTTP Middleware (Express)
  app.use(express.json());
  app.use('/health', healthRouter);

  const globalPrefix = 'api';
  app.setGlobalPrefix(globalPrefix);
  const port = process.env.PORT || 5005;
  
  await app.listen(port);

  Logger.log(`🚀 Application is running on: http://localhost:${port}/${globalPrefix}`);
  Logger.log(`📊 Metrics server is running on: http://localhost:${port}/api/metrics`);
}

bootstrap();
