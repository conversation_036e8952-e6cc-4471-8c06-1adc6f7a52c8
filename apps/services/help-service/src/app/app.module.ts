import { Module, MiddlewareConsumer, NestModule } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { MetricsController } from './metrics.controller';
import { MonitoringModule } from '@apply-goal-backend/monitoring';
import { MetricsMiddleware } from './middleware/metrics.middleware';

@Module({
  imports: [
    MonitoringModule.forRoot({
      metrics: {
        serviceName: 'help-service',
        serviceVersion: '1.0.0',
        port: parseInt(process.env.METRICS_PORT || '5005', 10),
        path: '/api/metrics',
        labels: {
          environment: process.env.NODE_ENV || 'development'
        }
      },
      tracing: {
        serviceName: 'help-service',
        jaegerEndpoint: 'http://jaeger:4318/v1/traces'
      }
    })
  ],
  controllers: [AppController, MetricsController],
  providers: [AppService],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(MetricsMiddleware)
      .exclude('/api/metrics') // Exclude metrics endpoint to avoid recursive tracking
      .forRoutes('*');
  }
}
