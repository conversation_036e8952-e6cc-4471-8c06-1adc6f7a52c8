import { Injectable } from '@nestjs/common';
import { MetricsService } from '@apply-goal-backend/monitoring';

@Injectable()
export class AppService {
  private requestCounter: any;
  private requestDuration: any;
  private ticketCounter: any;
  private ticketResolutionTime: any;
  private activeTicketsGauge: any;
  private supportQueueGauge: any;
  private categoryCounter: any;
  private responseTimeHistogram: any;
  private satisfactionRatingHistogram: any;

  constructor(private metricsService: MetricsService) {
    // HTTP metrics
    this.requestCounter = this.metricsService.createCounter(
      'http_requests_total',
      'Total number of HTTP requests processed',
      ['method', 'endpoint', 'status']
    );

    this.requestDuration = this.metricsService.createHistogram(
      'http_request_duration_seconds',
      'HTTP request duration in seconds',
      ['method', 'endpoint']
    );

    // Ticket metrics
    this.ticketCounter = this.metricsService.createCounter(
      'help_tickets_total',
      'Total number of help tickets',
      ['type', 'priority', 'status'] // type: question, bug, feature; priority: low, medium, high; status: open, closed
    );

    this.ticketResolutionTime = this.metricsService.createHistogram(
      'ticket_resolution_time_hours',
      'Time taken to resolve tickets in hours',
      ['type', 'priority'],
      [1, 4, 8, 24, 48, 72, 168] // buckets for different durations (up to 1 week)
    );

    // Active tickets gauge
    this.activeTicketsGauge = this.metricsService.createGauge(
      'active_tickets_current',
      'Current number of active tickets',
      ['type', 'priority']
    );

    // Support queue gauge
    this.supportQueueGauge = this.metricsService.createGauge(
      'support_queue_length',
      'Current length of support queue',
      ['priority']
    );

    // Category metrics
    this.categoryCounter = this.metricsService.createCounter(
      'help_requests_by_category',
      'Total number of help requests by category',
      ['category'] // e.g., technical, billing, account, etc.
    );

    // Response time metrics
    this.responseTimeHistogram = this.metricsService.createHistogram(
      'first_response_time_minutes',
      'Time to first response in minutes',
      ['priority'],
      [5, 15, 30, 60, 120, 240, 480] // buckets for different durations
    );

    // Satisfaction rating metrics
    this.satisfactionRatingHistogram = this.metricsService.createHistogram(
      'satisfaction_rating',
      'Customer satisfaction rating',
      ['type'],
      [1, 2, 3, 4, 5] // 1-5 rating scale
    );
  }

  // Track HTTP request
  trackHttpRequest(method: string, endpoint: string, status: string, duration: number) {
    this.requestCounter.inc({ method, endpoint, status });
    this.requestDuration.observe({ method, endpoint }, duration);
  }

  // Track new ticket
  trackNewTicket(type: string, priority: string, status: string) {
    this.ticketCounter.inc({ type, priority, status });
    this.activeTicketsGauge.inc({ type, priority });
    this.supportQueueGauge.inc({ priority });
  }

  // Track ticket resolution
  trackTicketResolution(type: string, priority: string, durationHours: number) {
    this.ticketResolutionTime.observe({ type, priority }, durationHours);
    this.activeTicketsGauge.dec({ type, priority });
    this.supportQueueGauge.dec({ priority });
  }

  // Track help request category
  trackHelpCategory(category: string) {
    this.categoryCounter.inc({ category });
  }

  // Track first response time
  trackFirstResponseTime(priority: string, durationMinutes: number) {
    this.responseTimeHistogram.observe({ priority }, durationMinutes);
  }

  // Track satisfaction rating
  trackSatisfactionRating(type: string, rating: number) {
    this.satisfactionRatingHistogram.observe({ type }, rating);
  }

  // Update queue length
  setQueueLength(priority: string, length: number) {
    this.supportQueueGauge.set({ priority }, length);
  }

  getData(): { message: string } {
    this.trackHttpRequest('GET', '/api', '200', 0.1);
    return { message: 'Hello Help API' };
  }
}
