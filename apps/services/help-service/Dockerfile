# Build stage
FROM node:18-alpine as builder

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY nx.json ./
COPY tsconfig*.json ./
COPY eslint.config.mjs ./
# Add ESLint config

# Copy source code
COPY apps/services/help-service ./apps/services/help-service
COPY libs ./libs

# Install dependencies
RUN npm ci

# Build the application
RUN npx nx build help-service --prod

# Production stage
FROM node:18-alpine

WORKDIR /app

# Copy built assets from builder
COPY --from=builder /app/dist/apps/services/help-service ./

# Install production dependencies
RUN npm ci --only=production

# Install curl for healthcheck
RUN apk --no-cache add curl

# Set environment variables
ENV NODE_ENV=production
ENV PORT=5005

# Expose the service port
EXPOSE 5005

# Add healthcheck
HEALTHCHECK --interval=30s --timeout=30s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:5005/health || exit 1

# Start the service
CMD ["node", "main.js"]


