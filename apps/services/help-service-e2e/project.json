{"name": "help-service-e2e", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "implicitDependencies": ["help-service"], "targets": {"e2e": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{e2eProjectRoot}"], "options": {"jestConfig": "apps/services/help-service-e2e/jest.config.ts", "passWithNoTests": true}, "dependsOn": ["help-service:build"]}}}