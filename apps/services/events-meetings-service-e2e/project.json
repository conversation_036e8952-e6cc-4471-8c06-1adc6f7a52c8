{"name": "events-meetings-service-e2e", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "implicitDependencies": ["events-meetings-service"], "targets": {"e2e": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{e2eProjectRoot}"], "options": {"jestConfig": "apps/services/events-meetings-service-e2e/jest.config.ts", "passWithNoTests": true}, "dependsOn": ["events-meetings-service:build"]}}}