// import { Controller } from '@nestjs/common';
// import { GrpcMethod } from '@nestjs/microservices';
// import { SettingsService } from './settings.service';
// import {
//   GetUserSettingsRequest,
//   UpdateUserSettingsRequest,
//   UserSettings,
//   GetAppSettingsRequest,
//   AppSettings,
// } from '@apply-goal-backend/dto';

// @Controller()
// export class SettingsController {
//   constructor(private readonly settingsService: SettingsService) {}

//   @GrpcMethod('SettingsService', 'GetUserSettings')
//   async getUserSettings(request: GetUserSettingsRequest): Promise<UserSettings> {
//     return this.settingsService.getUserSettings(request);
//   }

//   @GrpcMethod('SettingsService', 'UpdateUserSettings')
//   async updateUserSettings(request: UpdateUserSettingsRequest): Promise<UserSettings> {
//     return this.settingsService.updateUserSettings(request);
//   }

//   // Implement other methods...
// }