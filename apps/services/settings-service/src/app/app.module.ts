import { Module, MiddlewareConsumer, NestModule } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { MetricsController } from './metrics.controller';
import { MonitoringModule } from '@apply-goal-backend/monitoring';

@Module({
  imports: [
    MonitoringModule.forRoot({
      metrics: {
        serviceName: 'settings-service',
        serviceVersion: '1.0.0',
        port: parseInt(process.env.METRICS_PORT || '5007', 10),
        path: '/metrics',  // Changed from '/api/metrics' to '/metrics'
        labels: {
          environment: process.env.NODE_ENV || 'development'
        }
      },
      tracing: {
        serviceName: 'settings-service',
        jaegerEndpoint: 'http://jaeger:4318/v1/traces'
      }
    })
  ],
  controllers: [AppController, MetricsController],
  providers: [AppService],
})
export class AppModule {}
