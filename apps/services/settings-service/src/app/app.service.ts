import { Injectable } from '@nestjs/common';
import { MetricsService } from '@apply-goal-backend/monitoring';

@Injectable()
export class AppService {
  private requestCounter: any;
  private requestDuration: any;
  private settingsOperationsCounter: any;
  private settingsUpdateDuration: any;
  private activeSettingsGauge: any;

  constructor(private metricsService: MetricsService) {
    // Initialize HTTP metrics
    this.requestCounter = this.metricsService.createCounter(
      'http_requests_total',
      'Total number of HTTP requests processed',
      ['method', 'endpoint', 'status']
    );

    this.requestDuration = this.metricsService.createHistogram(
      'http_request_duration_seconds',
      'HTTP request duration in seconds',
      ['method', 'endpoint']
    );

    // Settings-specific metrics
    this.settingsOperationsCounter = this.metricsService.createCounter(
      'settings_operations_total',
      'Total number of settings operations',
      ['operation', 'status', 'category']
    );

    this.settingsUpdateDuration = this.metricsService.createHistogram(
      'settings_update_duration_seconds',
      'Settings update operation duration in seconds',
      ['category'],
      [0.1, 0.5, 1, 2, 5, 10]
    );

    this.activeSettingsGauge = this.metricsService.createGauge(
      'active_settings_total',
      'Total number of active settings',
      ['category']
    );
  }

  // Track HTTP request
  trackHttpRequest(method: string, endpoint: string, status: string, duration: number) {
    this.requestCounter.inc({ method, endpoint, status });
    this.requestDuration.observe({ method, endpoint }, duration);
  }

  // Track settings operation
  trackSettingsOperation(operation: string, status: string, category: string) {
    this.settingsOperationsCounter.inc({ operation, status, category });
  }

  // Track settings update duration
  trackSettingsUpdateDuration(category: string, duration: number) {
    this.settingsUpdateDuration.observe({ category }, duration);
  }

  // Update active settings count
  updateActiveSettingsCount(category: string, count: number) {
    this.activeSettingsGauge.set({ category }, count);
  }

  getData(): { message: string } {
    // Example of tracking an HTTP request
    this.requestCounter.inc({ method: 'GET', endpoint: '/api', status: '200' });
    return { message: 'Hello API' };
  }
}
