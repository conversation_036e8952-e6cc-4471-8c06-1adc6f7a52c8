{"name": "auth-service", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/services/auth-service/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "nx:run-commands", "options": {"command": "webpack-cli build", "args": ["node-env=production"]}, "configurations": {"development": {"args": ["node-env=development"]}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "auth-service:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "auth-service:build:development"}, "production": {"buildTarget": "auth-service:build:production"}}}, "test": {"options": {"passWithNoTests": true}}}}