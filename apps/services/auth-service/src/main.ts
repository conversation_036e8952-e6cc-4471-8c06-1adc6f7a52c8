/**
 * This is not a production server yet!
 * This is only a minimal backend to get started.
 */

import { NestFactory } from '@nestjs/core';
import { AppModule } from './app/app.module';
import { Logger } from '@nestjs/common';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import { join } from 'path';
import * as express from 'express';
import { healthRouter } from './app/health/health.routes';
import { MigrationService } from './app/migration/migration.service';
import { SeedService } from './app/seed/seed.service';
import { MetricsService, TracingService } from '@apply-goal-backend/monitoring';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Add express middleware for health routes
  app.use(express.json());
  app.use('/health', healthRouter);

  // Get monitoring services
  const metricsService = app.get(MetricsService);
  const tracingService = app.get(TracingService);

  // Start tracing
  await tracingService.start();

  // Run database migrations on startup
  try {
    const migrationService = app.get(MigrationService);
    await migrationService.migrate();
    Logger.log('Database migrations completed successfully');

    // Run seed service after migrations
    try {
      const seedService = app.get(SeedService);
      await seedService.seed();
      Logger.log('Database seeding completed successfully');
    } catch (seedError) {
      Logger.error('Failed to seed database', seedError);
      // Don't exit - allow the app to start even if seeding fails
    }
  } catch (error) {
    Logger.error('Failed to run migrations on startup', error);
    // Don't exit - allow the app to start even if migrations fail
  }

  try {
    // Setup gRPC Microservice
    app.connectMicroservice<MicroserviceOptions>({
      transport: Transport.GRPC,
      options: {
        package: 'auth',
        protoPath: join(
          process.cwd(),
          'libs/shared/dto/src/lib/auth/auth.proto'
        ),
        url: '0.0.0.0:50052',
        loader: {
          keepCase: true,
          longs: Number,
          enums: String,
          defaults: true,
          oneofs: true,
        },
        maxReceiveMessageLength: 50 * 1024 * 1024,
        maxSendMessageLength: 50 * 1024 * 1024,
      },
    });
    Logger.log('✅ prefix bind done');
  } catch (e) {
    Logger.debug('❌ connect to microservice failed failed');
  }

  try {
    const globalPrefix = 'api';
    app.setGlobalPrefix(globalPrefix);
    const port = process.env.PORT || 5003;
    Logger.debug('🟡 About to call app.listen...');

    await app.startAllMicroservices();
    Logger.log('✅ Microservices started');

    // Add more detailed error handling and logging
    try {
      await app.listen(port);
      Logger.log(`✅ HTTP server started at http://localhost:${port}`);
      Logger.log(
        `🚀 Auth Service is running on: http://localhost:${port}/${globalPrefix}`
      );
      Logger.log(
        `📊 Metrics server is running on: http://localhost:${port}/api/metrics`
      );
      Logger.log(`🚀 gRPC server is running on: 0.0.0.0:50052`);
    } catch (listenError) {
      Logger.error('❌ Failed to start HTTP server', {
        error: listenError,
        stack: listenError.stack,
        port: port,
      });
      // Attempt to get more information about the port
      const util = require('util');
      const exec = util.promisify(require('child_process').exec);
      try {
        const { stdout } = await exec(`lsof -i :${port}`);
        Logger.error(`Port ${port} might be in use:\n${stdout}`);
      } catch (portCheckError) {
        Logger.error(`Could not check port status: ${portCheckError.message}`);
      }
      throw listenError;
    }
  } catch (e) {
    Logger.error('❌ Failed in main bootstrap try block', {
      error: e,
      stack: e.stack,
    });
    process.exit(1);
  }
}

bootstrap();
