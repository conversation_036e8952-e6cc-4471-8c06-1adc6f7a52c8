const fs = require('fs');

// Read the old JSON
const oldData = JSON.parse(fs.readFileSync('old-permissions.json', 'utf8'));

const newData = oldData.map((roleBlock) => ({
  role: roleBlock.role,
  department: roleBlock.department,
  modules: (roleBlock.features || []).map((moduleBlock) => ({
    module: moduleBlock.feature,
    features: (moduleBlock.actions || []).map((actionBlock) => ({
      feature: actionBlock.action,
      permissions: actionBlock.permissions,
      subFeatures: (actionBlock.subActions || []).map((sub) => ({
        subFeature: sub.subAction,
        permissions: sub.permissions,
      })),
    })),
  })),
}));

fs.writeFileSync('new-permissions.json', JSON.stringify(newData, null, 2));
console.log('✅ new-permissions.json created!');
