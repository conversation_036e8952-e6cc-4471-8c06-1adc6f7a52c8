import { Controller, Logger } from '@nestjs/common';
import { GrpcMethod } from '@nestjs/microservices';
import { AuthService } from './auth.service';
import { status } from '@grpc/grpc-js';
import {
  LoginRequest,
  LoginResponse,
  LogoutRequest,
  LogoutResponse,
  RefreshTokenRequest,
  RefreshTokenResponse,
  RegisterRequest,
  RegisterResponse,
  ValidateTokenRequest,
  ValidateTokenResponse,
  GenerateOtpRequest,
  GenerateOtpResponse,
  VerifyOtpRequest,
  VerifyOtpResponse,
  SsoAuthRequest,
  SsoAuthResponse,
  ForgotPasswordRequest,
  ForgotPasswordResponse,
  ResetPasswordRequest,
  ResetPasswordResponse,
  VerifyResetTokenRequest,
  VerifyResetTokenResponse,
} from './auth.interfaces';

@Controller()
export class AuthGrpcController {
  private readonly logger = new Logger(AuthGrpcController.name);

  constructor(private readonly authService: AuthService) {}

  @GrpcMethod('AuthService', 'Register')
  async register(request: RegisterRequest): Promise<RegisterResponse> {
    try {
      this.logger.debug(`gRPC Register request for user: ${request.email}`);
      this.logger.debug(`gRPC Register request for role: ${request.roleName}`);
      return await this.authService.registerGrpc(request);
    } catch (error) {
      this.logger.error(`Register error: ${error.message}`, error.stack);
      throw {
        code: status.INTERNAL,
        message: error.message || 'Registration failed',
        details: error.stack || '',
      };
    }
  }

  // Authentication methods
  @GrpcMethod('AuthService', 'Login')
  async login(request: LoginRequest): Promise<LoginResponse> {
    try {
      this.logger.log(`gRPC Login request for user: ${request.email}`);
      return await this.authService.loginGrpc(request);
    } catch (error) {
      this.logger.error(`Login error: ${error.message}`, error.stack);
      throw {
        code: status.INTERNAL,
        message: error.message || 'Authentication failed',
        details: error.stack || '',
      };
    }
  }

  @GrpcMethod('AuthService', 'Logout')
  async logout(request: LogoutRequest): Promise<LogoutResponse> {
    try {
      this.logger.log(`gRPC Logout request for token: ${request.accessToken}`);
      return await this.authService.logoutGrpc(request);
    } catch (error) {
      this.logger.error(`Logout error: ${error.message}`, error.stack);
      throw {
        code: status.INTERNAL,
        message: error.message || 'Logout failed',
        details: error.stack || '',
      };
    }
  }

  @GrpcMethod('AuthService', 'ValidateToken')
  async validateToken(
    request: ValidateTokenRequest
  ): Promise<ValidateTokenResponse> {
    try {
      this.logger.log('gRPC ValidateToken request');
      return await this.authService.validateTokenGrpc(request);
    } catch (error) {
      this.logger.error(`ValidateToken error: ${error.message}`, error.stack);
      throw {
        code: status.INTERNAL,
        message: error.message || 'Token validation failed',
        details: error.stack || '',
      };
    }
  }

  @GrpcMethod('AuthService', 'RefreshToken')
  async refreshToken(
    request: RefreshTokenRequest
  ): Promise<RefreshTokenResponse> {
    try {
      this.logger.log(`gRPC RefreshToken request`);
      return await this.authService.refreshTokenGrpc(request);
    } catch (error) {
      this.logger.error(`RefreshToken error: ${error.message}`, error.stack);
      throw {
        code: status.INTERNAL,
        message: error.message || 'Refresh token failed',
        details: error.stack || '',
      };
    }
  }

  @GrpcMethod('AuthService', 'GenerateOtp')
  async generateOtp(request: GenerateOtpRequest): Promise<GenerateOtpResponse> {
    try {
      this.logger.log(`gRPC GenerateOtp request for email: ${request.email}`);
      return await this.authService.generateOtpGrpc(request);
    } catch (error) {
      this.logger.error(`GenerateOtp error: ${error.message}`, error.stack);
      throw {
        code: status.INTERNAL,
        message: error.message || 'Failed to generate OTP',
        details: error.stack || '',
      };
    }
  }

  @GrpcMethod('AuthService', 'VerifyOtp')
  async verifyOtp(request: VerifyOtpRequest): Promise<VerifyOtpResponse> {
    try {
      this.logger.log(`gRPC VerifyOtp request for email: ${request.email}`);
      return await this.authService.verifyOtpGrpc(request);
    } catch (error) {
      this.logger.error(`VerifyOtp error: ${error.message}`, error.stack);
      throw {
        code: status.INTERNAL,
        message: error.message || 'Failed to verify OTP',
        details: error.stack || '',
      };
    }
  }

  @GrpcMethod('AuthService', 'SsoAuth')
  async ssoAuth(request: SsoAuthRequest): Promise<SsoAuthResponse> {
    try {
      this.logger.log(`gRPC SsoAuth request for provider: ${request.provider}`);
      return await this.authService.ssoAuthGrpc(request);
    } catch (error) {
      this.logger.error(`SsoAuth error: ${error.message}`, error.stack);
      throw {
        code: status.INTERNAL,
        message: error.message || 'SSO authentication failed',
        details: error.stack || '',
      };
    }
  }

  // ─── Password Reset Methods ─────────────────────────────────────────────────
  @GrpcMethod('AuthService', 'ForgotPassword')
  async forgotPassword(
    request: ForgotPasswordRequest
  ): Promise<ForgotPasswordResponse> {
    try {
      this.logger.log(
        `gRPC ForgotPassword request for email: ${request.email}`
      );
      return await this.authService.forgotPassword(request);
    } catch (error) {
      this.logger.error(`ForgotPassword error: ${error.message}`, error.stack);
      return {
        success: false,
        message: error.message || 'Failed to process password reset request',
      };
    }
  }

  @GrpcMethod('AuthService', 'VerifyResetToken')
  async verifyResetToken(
    request: VerifyResetTokenRequest
  ): Promise<VerifyResetTokenResponse> {
    try {
      this.logger.log(
        `gRPC VerifyResetToken request for email: ${request.email}`
      );
      return await this.authService.verifyResetToken(request);
    } catch (error) {
      this.logger.error(
        `VerifyResetToken error: ${error.message}`,
        error.stack
      );
      return {
        valid: false,
        message: error.message || 'Failed to verify reset token',
      };
    }
  }

  @GrpcMethod('AuthService', 'ResetPassword')
  async resetPassword(
    request: ResetPasswordRequest
  ): Promise<ResetPasswordResponse> {
    try {
      this.logger.log(`gRPC ResetPassword request for email: ${request.email}`);
      return await this.authService.resetPassword(request);
    } catch (error) {
      this.logger.error(`ResetPassword error: ${error.message}`, error.stack);
      return {
        success: false,
        message: error.message || 'Failed to reset password',
      };
    }
  }
}
