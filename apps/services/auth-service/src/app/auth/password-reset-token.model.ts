import {
  Table,
  Column,
  DataType,
  ForeignKey,
  BelongsTo,
  Index,
} from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
import { User } from '../user/model/user.model';

@Table({
  tableName: 'password_reset_tokens',
  timestamps: true,
  indexes: [
    {
      fields: ['email'],
      name: 'idx_password_reset_tokens_email',
    },
    {
      fields: ['token'],
      name: 'idx_password_reset_tokens_token',
      unique: true,
    },
    {
      fields: ['expiresAt'],
      name: 'idx_password_reset_tokens_expires_at',
    },
  ],
})
export class PasswordResetToken extends BaseModel {
  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  email: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    unique: true,
  })
  token: string;

  @Column({
    type: DataType.DATE,
    allowNull: false,
  })
  expiresAt: Date;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false,
  })
  used: boolean;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  usedAt?: Date;

  @ForeignKey(() => User)
  @Column({
    type: DataType.BIGINT,
    allowNull: true,
  })
  userId?: number;

  @BelongsTo(() => User, {
    foreignKey: 'userId',
    as: 'user',
  })
  user?: User;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  ipAddress?: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  userAgent?: string;

  // Helper method to check if token is expired
  isExpired(): boolean {
    return new Date() > this.expiresAt;
  }

  // Helper method to check if token is valid (not used and not expired)
  isValid(): boolean {
    return !this.used && !this.isExpired();
  }
}
