import { Module } from '@nestjs/common';
import { AuthGrpcController } from './auth.grpc.controller';
import { AuthService } from './auth.service';
import { AuditModule } from '../audit/audit.module';
import { SequelizeModule } from '@nestjs/sequelize';
import { User } from '../user/model/user.model';
import { UserRole } from '../user/model/user-role.model';
import { CoreModule } from '../core/core.module';
import {
  AuthenticationModule,
  JwtService,
  OtpModule,
  SSOModule,
} from '@apply-goal-backend/auth';
import { Token } from './token.model';
import { PasswordResetToken } from './password-reset-token.model';
import { PasswordResetCleanupService } from './password-reset-cleanup.service';
import { MessagingModule } from '@apply-goal-backend/messaging';
import { SocialSite } from '../user/model/social-site.model';
import { DatabaseModule } from '../migration/database.module';
import { Feature } from '../user/model/feature.model';
import { SubFeature } from '../user/model/sub-feature.model';
import { Module as ModuleModel } from '../user/model/module.model';
import { Role } from '../user/model/role.model';
import { RoleSubFeaturePermission } from '../user/model/role-sub-feature-permission.model';
import { RoleFeaturePermission } from '../user/model/role-feature-permission.model';
import { Department } from '../user/model/department.model';
import { UserDepartment } from '../user/model/user‐department.model';
import { DepartmentFeaturePermission } from '../user/model/department-feature-permission.model';
import { DepartmentSubFeaturePermission } from '../user/model/department-sub-feature-permission.model';
import { EmployeePersonal } from '../employee/models/employee-personal.model';
import { EmployeeAddress } from '../employee/models/employee-address.model';
import { EmployeeEmergencyContact } from '../employee/models/employee-emergency-contact.model';
import { EmployeeIdentityDoc } from '../employee/models/employee-identity-doc.model';
import { EmployeeBankAccount } from '../employee/models/employee-bank-account.model';
import { Organization } from '../organization/organization.model';
import { OrganizationService } from '../organization/organization.service';
import { EmployeeController } from '../employee/employee.controller';
import { EmployeeService } from '../employee/employee.service';
import { RoleController } from '../role/role.controller';
import { RoleService } from '../role/role.service';
import { DepartmentController } from '../department/department.controller';
import { DepartmentService } from '../department/department.service';

@Module({
  imports: [
    AuditModule,
    DatabaseModule,
    SequelizeModule.forFeature([
      Feature,
      SubFeature,
      Department,
      UserDepartment,
      DepartmentFeaturePermission,
      DepartmentSubFeaturePermission,
      ModuleModel, // Module
      Role,
      RoleFeaturePermission,
      RoleSubFeaturePermission,
      User,
      UserRole,
      Token,
      PasswordResetToken,
      SocialSite,
      EmployeePersonal,
      EmployeeAddress,
      EmployeeEmergencyContact,
      EmployeeIdentityDoc,
      EmployeeBankAccount,
      Organization,
    ]),
    CoreModule,
    AuthenticationModule.forRoot({
      secret: process.env.JWT_SECRET || 'secret',
      expiresIn: '1h',
      refreshExpiresIn: '7d',
    }),
    OtpModule.forRoot({
      redis: {
        host: process.env.REDIS_HOST || 'redis',
        port: parseInt(process.env.REDIS_PORT || '6379', 10),
        password: process.env.REDIS_PASSWORD,
        db: parseInt(process.env.REDIS_DB || '0', 10),
      },
    }),
    MessagingModule.forRoot({
      uri: process.env.RABBITMQ_URI || 'amqp://localhost:5672',
      exchange: process.env.RABBITMQ_EXCHANGE || 'messaging_exchange',
    }),
    SSOModule,
  ],
  controllers: [
    AuthGrpcController,
    EmployeeController,
    RoleController,
    DepartmentController,
  ],
  providers: [
    AuthService,
    OrganizationService,
    EmployeeService,
    RoleService,
    DepartmentService,
    PasswordResetCleanupService,
  ],
  exports: [AuthService, OrganizationService],
})
export class AuthModule {}
