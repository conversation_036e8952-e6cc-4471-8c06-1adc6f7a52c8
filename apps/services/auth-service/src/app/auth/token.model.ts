import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
  Default,
  Index,
  PrimaryKey,
  AutoIncrement,
} from 'sequelize-typescript';
import { User } from '../user/model/user.model';

@Table({
  tableName: 'tokens',
  timestamps: true,
  paranoid: true, // soft deletes
})
export class Token extends Model<Token> {
  @ForeignKey(() => User)
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  userId: bigint;

  @BelongsTo(() => User)
  user: User;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
  })
  accessToken: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
  })
  refreshToken: string;

  @Column({
    type: DataType.DATE,
    allowNull: false,
  })
  accessTokenExpiresAt: Date;

  @Column({
    type: DataType.DATE,
    allowNull: false,
  })
  refreshTokenExpiresAt: Date;

  @Default(false)
  @Column({
    type: DataType.BOOLEAN,
  })
  revoked: boolean;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  userAgent: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  ipAddress: string;
}
