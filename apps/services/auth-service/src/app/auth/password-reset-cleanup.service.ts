import { Injectable, Logger } from '@nestjs/common';
import { AuthService } from './auth.service';

@Injectable()
export class PasswordResetCleanupService {
  private readonly logger = new Logger(PasswordResetCleanupService.name);

  constructor(private readonly authService: AuthService) {}

  // Manual cleanup method that can be called periodically
  async cleanupExpiredTokens() {
    try {
      this.logger.log('Starting cleanup of expired password reset tokens...');
      await this.authService.cleanupExpiredResetTokens();
      this.logger.log('Completed cleanup of expired password reset tokens');
    } catch (error) {
      this.logger.error(
        'Failed to cleanup expired password reset tokens:',
        error
      );
    }
  }

  // Daily cleanup method
  async dailyCleanup() {
    try {
      this.logger.log(
        'Starting daily cleanup of expired password reset tokens...'
      );
      await this.authService.cleanupExpiredResetTokens();
      this.logger.log(
        'Completed daily cleanup of expired password reset tokens'
      );
    } catch (error) {
      this.logger.error(
        'Failed to perform daily cleanup of expired password reset tokens:',
        error
      );
    }
  }
}
