import { Injectable, Logger } from '@nestjs/common';
import { join } from 'path';
import { Sequelize } from 'sequelize-typescript';
import { Umzug, SequelizeStorage } from 'umzug';

@Injectable()
export class MigrationService {
  private readonly logger = new Logger(MigrationService.name);
  private readonly umzug: Umzug;

  constructor(private readonly sequelize: Sequelize) {
    this.umzug = new Umzug({
      migrations: {
        glob: join(__dirname, '../../migrations/*.js'),
      },
      context: sequelize.getQueryInterface(),
      storage: new SequelizeStorage({ sequelize }),
      logger: console,
    });
  }

  async syncDatabase() {
    try {
      this.logger.log('Starting database sync...');
      await this.sequelize.sync({ force: false });
      this.logger.log('Database sync completed successfully');
    } catch (error) {
      this.logger.error('Database sync failed:', error);
      throw error;
    }
  }

  async migrate() {
    try {
      this.logger.log('Starting database migration...');
      await this.syncDatabase(); // First sync the database
      
      this.logger.log(`Looking for migrations in: ${join(__dirname, '../../migrations/*.js')}`);
      const pending = await this.umzug.pending();
      this.logger.log(`Found ${pending.length} pending migrations`);
      
      const executed = await this.umzug.up();
      this.logger.log(`Executed ${executed.length} migrations`);
      
      this.logger.log('Migrations executed successfully');
      return executed;
    } catch (error) {
      this.logger.error('Migration failed:', error);
      throw error;
    }
  }

  async getMigrationStatus() {
    try {
      const pending = await this.umzug.pending();
      const executed = await this.umzug.executed();

      return {
        pending: pending.map(m => m.name),
        executed: executed.map(m => m.name),
        isUpToDate: pending.length === 0
      };
    } catch (error) {
      this.logger.error('Failed to get migration status:', error);
      throw error;
    }
  }
}
