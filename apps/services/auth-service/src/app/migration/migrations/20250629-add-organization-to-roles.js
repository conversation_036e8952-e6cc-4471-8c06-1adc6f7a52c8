'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // Add organizationId column to roles table
    await queryInterface.addColumn('roles', 'organizationId', {
      type: Sequelize.BIGINT,
      allowNull: true,
      references: {
        model: 'organizations',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });

    // Add description column to roles table
    await queryInterface.addColumn('roles', 'description', {
      type: Sequelize.TEXT,
      allowNull: true,
    });

    // Add isSystemRole column to roles table
    await queryInterface.addColumn('roles', 'isSystemRole', {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    });

    // Add index for organizationId
    await queryInterface.addIndex('roles', ['organizationId'], {
      name: 'idx_roles_organization_id',
    });

    // Add composite index for organization-specific role names
    await queryInterface.addIndex('roles', ['organizationId', 'name'], {
      name: 'idx_roles_org_name',
      unique: false, // Allow same role name across different organizations
    });

    // Update existing roles to be system roles (for backward compatibility)
    await queryInterface.sequelize.query(`
      UPDATE roles 
      SET "isSystemRole" = true, 
          "organizationId" = NULL 
      WHERE "organizationId" IS NULL
    `);
  },

  async down(queryInterface, Sequelize) {
    // Remove indexes
    await queryInterface.removeIndex('roles', 'idx_roles_org_name');
    await queryInterface.removeIndex('roles', 'idx_roles_organization_id');

    // Remove columns
    await queryInterface.removeColumn('roles', 'isSystemRole');
    await queryInterface.removeColumn('roles', 'description');
    await queryInterface.removeColumn('roles', 'organizationId');
  },
};
