import { Injectable, OnModuleInit } from '@nestjs/common';
import { MetricsService } from '@apply-goal-backend/monitoring';

@Injectable()
export class AppService implements OnModuleInit {
  // make these static so they're shared across all instances
  private static metricsInitialized = false;
  private static requestCounter: any;
  private static requestDuration: any;
  private static authenticationCounter: any;
  private static authorizationCounter: any;
  private static tokenOperationsHistogram: any;
  private static activeSessionsGauge: any;
  private static loginAttemptsCounter: any;
  private static auditLogRequestCounter: any;
  private static auditLogRequestDuration: any;

  constructor(private readonly metricsService: MetricsService) {}

  onModuleInit() {
    if (!AppService.metricsInitialized) {
      console.log('Initializing metrics for auth-service...');
      this.initializeMetrics();
      AppService.metricsInitialized = true;
    } else {
      console.log('Metrics already initialized, skipping...');
    }
  }

  private initializeMetrics() {
    // double-check guard
    if (AppService.requestCounter) {
      return;
    }

    try {
      AppService.requestCounter = this.metricsService.createCounter(
        'http_requests_total',
        'Total HTTP requests',
        ['method', 'endpoint', 'status']
      );
      AppService.requestDuration = this.metricsService.createHistogram(
        'http_request_duration_seconds',
        'HTTP request duration',
        ['method', 'endpoint']
      );
      AppService.authenticationCounter = this.metricsService.createCounter(
        'authentication_attempts_total',
        'Total authentication attempts',
        ['result', 'reason']
      );
      AppService.authorizationCounter = this.metricsService.createCounter(
        'authorization_attempts_total',
        'Total authorization attempts',
        ['type', 'resource', 'result']
      );
      AppService.tokenOperationsHistogram = this.metricsService.createHistogram(
        'token_operation_duration_seconds',
        'Token operation duration',
        ['operation']
      );
      AppService.activeSessionsGauge = this.metricsService.createGauge(
        'active_sessions',
        'Number of active sessions',
        ['type']
      );
      AppService.loginAttemptsCounter = this.metricsService.createCounter(
        'login_attempts_total',
        'Total login attempts',
        ['result', 'reason']
      );
      AppService.auditLogRequestCounter = this.metricsService.createCounter(
        'audit_log_requests_total',
        'Total audit log requests',
        ['operation', 'status']
      );
      AppService.auditLogRequestDuration = this.metricsService.createHistogram(
        'audit_log_request_duration_seconds',
        'Audit log request duration',
        ['operation']
      );
      console.log('Metrics initialized successfully');
    } catch (error) {
      console.warn(
        'Error initializing metrics, they may already be registered:',
        error.message
      );
      // prevent retry
      AppService.metricsInitialized = true;
    }
  }

  // now refer to the static metrics in your tracking methods:

  trackLoginAttempt(result: string, reason: string) {
    try {
      AppService.loginAttemptsCounter?.inc({ result, reason });
    } catch (error) {
      console.warn('Error tracking login attempt metric:', error.message);
    }
  }

  incrementActiveSessions(type: string) {
    try {
      AppService.activeSessionsGauge?.inc({ type });
    } catch (error) {
      console.warn('Error incrementing active sessions metric:', error.message);
    }
  }

  decrementActiveSessions(type: string) {
    try {
      AppService.activeSessionsGauge?.dec({ type });
    } catch (error) {
      console.warn('Error decrementing active sessions metric:', error.message);
    }
  }

  trackTokenOperation(operation: string, duration: number) {
    try {
      AppService.tokenOperationsHistogram?.observe({ operation }, duration);
    } catch (error) {
      console.warn('Error tracking token operation metric:', error.message);
    }
  }

  trackAuthorization(type: string, resource: string, result: string) {
    try {
      AppService.authorizationCounter?.inc({ type, resource, result });
    } catch (error) {
      console.warn('Error tracking authorization metric:', error.message);
    }
  }

  trackAuditLogRequest(operation: string, status: string, duration: number) {
    try {
      AppService.auditLogRequestCounter?.inc({ operation, status });
      AppService.auditLogRequestDuration?.observe({ operation }, duration);
    } catch (error) {
      console.warn('Error tracking audit log request metric:', error.message);
    }
  }

  trackHttpRequest(
    method: string,
    endpoint: string,
    status: string,
    duration: number
  ) {
    try {
      AppService.requestCounter?.inc({ method, endpoint, status });
      AppService.requestDuration?.observe({ method, endpoint }, duration);
    } catch (error) {
      console.warn('Error tracking HTTP request metric:', error.message);
    }
  }

  getData() {
    return { message: 'Hello API' };
  }
}
