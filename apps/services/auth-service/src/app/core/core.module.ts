import { Global, Module } from '@nestjs/common';
import { AppService } from '../app.service';
import { MonitoringModule } from '@apply-goal-backend/monitoring';

@Global()
@Module({
  imports: [
    MonitoringModule.forRoot({
      metrics: {
        serviceName: 'auth-service',
        serviceVersion: '1.0.0',
        port: parseInt(process.env.METRICS_PORT || '5003', 10),
        path: '/api/metrics',
        labels: {
          environment: process.env.NODE_ENV || 'development'
        }
      },
      tracing: {
        serviceName: 'auth-service',
        jaegerEndpoint: 'http://jaeger:4318/v1/traces'
      }
    })
  ],
  providers: [AppService],
  exports: [AppService, MonitoringModule],
})
export class CoreModule {}
