import { Controller, Logger } from '@nestjs/common';
import { GrpcMethod } from '@nestjs/microservices';
import {
  DepartmentService,
  CreateDepartmentRequest,
  UpdateDepartmentRequest,
  ListDepartmentsRequest,
  DepartmentResponse,
  GetDepartmentRequest,
  DeleteDepartmentRequest,
  DeleteDepartmentResponse,
  GetOrganizationDepartmentsRequest,
  GetOrganizationDepartmentsResponse,
} from './department.service';

@Controller()
export class DepartmentController {
  private readonly logger = new Logger(DepartmentController.name);

  constructor(private readonly departmentService: DepartmentService) {}

  @GrpcMethod('AuthService', 'CreateDepartment')
  async createDepartment(request: any): Promise<any> {
    try {
      this.logger.log(`gRPC CreateDepartment request`);
      this.logger.debug(`Request data: ${JSON.stringify(request, null, 2)}`);

      // Handle bulk department creation from proto (departments array)
      if (request.departments && Array.isArray(request.departments)) {
        return await this.createBulkDepartments(request);
      }

      // Handle single department creation (if organizationId is provided)
      if (request.organizationId && request.name) {
        return await this.departmentService.createDepartment(
          request as CreateDepartmentRequest
        );
      }

      return {
        success: false,
        message:
          'Invalid request format - expected departments array or single department with organizationId',
      };
    } catch (error) {
      this.logger.error(
        `CreateDepartment error: ${error.message}`,
        error.stack
      );
      return {
        success: false,
        message: error.message || 'Failed to create department',
      };
    }
  }

  private async createBulkDepartments(request: any): Promise<any> {
    try {
      this.logger.log(
        `Creating ${
          request.departments?.length || 0
        } departments with proper hierarchy`
      );

      // Use provided organizationId or default to 1
      const organizationId = request.organizationId || 1;
      this.logger.log(`Using organization ID: ${organizationId}`);

      const createdDepartments = [];
      const departmentMap = new Map<string, number>(); // name -> id mapping

      // First, get all existing departments to avoid duplicates
      const existingDepts = await this.departmentService.getAllDepartments(
        false
      );
      existingDepts.forEach((dept) => {
        if (dept.organizationId === organizationId) {
          departmentMap.set(dept.name, dept.id);
        }
      });

      // Sort departments by dependency order (parents before children)
      const sortedDepartments = this.sortDepartmentsByDependency(
        request.departments || []
      );

      for (const deptInfo of sortedDepartments) {
        try {
          let parentId = null;

          // Handle parent department
          if (deptInfo.parent && deptInfo.parent.trim() !== '') {
            if (departmentMap.has(deptInfo.parent)) {
              parentId = departmentMap.get(deptInfo.parent);
              this.logger.log(
                `Found parent ${deptInfo.parent} with ID: ${parentId}`
              );
            } else {
              this.logger.warn(
                `Parent department '${deptInfo.parent}' not found for '${deptInfo.name}'`
              );
              // Skip this department if parent doesn't exist
              continue;
            }
          }

          // Check if department already exists
          if (departmentMap.has(deptInfo.name)) {
            this.logger.log(
              `Department ${deptInfo.name} already exists, skipping`
            );
            continue;
          }

          // Create the department
          const result = await this.departmentService.createDepartment({
            name: deptInfo.name,
            organizationId,
            parentId,
          });

          if (result.success && result.department) {
            createdDepartments.push(result.department);
            departmentMap.set(deptInfo.name, result.department.id);
            this.logger.log(
              `Created department: ${deptInfo.name} (ID: ${
                result.department.id
              }) with parent: ${parentId || 'none'}`
            );
          }
        } catch (deptError) {
          this.logger.warn(
            `Failed to create department ${deptInfo.name}: ${deptError.message}`
          );
          // Continue with other departments
        }
      }

      return {
        success: true,
        message: `Processed ${createdDepartments.length} departments successfully`,
        departments: createdDepartments,
      };
    } catch (error) {
      this.logger.error(
        `Bulk department creation error: ${error.message}`,
        error.stack
      );
      return {
        success: false,
        message: 'Failed to create departments',
      };
    }
  }

  private sortDepartmentsByDependency(departments: any[]): any[] {
    const sorted = [];
    const processed = new Set<string>();
    const processing = new Set<string>();

    const processDepartment = (dept: any) => {
      // If already processed, skip
      if (processed.has(dept.name)) {
        return;
      }

      // Check for circular dependency
      if (processing.has(dept.name)) {
        this.logger.warn(
          `Circular dependency detected for department: ${dept.name}`
        );
        return;
      }

      processing.add(dept.name);

      // If has parent, process parent first
      if (dept.parent && dept.parent.trim() !== '') {
        const parentDept = departments.find((d) => d.name === dept.parent);
        if (parentDept && !processed.has(parentDept.name)) {
          processDepartment(parentDept);
        }
      }

      // Add current department to sorted list
      if (!processed.has(dept.name)) {
        sorted.push(dept);
        processed.add(dept.name);
      }

      processing.delete(dept.name);
    };

    // Process all departments
    departments.forEach((dept) => processDepartment(dept));

    this.logger.log(`Sorted ${sorted.length} departments by dependency order`);
    return sorted;
  }

  @GrpcMethod('AuthService', 'UpdateDepartment')
  async updateDepartment(
    request: UpdateDepartmentRequest
  ): Promise<DepartmentResponse> {
    try {
      this.logger.log(`gRPC UpdateDepartment request: ${request.id}`);
      return await this.departmentService.updateDepartment(request);
    } catch (error) {
      this.logger.error(
        `UpdateDepartment error: ${error.message}`,
        error.stack
      );
      return {
        success: false,
        message: error.message || 'Failed to update department',
      };
    }
  }

  @GrpcMethod('AuthService', 'ListDepartments')
  async listDepartments(request: any): Promise<any> {
    try {
      this.logger.log(`gRPC ListDepartments request`);
      this.logger.debug(`Request data: ${JSON.stringify(request, null, 2)}`);

      // Default to include hierarchy for better tree structure
      const includeHierarchy = request.includeHierarchy !== false; // Default to true

      // If organizationId is provided, use the specific organization listing
      if (request.organizationId) {
        return await this.departmentService.listDepartments({
          ...request,
          includeHierarchy,
        } as ListDepartmentsRequest);
      }

      // Otherwise, list all departments with hierarchy
      return await this.listAllDepartments(includeHierarchy);
    } catch (error) {
      this.logger.error(`ListDepartments error: ${error.message}`, error.stack);
      return {
        success: false,
        message: error.message || 'Failed to list departments',
        departments: [],
      };
    }
  }

  private async listAllDepartments(
    includeHierarchy: boolean = true
  ): Promise<any> {
    try {
      // Fetch all departments across all organizations with hierarchy
      const departments = await this.departmentService.getAllDepartments(
        includeHierarchy
      );

      return {
        success: true,
        message: `Departments retrieved successfully${
          includeHierarchy ? ' with hierarchy' : ''
        }`,
        departments: departments,
      };
    } catch (error) {
      this.logger.error(
        `Error listing all departments: ${error.message}`,
        error.stack
      );
      throw new Error('Failed to list all departments');
    }
  }

  @GrpcMethod('AuthService', 'GetDepartment')
  async getDepartment(
    request: GetDepartmentRequest
  ): Promise<DepartmentResponse> {
    try {
      this.logger.log(`gRPC GetDepartment request: ${request.id}`);
      const department = await this.departmentService.getDepartmentById(
        request.id
      );
      return {
        success: true,
        message: 'Department retrieved successfully',
        department,
      };
    } catch (error) {
      this.logger.error(`GetDepartment error: ${error.message}`, error.stack);
      return {
        success: false,
        message: error.message || 'Failed to get department',
      };
    }
  }

  @GrpcMethod('AuthService', 'DeleteDepartment')
  async deleteDepartment(
    request: DeleteDepartmentRequest
  ): Promise<DeleteDepartmentResponse> {
    try {
      this.logger.log(`gRPC DeleteDepartment request: ${request.id}`);
      return await this.departmentService.deleteDepartment(request.id);
    } catch (error) {
      this.logger.error(
        `DeleteDepartment error: ${error.message}`,
        error.stack
      );
      return {
        success: false,
        message: error.message || 'Failed to delete department',
      };
    }
  }

  @GrpcMethod('AuthService', 'GetOrganizationDepartments')
  async getOrganizationDepartments(
    request: GetOrganizationDepartmentsRequest
  ): Promise<GetOrganizationDepartmentsResponse> {
    try {
      this.logger.log(
        `gRPC GetOrganizationDepartments request: ${request.organizationId}`
      );
      const departments =
        await this.departmentService.getDepartmentsForOrganization(
          request.organizationId,
          request.includeHierarchy
        );
      return {
        success: true,
        message: 'Organization departments retrieved successfully',
        departments,
      };
    } catch (error) {
      this.logger.error(
        `GetOrganizationDepartments error: ${error.message}`,
        error.stack
      );
      return {
        success: false,
        message: error.message || 'Failed to get organization departments',
        departments: [],
      };
    }
  }
}
