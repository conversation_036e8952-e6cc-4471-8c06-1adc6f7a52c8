export interface CreateEmployeeRequest {
  // User creation fields
  email: string;
  password?: string;
  name?: string;
  phone?: string;
  departmentName?: string;
  organizationName?: string;
  employeeRoleName?: string; // Role for the employee user (e.g., "Employee", "Staff")

  // Employee specific fields
  userId?: bigint; // Optional - will be set after user creation
  lastName?: string;
  firstName?: string;
  joiningDate?: string;
  jobType?: string;
  jobStatus?: string;
  dateOfBirth?: string;
  bloodGroup?: string;
  gender?: string;
  agencyId?: bigint;
  orgId?: string;
  addresses?: EmployeeAddressInfo[];
  emergencyContacts?: EmployeeEmergencyContactInfo[];
  identityDocs?: EmployeeIdentityDocInfo[];
  bankAccounts?: EmployeeBankAccountInfo[];

  // Audit fields
  requestUserId: bigint;
  roleName: string;
  ipAddress: string;
  userAgent: string;
}

export interface UpdateEmployeeRequest {
  id: bigint;
  lastName?: string;
  firstName?: string;
  joiningDate?: string;
  jobType?: string;
  jobStatus?: string;
  dateOfBirth?: string;
  bloodGroup?: string;
  gender?: string;
  agencyId?: bigint;
  orgId?: string;
  addresses?: EmployeeAddressInfo[];
  emergencyContacts?: EmployeeEmergencyContactInfo[];
  identityDocs?: EmployeeIdentityDocInfo[];
  bankAccounts?: EmployeeBankAccountInfo[];
  requestUserId: bigint;
  roleName: string;
  ipAddress: string;
  userAgent: string;
}

export interface GetEmployeeRequest {
  id: bigint;
  requestUserId: bigint;
  roleName: string;
  ipAddress: string;
  userAgent: string;
}

export interface ListEmployeesRequest {
  page?: number;
  limit?: number;
  search?: string;
  jobType?: string;
  jobStatus?: string;
  agencyId?: bigint;
  orgId?: string;
  requestUserId: bigint;
  roleName: string;
  ipAddress: string;
  userAgent: string;
}

export interface RemoveEmployeeRequest {
  id: bigint;
  requestUserId: bigint;
  roleName: string;
  ipAddress: string;
  userAgent: string;
}

export interface EmployeeResponse {
  success: boolean;
  message: string;
  employee?: EmployeeInfo;
}

export interface ListEmployeesResponse {
  success: boolean;
  message: string;
  employees: EmployeeInfo[];
  total: number;
  page: number;
  limit: number;
}

export interface RemoveEmployeeResponse {
  success: boolean;
  message: string;
}

export interface EmployeeInfo {
  userId: bigint;
  lastName?: string;
  firstName?: string;
  joiningDate?: string;
  jobType?: string;
  jobStatus?: string;
  dateOfBirth?: string;
  bloodGroup?: string;
  gender?: string;
  agencyId?: bigint;
  orgId?: string;
  addresses?: EmployeeAddressInfo[];
  emergencyContacts?: EmployeeEmergencyContactInfo[];
  identityDocs?: EmployeeIdentityDocInfo[];
  bankAccounts?: EmployeeBankAccountInfo[];
  createdAt: Date;
  updatedAt: Date;
}

export interface EmployeeAddressInfo {
  id?: bigint;
  userId: bigint;
  addressType: string;
  addressLine?: string;
  country?: string;
  state?: string;
  city?: string;
  postalCode?: string;
}

export interface EmployeeEmergencyContactInfo {
  id?: bigint;
  userId: bigint;
  category?: string;
  name?: string;
  relationship?: string;
  address?: string;
  phoneNumber?: string;
  email?: string;
}

export interface EmployeeIdentityDocInfo {
  id?: bigint;
  userId: bigint;
  docType?: string;
  nationality?: string;
  issueDate?: string;
  expiryDate?: string;
}

export interface EmployeeBankAccountInfo {
  id?: bigint;
  userId: bigint;
  accountHolder?: string;
  accountNumber?: string;
  bankName?: string;
  branchName?: string;
}

// Internal service interfaces
export interface CreateEmployeeData {
  userId: bigint;
  lastName?: string;
  firstName?: string;
  joiningDate?: Date;
  jobType?: string;
  jobStatus?: string;
  dateOfBirth?: Date;
  bloodGroup?: string;
  gender?: string;
  agencyId?: bigint;
  orgId?: string;
}

export interface UpdateEmployeeData {
  lastName?: string;
  firstName?: string;
  joiningDate?: Date;
  jobType?: string;
  jobStatus?: string;
  dateOfBirth?: Date;
  bloodGroup?: string;
  gender?: string;
  agencyId?: bigint;
  orgId?: string;
}

export interface EmployeeFilters {
  search?: string;
  jobType?: string;
  jobStatus?: string;
  agencyId?: bigint;
  orgId?: string;
}

export interface PaginationOptions {
  page: number;
  limit: number;
  offset: number;
}

export const JOB_TYPES = [
  'full-time',
  'part-time',
  'contract',
  'intern',
  'consultant',
  'freelance',
] as const;

export const JOB_STATUSES = [
  'active',
  'inactive',
  'on-leave',
  'terminated',
  'resigned',
] as const;

export const BLOOD_GROUPS = [
  'A+',
  'A-',
  'B+',
  'B-',
  'AB+',
  'AB-',
  'O+',
  'O-',
] as const;

export const GENDERS = [
  'male',
  'female',
  'other',
  'prefer-not-to-say',
] as const;

export type JobType = (typeof JOB_TYPES)[number];
export type JobStatus = (typeof JOB_STATUSES)[number];
export type BloodGroup = (typeof BLOOD_GROUPS)[number];
export type Gender = (typeof GENDERS)[number];
