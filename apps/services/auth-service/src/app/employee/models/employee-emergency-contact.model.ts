import { BaseModel } from '@apply-goal-backend/database';
import {
  AutoIncrement,
  BelongsTo,
  Column,
  DataType,
  ForeignKey,
  PrimaryKey,
  Table,
} from 'sequelize-typescript';
import { EmployeePersonal } from './employee-personal.model';

@Table({ tableName: 'employee_emergency_contact' })
export class EmployeeEmergencyContact extends BaseModel {
  @ForeignKey(() => EmployeePersonal)
  @Column(DataType.BIGINT)
  userId!: number;

  @Column(DataType.STRING(50)) category?: string;
  @Column(DataType.STRING(200)) name?: string;
  @Column(DataType.STRING(100)) relationship?: string;
  @Column(DataType.TEXT) address?: string;
  @Column(DataType.STRING(50)) phoneNumber?: string;
  @Column(DataType.STRING(255)) email?: string;

  @BelongsTo(() => EmployeePersonal)
  employee?: EmployeePersonal;
}
