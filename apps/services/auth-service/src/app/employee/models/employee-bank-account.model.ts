import { BaseModel } from '@apply-goal-backend/database';
import {
  AutoI<PERSON>rement,
  BelongsTo,
  Column,
  DataType,
  ForeignKey,
  PrimaryKey,
  Table,
} from 'sequelize-typescript';
import { EmployeePersonal } from './employee-personal.model';

@Table({ tableName: 'employee_bank_account' })
export class EmployeeBankAccount extends BaseModel {
  @ForeignKey(() => EmployeePersonal)
  @Column(DataType.BIGINT)
  userId!: number;

  @Column(DataType.STRING(200)) accountHolder?: string;
  @Column(DataType.STRING(100)) accountNumber?: string;
  @Column(DataType.STRING(200)) bankName?: string;
  @Column(DataType.STRING(200)) branchName?: string;

  @BelongsTo(() => EmployeePersonal)
  employee?: EmployeePersonal;
}
