import { BaseModel } from '@apply-goal-backend/database';
import {
  AutoI<PERSON>rement,
  BelongsTo,
  Column,
  DataType,
  ForeignKey,
  PrimaryKey,
  Table,
} from 'sequelize-typescript';
import { EmployeePersonal } from './employee-personal.model';

@Table({ tableName: 'employee_identity_doc' })
export class EmployeeIdentityDoc extends BaseModel {
  @ForeignKey(() => EmployeePersonal)
  @Column(DataType.BIGINT)
  userId!: number;

  @Column(DataType.STRING(50)) docType?: string;
  @Column(DataType.STRING(100)) nationality?: string;
  @Column(DataType.DATE) issueDate?: Date;
  @Column(DataType.DATE) expiryDate?: Date;

  @BelongsTo(() => EmployeePersonal)
  employee?: EmployeePersonal;
}
