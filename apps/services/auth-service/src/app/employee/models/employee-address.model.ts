import { BaseModel } from '@apply-goal-backend/database';
import {
  AutoIncrement,
  BelongsTo,
  Column,
  DataType,
  ForeignKey,
  PrimaryKey,
  Table,
} from 'sequelize-typescript';
import { EmployeePersonal } from './employee-personal.model';

@Table({ tableName: 'employee_address' })
export class Employee<PERSON>ddress extends BaseModel {
  @ForeignKey(() => EmployeePersonal)
  @Column(DataType.BIGINT)
  userId!: number;

  @Column(DataType.STRING(20)) addressType!: string;
  @Column(DataType.TEXT) addressLine?: string;
  @Column(DataType.STRING(100)) country?: string;
  @Column(DataType.STRING(100)) state?: string;
  @Column(DataType.STRING(100)) city?: string;
  @Column(DataType.STRING(50)) postalCode?: string;

  @BelongsTo(() => EmployeePersonal)
  employee?: EmployeePersonal;
}
