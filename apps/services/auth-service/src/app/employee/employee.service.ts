import {
  Injectable,
  NotFoundException,
  Logger,
  BadRequestException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Op, UniqueConstraintError } from 'sequelize';
import { firstValueFrom } from 'rxjs';
import { EmployeePersonal } from './models/employee-personal.model';
import { EmployeeAddress } from './models/employee-address.model';
import { EmployeeEmergencyContact } from './models/employee-emergency-contact.model';
import { EmployeeIdentityDoc } from './models/employee-identity-doc.model';
import { EmployeeBankAccount } from './models/employee-bank-account.model';
import { CreateEmployeeDto } from './dto/create-employee.dto';
import { UpdateEmployeeDto } from './dto/update-employee.dto';
import { AuditClientService } from '../audit/audit.service';
import { AppService } from '../app.service';
import {
  CreateEmployeeRequest,
  UpdateEmployeeRequest,
  GetEmployeeRequest,
  ListEmployeesRequest,
  RemoveEmployeeRequest,
  EmployeeResponse,
  ListEmployeesResponse,
  RemoveEmployeeResponse,
  EmployeeInfo,
  CreateEmployeeData,
  UpdateEmployeeData,
  EmployeeAddressInfo,
  EmployeeEmergencyContactInfo,
  EmployeeIdentityDocInfo,
  EmployeeBankAccountInfo,
  JOB_TYPES,
  JOB_STATUSES,
  BLOOD_GROUPS,
  GENDERS,
} from './employee.interface';
import { CreateAuditLogRequest } from '../auth/auth.interfaces';
import { User } from '../user/model/user.model';
import { Role } from '../user/model/role.model';
import { UserRole } from '../user/model/user-role.model';
import { Department } from '../user/model/department.model';
import { UserDepartment } from '../user/model/user‐department.model';
import { Organization } from '../organization/organization.model';
import * as bcrypt from 'bcrypt';

@Injectable()
export class EmployeeService {
  private readonly logger = new Logger(EmployeeService.name);

  constructor(
    @InjectModel(EmployeePersonal)
    private readonly employeeModel: typeof EmployeePersonal,
    @InjectModel(EmployeeAddress)
    private readonly employeeAddressModel: typeof EmployeeAddress,
    @InjectModel(EmployeeEmergencyContact)
    private readonly employeeEmergencyContactModel: typeof EmployeeEmergencyContact,
    @InjectModel(EmployeeIdentityDoc)
    private readonly employeeIdentityDocModel: typeof EmployeeIdentityDoc,
    @InjectModel(EmployeeBankAccount)
    private readonly employeeBankAccountModel: typeof EmployeeBankAccount,
    @InjectModel(User)
    private readonly userModel: typeof User,
    @InjectModel(Role)
    private readonly roleModel: typeof Role,
    @InjectModel(UserRole)
    private readonly userRoleModel: typeof UserRole,
    @InjectModel(Department)
    private readonly departmentModel: typeof Department,
    @InjectModel(UserDepartment)
    private readonly userDepartmentModel: typeof UserDepartment,
    @InjectModel(Organization)
    private readonly organizationModel: typeof Organization,
    private readonly auditService: AuditClientService,
    private readonly appService: AppService
  ) {}

  // Audit-log helper, mirroring AuthService
  private async createAuditLog(payload: CreateAuditLogRequest) {
    const start = Date.now();
    try {
      await firstValueFrom(this.auditService.createAuditLog(payload));
      this.appService.trackAuditLogRequest(
        'create',
        'success',
        (Date.now() - start) / 1000
      );
    } catch (error) {
      this.appService.trackAuditLogRequest(
        'create',
        'error',
        (Date.now() - start) / 1000
      );
      this.logger.error('Failed to create audit log', error);
    }
  }

  // Metrics tracking helper
  private trackMetrics(
    operation: string,
    status: 'success' | 'failure' | 'error',
    duration: number
  ) {
    try {
      this.appService.trackAuthorization('employee', operation, status);
    } catch (err) {
      this.logger.warn(`Metrics error: ${err.message}`);
    }
  }

  async createEmployee(
    request: CreateEmployeeRequest
  ): Promise<EmployeeResponse> {
    const startTime = Date.now();
    let transaction: any;

    try {
      // Log the full request for debugging
      this.logger.debug(
        `CreateEmployee request received: ${JSON.stringify(request, null, 2)}`
      );

      // Validate required fields
      if (!request.email) {
        this.logger.error('Email is required but not provided in request');
        this.logger.error(`Request keys: ${Object.keys(request).join(', ')}`);
        return {
          success: false,
          message: 'Email is required',
        };
      }

      this.logger.log(`Creating employee with email: ${request.email}`);

      // Start transaction
      transaction = await this.userModel.sequelize.transaction();

      // 1. Check if email already exists
      const existingUser = await this.userModel.findOne({
        where: { email: request.email },
        transaction,
      });

      if (existingUser) {
        await transaction.rollback();
        return {
          success: false,
          message: 'Email already exists',
        };
      }

      // 2. Validate job type and status
      if (request.jobType && !JOB_TYPES.includes(request.jobType as any)) {
        await transaction.rollback();
        throw new BadRequestException(
          `Invalid job type. Must be one of: ${JOB_TYPES.join(', ')}`
        );
      }
      if (
        request.jobStatus &&
        !JOB_STATUSES.includes(request.jobStatus as any)
      ) {
        await transaction.rollback();
        throw new BadRequestException(
          `Invalid job status. Must be one of: ${JOB_STATUSES.join(', ')}`
        );
      }

      // 3. Find or create organization
      let organization = await this.organizationModel.findOne({
        where: { name: request.organizationName || 'ApplyGoal' },
        transaction,
      });

      if (!organization) {
        organization = await this.organizationModel.create(
          {
            name: request.organizationName || 'ApplyGoal',
            description: `Organization for ${
              request.organizationName || 'ApplyGoal'
            }`,
          },
          { transaction }
        );
      }

      // 4. Find department
      let department = null;
      if (request.departmentName) {
        department = await this.departmentModel.findOne({
          where: { name: request.departmentName },
          transaction,
        });
      }

      // 5. Find employee role (organization-specific or system role)
      const employeeRoleName = request.employeeRoleName || 'Employee';

      // First try to find organization-specific role
      let role = await this.roleModel.findOne({
        where: {
          name: employeeRoleName,
          organizationId: organization.id,
        },
        transaction,
      });

      // If not found, try to find system role
      if (!role) {
        role = await this.roleModel.findOne({
          where: {
            name: employeeRoleName,
            isSystemRole: true,
            organizationId: null,
          },
          transaction,
        });
      }

      // If still not found, create organization-specific role
      if (!role) {
        this.logger.log(
          `Creating organization-specific role '${employeeRoleName}' for organization '${organization.name}'`
        );
        role = await this.roleModel.create(
          {
            name: employeeRoleName,
            organizationId: organization.id,
            description: `${employeeRoleName} role for ${organization.name}`,
            isSystemRole: false,
          },
          { transaction }
        );
      }

      // 6. Create user
      const hashedPassword = request.password
        ? await bcrypt.hash(request.password, 10)
        : await bcrypt.hash('DefaultPassword123!', 10); // Default password

      const user = await this.userModel.create(
        {
          email: request.email,
          password: hashedPassword,
          name:
            request.name ||
            `${request.firstName || ''} ${request.lastName || ''}`.trim(),
          phone: request.phone,
          status: 'active',
          organizationId: organization.id,
        },
        { transaction }
      );

      // 7. Assign role to user
      await this.userRoleModel.create(
        {
          userId: user.id,
          roleId: role.id,
        },
        { transaction }
      );

      // 8. Assign department to user (if provided)
      if (department) {
        await this.userDepartmentModel.create(
          {
            userId: user.id,
            departmentId: department.id,
          },
          { transaction }
        );
      }

      // 9. Create employee record
      const employeeData: CreateEmployeeData = {
        userId: BigInt(user.id),
        lastName: request.lastName,
        firstName: request.firstName,
        joiningDate: request.joiningDate
          ? new Date(request.joiningDate)
          : new Date(), // Default to today
        jobType: request.jobType || 'Full-time',
        jobStatus: request.jobStatus || 'Active',
        dateOfBirth: request.dateOfBirth
          ? new Date(request.dateOfBirth)
          : undefined,
        bloodGroup: request.bloodGroup,
        gender: request.gender,
        agencyId: request.agencyId,
        orgId: request.orgId || organization.id.toString(),
      };

      const employee = await this.employeeModel.create(employeeData as any, {
        transaction,
      });

      // 11. Commit transaction before creating related records
      await transaction.commit();

      // 12. Create related records (outside transaction for now)
      if (request.addresses?.length) {
        await this.createEmployeeAddresses(
          BigInt(employee.userId),
          request.addresses
        );
      }
      if (request.emergencyContacts?.length) {
        await this.createEmployeeEmergencyContacts(
          BigInt(employee.userId),
          request.emergencyContacts
        );
      }
      if (request.identityDocs?.length) {
        await this.createEmployeeIdentityDocs(
          BigInt(employee.userId),
          request.identityDocs
        );
      }
      if (request.bankAccounts?.length) {
        await this.createEmployeeBankAccounts(
          BigInt(employee.userId),
          request.bankAccounts
        );
      }

      // Audit-log
      await this.createAuditLog({
        userId: Number(request.requestUserId),
        userRole: request.roleName,
        actions: 'CREATE_EMPLOYEE',
        serviceName: 'auth-service',
        resourceType: 'Employee',
        resourceId: Number(employee.userId),
        description: `Created employee for user: ${employee.userId}`,
        ipAddress: request.ipAddress,
        userAgent: request.userAgent,
        source: 'grpc',
      });

      this.logger.log(`Employee created successfully: ${employee.userId}`);

      // Fetch the complete employee with all relations
      const fullEmployee = await this.employeeModel.findByPk(employee.userId, {
        include: { all: true, nested: true },
      });

      if (!fullEmployee) {
        this.logger.error(
          `Failed to fetch created employee: ${employee.userId}`
        );
        throw new Error('Employee created but could not be retrieved');
      }

      // Metrics
      this.trackMetrics('create_employee', 'success', Date.now() - startTime);

      return {
        success: true,
        message: 'Employee created successfully',
        employee: await this.mapToEmployeeInfo(fullEmployee),
      };
    } catch (error) {
      // Rollback transaction if it exists and hasn't been committed
      if (transaction) {
        try {
          await transaction.rollback();
        } catch (rollbackError) {
          this.logger.error('Failed to rollback transaction', rollbackError);
        }
      }

      this.logger.error(
        `Error creating employee: ${error.message}`,
        error.stack
      );
      this.trackMetrics('create_employee', 'error', Date.now() - startTime);

      if (error instanceof BadRequestException) {
        throw error;
      }

      throw new Error('Failed to create employee');
    }
  }

  private async createEmployeeAddresses(
    userId: bigint,
    addresses: EmployeeAddressInfo[]
  ): Promise<void> {
    const addressData = addresses.map((addr) => ({
      userId: Number(userId),
      addressType: addr.addressType,
      addressLine: addr.addressLine,
      country: addr.country,
      state: addr.state,
      city: addr.city,
      postalCode: addr.postalCode,
    }));
    await this.employeeAddressModel.bulkCreate(addressData);
  }

  private async createEmployeeEmergencyContacts(
    userId: bigint,
    contacts: EmployeeEmergencyContactInfo[]
  ): Promise<void> {
    const contactData = contacts.map((contact) => ({
      userId: Number(userId),
      category: contact.category,
      name: contact.name,
      relationship: contact.relationship,
      address: contact.address,
      phoneNumber: contact.phoneNumber,
      email: contact.email,
    }));
    await this.employeeEmergencyContactModel.bulkCreate(contactData);
  }

  private async createEmployeeIdentityDocs(
    userId: bigint,
    docs: EmployeeIdentityDocInfo[]
  ): Promise<void> {
    const docData = docs.map((doc) => ({
      userId: Number(userId),
      docType: doc.docType,
      nationality: doc.nationality,
      issueDate: doc.issueDate ? new Date(doc.issueDate) : undefined,
      expiryDate: doc.expiryDate ? new Date(doc.expiryDate) : undefined,
    }));
    await this.employeeIdentityDocModel.bulkCreate(docData);
  }

  private async createEmployeeBankAccounts(
    userId: bigint,
    accounts: EmployeeBankAccountInfo[]
  ): Promise<void> {
    const accountData = accounts.map((account) => ({
      userId: Number(userId),
      accountHolder: account.accountHolder,
      accountNumber: account.accountNumber,
      bankName: account.bankName,
      branchName: account.branchName,
    }));
    await this.employeeBankAccountModel.bulkCreate(accountData);
  }

  async mapToEmployeeInfo(employee: EmployeePersonal): Promise<EmployeeInfo> {
    // If employee already has relations loaded, use it directly
    let fullEmployee = employee;

    // If relations are not loaded, fetch them
    if (
      !employee.addresses &&
      !employee.emergencyContacts &&
      !employee.identityDocs &&
      !employee.bankAccounts
    ) {
      const fetchedEmployee = await this.employeeModel.findByPk(
        employee.userId,
        {
          include: { all: true, nested: true },
        }
      );
      if (!fetchedEmployee) {
        throw new NotFoundException(
          `Employee not found with userId: ${employee.userId}`
        );
      }
      fullEmployee = fetchedEmployee;
    }
    return {
      userId: BigInt(fullEmployee.userId),
      lastName: fullEmployee.lastName,
      firstName: fullEmployee.firstName,
      joiningDate: fullEmployee.joiningDate?.toISOString(),
      jobType: fullEmployee.jobType,
      jobStatus: fullEmployee.jobStatus,
      dateOfBirth: fullEmployee.dateOfBirth?.toISOString(),
      bloodGroup: fullEmployee.bloodGroup,
      gender: fullEmployee.gender,
      agencyId: fullEmployee.agencyId
        ? BigInt(fullEmployee.agencyId)
        : undefined,
      orgId: fullEmployee.orgId,
      addresses: fullEmployee.addresses?.map((addr) => ({
        id: BigInt(addr.id),
        userId: BigInt(addr.userId),
        addressType: addr.addressType,
        addressLine: addr.addressLine,
        country: addr.country,
        state: addr.state,
        city: addr.city,
        postalCode: addr.postalCode,
      })),
      emergencyContacts: fullEmployee.emergencyContacts?.map((contact) => ({
        id: BigInt(contact.id),
        userId: BigInt(contact.userId),
        category: contact.category,
        name: contact.name,
        relationship: contact.relationship,
        address: contact.address,
        phoneNumber: contact.phoneNumber,
        email: contact.email,
      })),
      identityDocs: fullEmployee.identityDocs?.map((doc) => ({
        id: BigInt(doc.id),
        userId: BigInt(doc.userId),
        docType: doc.docType,
        nationality: doc.nationality,
        issueDate: doc.issueDate?.toISOString(),
        expiryDate: doc.expiryDate?.toISOString(),
      })),
      bankAccounts: fullEmployee.bankAccounts?.map((account) => ({
        id: BigInt(account.id),
        userId: BigInt(account.userId),
        accountHolder: account.accountHolder,
        accountNumber: account.accountNumber,
        bankName: account.bankName,
        branchName: account.branchName,
      })),
      createdAt: fullEmployee.createdAt,
      updatedAt: fullEmployee.updatedAt,
    };
  }

  // Legacy methods remain unchanged
  async create(data: CreateEmployeeDto): Promise<EmployeePersonal> {
    return this.employeeModel.create(data as any);
  }

  async findAll(): Promise<EmployeePersonal[]> {
    return this.employeeModel.findAll({
      include: { all: true, nested: true },
    });
  }

  async findOne(id: number): Promise<EmployeePersonal> {
    const emp = await this.employeeModel.findByPk(id, {
      include: { all: true, nested: true },
    });
    if (!emp) throw new NotFoundException(`Employee ${id} not found`);
    return emp;
  }

  async update(id: number, data: UpdateEmployeeDto): Promise<EmployeePersonal> {
    const emp = await this.findOne(id);
    await emp.update(data as any);
    return emp;
  }

  async remove(id: number): Promise<{ deleted: boolean }> {
    const emp = await this.findOne(id);
    await emp.destroy();
    return { deleted: true };
  }
}
