export interface GetRoleDetailsRequest {
  name: string;
  getArgs: {
    userId: number;
    roleName: string;
    ipAddress: string;
    userAgent: string;
  };
}

export interface RoleDetailsResponse {
  success: boolean;
  message: string;
  role: RoleDetailsInfo;
}

export interface RoleDetailsInfo {
  id: number;
  role: string;
  department: Array<{
    name: string;
    users: Array<{ id: number; name: string; email: string }>;
  }>;
  modules: Array<{
    module: string;
    features: Array<{
      feature: string;
      permissions: boolean;
      subFeatures: Array<{
        subFeature: string;
        permissions: boolean;
      }>;
    }>;
  }>;
}

export interface CreateRoleWithDetailsRequest {
  id?: number;
  name: string;
  // ↓ here
  departments: Array<{
    id: number;
    name: string;
    users: Array<{ id: number; name: string; email: string }>;
  }>;
  modules: Array<{
    id: number;
    features: Array<{
      id: number;
      feature: string;
      permissions: boolean;
      subFeatures: Array<{
        id: number;
        subFeature: string;
        permissions: boolean;
      }>;
    }>;
  }>;
  getArgs: {
    userId?: number;
    roleName?: string;
    ipAddress?: string;
    userAgent?: string;
  };
}

export interface CreateRoleResponse {
  success: boolean;
  message: string;
  role?: {
    id: number;
    name: string;
    description?: string;
    departments?: Array<{
      id: number;
      name: string;
      users: Array<{ id: number; name: string; email: string }>;
    }>;
    modules?: Array<{
      id: number;
      module: string;
      features: Array<{
        id: number;
        feature: string;
        permissions: boolean;
        subFeatures: Array<{
          id: number;
          subFeature: string;
          permissions: boolean;
        }>;
      }>;
    }>;
  };
}

export interface GetRolesWithDetailsResponse {
  success: boolean;
  message: string;
  roles: {
    id: number;
    role: string;
    department: {
      id: number;
      name: string;
      users: {
        id: number;
        name: string;
        email: string;
      }[];
    }[];
    modules: {
      id: number;
      module: string;
      features: {
        id: number;
        feature: string;
        permissions: boolean;
        subFeatures: {
          id: number;
          subFeature: string;
          permissions: boolean;
        }[];
      }[];
    }[];
  }[];
}

export interface UpdateRoleRequest {
  id: number;
  roleName: string;
  getArgs: {
    userId?: number;
    roleName?: string;
    ipAddress?: string;
    userAgent?: string;
  };
}

export interface UpdateRoleResponse {
  success: boolean;
  message: string;
}

export interface DeleteRoleRequest {
  id: number;
  getArgs: {
    userId?: number;
    roleName?: string;
    ipAddress?: string;
    userAgent?: string;
  };
}

export interface DeleteRoleResponse {
  success: boolean;
  message: string;
}
