import { Table, Column, DataType, ForeignKey } from 'sequelize-typescript';
import { Role } from './role.model';
import { BaseModel } from '@apply-goal-backend/database';
import { Feature } from './feature.model';

@Table({
  tableName: 'role_feature_permissions',
  timestamps: true,
})
export class RoleFeaturePermission extends BaseModel {
  @ForeignKey(() => Role)
  @Column({ type: DataType.BIGINT, primaryKey: true })
  roleId: bigint;

  @ForeignKey(() => Feature)
  @Column({ type: DataType.BIGINT, primaryKey: true })
  featureId: bigint; // was actionId

  @Column({ type: DataType.BOOLEAN, allowNull: false, defaultValue: false })
  isAllowed: boolean;
}
