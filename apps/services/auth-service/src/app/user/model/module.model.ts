import { Column, Table, DataType, HasMany } from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
import { Feature } from './feature.model';

@Table({
  tableName: 'modules',
  timestamps: true,
})
export class Module extends BaseModel {
  @Column({ type: DataType.STRING, allowNull: false })
  name: string;

  @HasMany(() => Feature, { foreignKey: 'moduleId', as: 'features' })
  features: Feature[];
}
