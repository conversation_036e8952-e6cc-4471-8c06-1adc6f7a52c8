// department-action-permission.model.ts
import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  PrimaryKey,
} from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
import { Department } from './department.model';
import { Feature } from './feature.model';

@Table({
  tableName: 'department_feature_permissions',
  timestamps: true,
})
export class DepartmentFeaturePermission extends BaseModel {
  @ForeignKey(() => Department)
  @PrimaryKey
  @Column({ type: DataType.BIGINT })
  departmentId: bigint;

  @ForeignKey(() => Feature)
  @PrimaryKey
  @Column({ type: DataType.BIGINT })
  featureId: bigint;

  @Column({ type: DataType.BOOLEAN, allowNull: false, defaultValue: false })
  isAllowed: boolean;
}
