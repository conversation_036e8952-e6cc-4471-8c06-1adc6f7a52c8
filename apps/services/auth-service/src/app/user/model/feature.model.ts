import { BaseModel } from '@apply-goal-backend/database';
import {
  Table,
  DataType,
  Column,
  ForeignKey,
  BelongsTo,
  HasMany,
} from 'sequelize-typescript';
import { Module } from './module.model';
import { SubFeature } from './sub-feature.model';

@Table({
  tableName: 'features', // renamed table
  timestamps: true,
})
export class Feature extends BaseModel {
  @ForeignKey(() => Module)
  @Column({ type: DataType.BIGINT, allowNull: false })
  moduleId: bigint; // was featureId

  @BelongsTo(() => Module, { as: 'module' })
  module: Module;

  @Column({ type: DataType.STRING, allowNull: false })
  name: string;

  @HasMany(() => SubFeature, { foreignKey: 'featureId', as: 'subFeatures' })
  subFeatures: SubFeature[];
}
