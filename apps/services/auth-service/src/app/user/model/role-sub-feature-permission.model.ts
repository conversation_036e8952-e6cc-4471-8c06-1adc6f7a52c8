import { Table, Column, DataType, ForeignKey } from 'sequelize-typescript';
import { Role } from './role.model';
import { BaseModel } from '@apply-goal-backend/database';
import { SubFeature } from './sub-feature.model';

@Table({ tableName: 'role_sub_feature_permissions', timestamps: true })
export class RoleSubFeaturePermission extends BaseModel {
  @ForeignKey(() => Role)
  @Column({ type: DataType.BIGINT, primaryKey: true })
  roleId: bigint;

  @ForeignKey(() => SubFeature)
  @Column({ type: DataType.BIGINT, primaryKey: true })
  subFeatureId: bigint; // was subActionId

  @Column({ type: DataType.BOOLEAN, allowNull: false, defaultValue: false })
  isAllowed: boolean;
}
