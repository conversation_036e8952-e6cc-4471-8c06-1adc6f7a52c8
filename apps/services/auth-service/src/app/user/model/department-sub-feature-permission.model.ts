// department-sub-action-permission.model.ts
import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  PrimaryKey,
} from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
import { Department } from './department.model';
import { SubFeature } from './sub-feature.model';

@Table({
  tableName: 'department_sub_feature_permissions',
  timestamps: true,
})
export class DepartmentSubFeaturePermission extends BaseModel {
  @ForeignKey(() => Department)
  @PrimaryKey
  @Column({ type: DataType.BIGINT })
  departmentId: bigint;

  @ForeignKey(() => SubFeature)
  @PrimaryKey
  @Column({ type: DataType.BIGINT })
  subFeatureId: bigint;

  @Column({ type: DataType.BOOLEAN, allowNull: false, defaultValue: false })
  isAllowed: boolean;
}
