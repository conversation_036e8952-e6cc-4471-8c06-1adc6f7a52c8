// user‐department.model.ts
import { User } from './user.model';
import { Department } from './department.model';
import {
  Column,
  DataType,
  ForeignKey,
  Model,
  PrimaryKey,
  Table,
} from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';

@Table({
  tableName: 'user_departments',
  timestamps: false,
})
export class UserDepartment extends BaseModel {
  @ForeignKey(() => User)
  @PrimaryKey
  @Column(DataType.BIGINT)
  userId: bigint;

  @ForeignKey(() => Department)
  @PrimaryKey
  @Column(DataType.BIGINT)
  departmentId: bigint;
}
