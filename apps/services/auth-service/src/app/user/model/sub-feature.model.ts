import { BaseModel } from '@apply-goal-backend/database';
import {
  Table,
  DataType,
  Column,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { Feature } from './feature.model';

@Table({
  tableName: 'sub_features', // renamed table
  timestamps: true,
})
export class SubFeature extends BaseModel {
  @ForeignKey(() => Feature)
  @Column({ type: DataType.BIGINT, allowNull: false })
  featureId: bigint; // was actionId

  @BelongsTo(() => Feature, { as: 'feature' })
  feature: Feature;

  @Column({ type: DataType.STRING, allowNull: false })
  name: string;
}
