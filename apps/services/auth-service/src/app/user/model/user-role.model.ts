import {
  Column,
  Model,
  Table,
  DataType,
  Foreign<PERSON>ey,
  Primary<PERSON>ey,
} from 'sequelize-typescript';
import { User } from './user.model';
import { Role } from './role.model';
import { BaseModel } from '@apply-goal-backend/database';

@Table({ tableName: 'user_roles', timestamps: false })
export class UserRole extends Model<UserRole> {
  @ForeignKey(() => User)
  @Column(DataType.BIGINT)
  userId: bigint;

  @ForeignKey(() => Role)
  @Column(DataType.BIGINT)
  roleId: bigint;
}
