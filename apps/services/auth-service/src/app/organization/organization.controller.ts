import { <PERSON>, Logger } from '@nestjs/common';
import { GrpcMethod } from '@nestjs/microservices';
import { OrganizationService } from './organization.service';
import {
  CreateOrganizationRequest,
  CreateOrganizationResponse,
  GetOrganizationRequest,
  GetOrganizationResponse,
  UpdateOrganizationRequest,
  UpdateOrganizationResponse,
  DeleteOrganizationRequest,
  DeleteOrganizationResponse,
  ListOrganizationsRequest,
  ListOrganizationsResponse,
} from './organization.interface';

@Controller()
export class OrganizationController {
  private readonly logger = new Logger(OrganizationController.name);

  constructor(private readonly organizationService: OrganizationService) {}

  @GrpcMethod('AuthService', 'CreateOrganization')
  async createOrganization(
    request: CreateOrganizationRequest
  ): Promise<CreateOrganizationResponse> {
    try {
      this.logger.log(`gRPC CreateOrganization request: ${request.name}`);
      return await this.organizationService.createOrganization(request);
    } catch (error) {
      this.logger.error(
        `CreateOrganization error: ${error.message}`,
        error.stack
      );
      return {
        success: false,
        message: error.message || 'Failed to create organization',
      };
    }
  }

  @GrpcMethod('AuthService', 'GetOrganization')
  async getOrganization(
    request: GetOrganizationRequest
  ): Promise<GetOrganizationResponse> {
    try {
      this.logger.log(`gRPC GetOrganization request: ${request.id}`);
      return await this.organizationService.getOrganization(request);
    } catch (error) {
      this.logger.error(`GetOrganization error: ${error.message}`, error.stack);
      return {
        success: false,
        message: error.message || 'Failed to get organization',
      };
    }
  }

  @GrpcMethod('AuthService', 'UpdateOrganization')
  async updateOrganization(
    request: UpdateOrganizationRequest
  ): Promise<UpdateOrganizationResponse> {
    try {
      this.logger.log(`gRPC UpdateOrganization request: ${request.id}`);
      return await this.organizationService.updateOrganization(request);
    } catch (error) {
      this.logger.error(
        `UpdateOrganization error: ${error.message}`,
        error.stack
      );
      return {
        success: false,
        message: error.message || 'Failed to update organization',
      };
    }
  }

  @GrpcMethod('AuthService', 'DeleteOrganization')
  async deleteOrganization(
    request: DeleteOrganizationRequest
  ): Promise<DeleteOrganizationResponse> {
    try {
      this.logger.log(`gRPC DeleteOrganization request: ${request.id}`);
      return await this.organizationService.deleteOrganization(request);
    } catch (error) {
      this.logger.error(
        `DeleteOrganization error: ${error.message}`,
        error.stack
      );
      return {
        success: false,
        message: error.message || 'Failed to delete organization',
      };
    }
  }

  @GrpcMethod('AuthService', 'ListOrganizations')
  async listOrganizations(
    request: ListOrganizationsRequest
  ): Promise<ListOrganizationsResponse> {
    try {
      this.logger.log('gRPC ListOrganizations request');
      return await this.organizationService.listOrganizations(request);
    } catch (error) {
      this.logger.error(
        `ListOrganizations error: ${error.message}`,
        error.stack
      );
      return {
        success: false,
        message: error.message || 'Failed to list organizations',
        organizations: [],
        total: 0,
        page: request.page || 1,
        limit: request.limit || 10,
      };
    }
  }
}
