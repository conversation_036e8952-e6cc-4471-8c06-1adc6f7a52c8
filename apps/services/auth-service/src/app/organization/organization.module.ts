import { <PERSON>du<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { Organization } from './organization.model';
import { OrganizationController } from './organization.controller';
import { OrganizationService } from './organization.service';
import { AuditModule } from '../audit/audit.module';
import { DatabaseModule } from '../migration/database.module';
import { AppService } from '../app.service';
import { MetricsModule } from '../metrics/metrics.module';

@Module({
  imports: [
    DatabaseModule,
    SequelizeModule.forFeature([Organization]),
    AuditModule,
    MetricsModule,
  ],
  controllers: [OrganizationController],
  providers: [OrganizationService, AppService],
  exports: [SequelizeModule, OrganizationService],
})
export class OrganizationModule {}
