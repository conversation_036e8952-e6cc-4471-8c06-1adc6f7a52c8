import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AppController } from './app.controller';
import { DatabaseModule } from './migration/database.module';
import { MigrationModule } from './migration/migration.module';
import { UserModule } from './user/user.module';
import { AuditModule } from './audit/audit.module';
import { AuthModule } from './auth/auth.module';
import { MetricsMiddleware } from './metrics/metrics.middleware';
import { CoreModule } from './core/core.module';
import { MetricsModule } from './metrics/metrics.module';
import { SeedModule } from './seed/seed.module';
import { OrganizationModule } from './organization/organization.module';
import { OtpModule } from '@apply-goal-backend/auth';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    CoreModule,
    DatabaseModule,
    MigrationModule,
    UserModule,
    AuditModule,
    AuthModule,
    MetricsModule,
    SeedModule,
    OrganizationModule,
    OtpModule.forRoot({
      redis: {
        host: 'redis',
        port: 6379,
      },
    }),
  ],
  controllers: [AppController],
  providers: [],
  exports: [],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(MetricsMiddleware).exclude('/api/metrics').forRoutes('*');
  }
}
