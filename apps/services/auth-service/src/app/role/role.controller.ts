import { Controller, Logger } from '@nestjs/common';
import { GrpcMethod } from '@nestjs/microservices';
import { RoleService, CreateRoleRequest, UpdateRoleRequest, ListRolesRequest, RoleResponse, ListRolesResponse } from './role.service';

export interface GetRoleRequest {
  id: number;
}

export interface DeleteRoleRequest {
  id: number;
}

export interface DeleteRoleResponse {
  success: boolean;
  message: string;
}

export interface GetOrganizationRolesRequest {
  organizationId: number;
}

export interface GetOrganizationRolesResponse {
  success: boolean;
  message: string;
  roles: any[];
}

@Controller()
export class RoleController {
  private readonly logger = new Logger(RoleController.name);

  constructor(private readonly roleService: RoleService) {}

  @GrpcMethod('AuthService', 'CreateRole')
  async createRole(request: CreateRoleRequest): Promise<RoleResponse> {
    try {
      this.logger.log(`gRPC CreateRole request: ${request.name}`);
      return await this.roleService.createRole(request);
    } catch (error) {
      this.logger.error(`CreateRole error: ${error.message}`, error.stack);
      return {
        success: false,
        message: error.message || 'Failed to create role',
      };
    }
  }

  @GrpcMethod('AuthService', 'UpdateRole')
  async updateRole(request: UpdateRoleRequest): Promise<RoleResponse> {
    try {
      this.logger.log(`gRPC UpdateRole request: ${request.id}`);
      return await this.roleService.updateRole(request);
    } catch (error) {
      this.logger.error(`UpdateRole error: ${error.message}`, error.stack);
      return {
        success: false,
        message: error.message || 'Failed to update role',
      };
    }
  }

  @GrpcMethod('AuthService', 'ListRoles')
  async listRoles(request: ListRolesRequest): Promise<ListRolesResponse> {
    try {
      this.logger.log(`gRPC ListRoles request for organization: ${request.organizationId}`);
      return await this.roleService.listRoles(request);
    } catch (error) {
      this.logger.error(`ListRoles error: ${error.message}`, error.stack);
      return {
        success: false,
        message: error.message || 'Failed to list roles',
        roles: [],
        total: 0,
        page: request.page || 1,
        limit: request.limit || 10,
      };
    }
  }

  @GrpcMethod('AuthService', 'GetRole')
  async getRole(request: GetRoleRequest): Promise<RoleResponse> {
    try {
      this.logger.log(`gRPC GetRole request: ${request.id}`);
      const role = await this.roleService.getRoleById(request.id);
      return {
        success: true,
        message: 'Role retrieved successfully',
        role,
      };
    } catch (error) {
      this.logger.error(`GetRole error: ${error.message}`, error.stack);
      return {
        success: false,
        message: error.message || 'Failed to get role',
      };
    }
  }

  @GrpcMethod('AuthService', 'DeleteRole')
  async deleteRole(request: DeleteRoleRequest): Promise<DeleteRoleResponse> {
    try {
      this.logger.log(`gRPC DeleteRole request: ${request.id}`);
      return await this.roleService.deleteRole(request.id);
    } catch (error) {
      this.logger.error(`DeleteRole error: ${error.message}`, error.stack);
      return {
        success: false,
        message: error.message || 'Failed to delete role',
      };
    }
  }

  @GrpcMethod('AuthService', 'GetOrganizationRoles')
  async getOrganizationRoles(request: GetOrganizationRolesRequest): Promise<GetOrganizationRolesResponse> {
    try {
      this.logger.log(`gRPC GetOrganizationRoles request: ${request.organizationId}`);
      const roles = await this.roleService.getRolesForOrganization(request.organizationId);
      return {
        success: true,
        message: 'Organization roles retrieved successfully',
        roles: roles.map(role => ({
          id: role.id,
          name: role.name,
          description: role.description,
          organizationId: role.organizationId,
          isSystemRole: role.isSystemRole,
          organization: role.organization ? {
            id: role.organization.id,
            name: role.organization.name,
            type: role.organization.type,
          } : null,
        })),
      };
    } catch (error) {
      this.logger.error(`GetOrganizationRoles error: ${error.message}`, error.stack);
      return {
        success: false,
        message: error.message || 'Failed to get organization roles',
        roles: [],
      };
    }
  }
}
