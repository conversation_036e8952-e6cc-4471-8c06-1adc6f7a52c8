import { Catch, ArgumentsHost, Logger, ExceptionFilter } from '@nestjs/common';
import { Observable, throwError } from 'rxjs';
import { status } from '@grpc/grpc-js';
import { Request } from 'express';

@Catch()
export class GrpcExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(GrpcExceptionFilter.name);
  private readonly ignoredPaths = ['/favicon.ico'];

  catch(exception: any, host: ArgumentsHost): Observable<any> {
    // Check if this is an HTTP request we should ignore
    if (host.getType() === 'http') {
      const ctx = host.switchToHttp();
      const request = ctx.getRequest<Request>();
      
      // Ignore certain paths like favicon.ico
      if (this.ignoredPaths.includes(request.path)) {
        return throwError(() => exception);
      }
    }
    
    this.logger.error(`GRPC Exception: ${exception?.message || 'Unknown error'}`, exception?.stack);
    
    // Format the error for gRPC
    const grpcError = {
      code: status.INTERNAL,
      message: exception?.message || 'Internal server error',
      details: exception?.stack || '',
    };
    
    // Return a properly formatted gRPC error
    return throwError(() => grpcError);
  }
}
