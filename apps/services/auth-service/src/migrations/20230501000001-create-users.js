'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    console.log('Running migration: create-users');
    await queryInterface.createTable('users', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      username: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true,
      },
      firstName: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      lastName: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      email: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true,
      },
      password: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      phone: {
        type: Sequelize.STRING,
      },
      socialLink: {
        type: Sequelize.STRING,
      },
      role_id: {
        type: Sequelize.INTEGER,
        references: {
          model: 'roles',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      },
      gender: {
        type: Sequelize.STRING,
      },
      present_address: {
        type: Sequelize.STRING,
      },
      present_country: {
        type: Sequelize.STRING,
      },
      present_state: {
        type: Sequelize.STRING,
      },
      present_city: {
        type: Sequelize.STRING,
      },
      present_address_zipcode: {
        type: Sequelize.STRING,
      },
      permanat_address: {
        type: Sequelize.STRING,
      },
      permanat_country: {
        type: Sequelize.STRING,
      },
      permanat_state: {
        type: Sequelize.STRING,
      },
      permanat_city: {
        type: Sequelize.STRING,
      },
      permanat_address_zipcode: {
        type: Sequelize.STRING,
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });
    console.log('Migration completed: create-users');
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('users');
  },
};
