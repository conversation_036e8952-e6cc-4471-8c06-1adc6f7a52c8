'use strict';
const bcrypt = require('bcrypt');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    console.log('Running migration: seed-initial-data');

    // Create default roles
    await queryInterface.bulkInsert('roles', [
      {
        name: 'admin',
        status: 'active',
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        name: 'user',
        status: 'active',
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        name: 'manager',
        status: 'active',
        created_at: new Date(),
        updated_at: new Date(),
      },
    ]);

    // Create default permissions
    await queryInterface.bulkInsert('permissions', [
      {
        name: 'create_user',
        status: 'active',
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        name: 'read_user',
        status: 'active',
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        name: 'update_user',
        status: 'active',
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        name: 'delete_user',
        status: 'active',
        created_at: new Date(),
        updated_at: new Date(),
      },
    ]);

    // Get role IDs
    const roles = await queryInterface.sequelize.query(
      'SELECT id, name FROM roles',
      { type: queryInterface.sequelize.QueryTypes.SELECT }
    );

    const adminRoleId = roles.find((role) => role.name === 'admin').id;
    const userRoleId = roles.find((role) => role.name === 'user').id;

    // Get permission IDs
    const permissions = await queryInterface.sequelize.query(
      'SELECT id, name FROM permissions',
      { type: queryInterface.sequelize.QueryTypes.SELECT }
    );

    // Assign all permissions to admin role
    const rolePermissions = permissions.map((permission) => ({
      role_id: adminRoleId,
      permission_id: permission.id,
      created_at: new Date(),
      updated_at: new Date(),
    }));

    await queryInterface.bulkInsert('role_permissions', rolePermissions);

    // Create admin user
    const hashedPassword = await bcrypt.hash('admin123', 10);

    await queryInterface.bulkInsert('users', [
      {
        username: 'admin',
        firstName: 'Admin',
        lastName: 'User',
        email: '<EMAIL>',
        password: hashedPassword,
        role_id: adminRoleId,
        created_at: new Date(),
        updated_at: new Date(),
      },
    ]);

    // Get admin user ID
    const adminUser = await queryInterface.sequelize.query(
      "SELECT id FROM users WHERE username = 'admin'",
      { type: queryInterface.sequelize.QueryTypes.SELECT }
    );

    const adminUserId = adminUser[0].id;

    // Assign admin role to admin user
    await queryInterface.bulkInsert('userRoles', [
      {
        userId: adminUserId,
        role_id: adminRoleId,
        status: 'active',
        created_at: new Date(),
        updated_at: new Date(),
      },
    ]);

    console.log('Migration completed: seed-initial-data');
  },

  down: async (queryInterface, Sequelize) => {
    // Remove data in reverse order
    await queryInterface.bulkDelete('userRoles', null, {});
    await queryInterface.bulkDelete('role_permissions', null, {});
    await queryInterface.bulkDelete('users', null, {});
    await queryInterface.bulkDelete('permissions', null, {});
    await queryInterface.bulkDelete('roles', null, {});
  },
};
