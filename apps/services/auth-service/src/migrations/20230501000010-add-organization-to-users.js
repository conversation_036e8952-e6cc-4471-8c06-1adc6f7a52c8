'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // Add organization_id column to users table
    await queryInterface.addColumn('users', 'organization_id', {
      type: Sequelize.BIGINT,
      allowNull: true,
      references: {
        model: 'organizations',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL'
    });

    // Add index for organization_id
    await queryInterface.addIndex('users', ['organization_id']);
  },

  async down(queryInterface, Sequelize) {
    // Remove the organization_id column
    await queryInterface.removeColumn('users', 'organization_id');
  }
};
