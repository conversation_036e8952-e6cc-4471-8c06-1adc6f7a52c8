'use strict';

const seedData = {
  roles: [
    {
      id: 1,
      name: "Super Admin",
      status: "active",
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      id: 2,
      name: "University HR",
      status: "active",
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      id: 3,
      name: "University Account Manager",
      status: "active",
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      id: 4,
      name: "University Admin",
      status: "active",
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      id: 5,
      name: "Application Officer",
      status: "active",
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      id: 6,
      name: "Application Manager",
      status: "active",
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      id: 7,
      name: "Counselor",
      status: "active",
      created_at: new Date(),
      updated_at: new Date()
    }
  ],
  permissions: [
    {
      id: 1,
      name: "create-user",
      status: "active",
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      id: 2,
      name: "edit-user",
      status: "active",
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      id: 3,
      name: "delete-user",
      status: "active",
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      id: 4,
      name: "view-user",
      status: "active",
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      id: 5,
      name: "manage-roles",
      status: "active",
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      id: 6,
      name: "manage-permissions",
      status: "active",
      created_at: new Date(),
      updated_at: new Date()
    }
  ],
  role_permissions: [
    {
      role_id: 1,
      permission_id: 1,
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      role_id: 1,
      permission_id: 2,
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      role_id: 1,
      permission_id: 3,
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      role_id: 1,
      permission_id: 4,
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      role_id: 1,
      permission_id: 5,
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      role_id: 1,
      permission_id: 6,
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      role_id: 2,
      permission_id: 1,
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      role_id: 2,
      permission_id: 4,
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      role_id: 3,
      permission_id: 1,
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      role_id: 3,
      permission_id: 2,
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      role_id: 3,
      permission_id: 4,
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      role_id: 4,
      permission_id: 4,
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      role_id: 5,
      permission_id: 1,
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      role_id: 5,
      permission_id: 2,
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      role_id: 5,
      permission_id: 4,
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      role_id: 6,
      permission_id: 4,
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      role_id: 7,
      permission_id: 4,
      created_at: new Date(),
      updated_at: new Date()
    }
  ]
};

module.exports = {
  up: async (queryInterface, Sequelize) => {
    console.log('Running migration: seed-roles-permissions');
    
    // Create roles if they don't exist
    for (const role of seedData.roles) {
      const existingRole = await queryInterface.sequelize.query(
        `SELECT id FROM roles WHERE id = ${role.id} OR name = '${role.name}'`,
        { type: queryInterface.sequelize.QueryTypes.SELECT }
      );
      
      if (existingRole.length === 0) {
        await queryInterface.bulkInsert('roles', [role]);
      }
    }
    
    // Create permissions if they don't exist
    for (const permission of seedData.permissions) {
      const existingPermission = await queryInterface.sequelize.query(
        `SELECT id FROM permissions WHERE id = ${permission.id} OR name = '${permission.name}'`,
        { type: queryInterface.sequelize.QueryTypes.SELECT }
      );
      
      if (existingPermission.length === 0) {
        await queryInterface.bulkInsert('permissions', [permission]);
      }
    }
    
    // Create role-permission associations if they don't exist
    for (const rolePermission of seedData.role_permissions) {
      const existingRolePermission = await queryInterface.sequelize.query(
        `SELECT id FROM role_permissions WHERE role_id = ${rolePermission.role_id} AND permission_id = ${rolePermission.permission_id}`,
        { type: queryInterface.sequelize.QueryTypes.SELECT }
      );
      
      if (existingRolePermission.length === 0) {
        await queryInterface.bulkInsert('role_permissions', [rolePermission]);
      }
    }
    
    console.log('Migration completed: seed-roles-permissions');
  },

  down: async (queryInterface, Sequelize) => {
    // Remove the seeded data
    for (const rolePermission of seedData.role_permissions) {
      await queryInterface.bulkDelete('role_permissions', {
        role_id: rolePermission.role_id,
        permission_id: rolePermission.permission_id
      });
    }
    
    for (const permission of seedData.permissions) {
      await queryInterface.bulkDelete('permissions', { id: permission.id });
    }
    
    for (const role of seedData.roles) {
      await queryInterface.bulkDelete('roles', { id: role.id });
    }
  }
};