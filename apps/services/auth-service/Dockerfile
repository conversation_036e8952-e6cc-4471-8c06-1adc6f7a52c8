# Build stage
FROM node:18-alpine as builder

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY nx.json ./
COPY tsconfig*.json ./
COPY eslint.config.mjs ./

# Copy source code
COPY apps/services/auth-service ./apps/services/auth-service
COPY libs ./libs

# Install dependencies
RUN npm ci

# Build the application
RUN npx nx build auth-service --prod

# Production stage
FROM node:18-alpine

WORKDIR /app

# Copy built assets from builder
COPY --from=builder /app/dist/apps/services/auth-service ./

# Install production dependencies
RUN npm ci --only=production

# Set environment variables
ENV NODE_ENV=production
ENV PORT=5003

# Expose the service port
EXPOSE 5003

# Start the service
CMD ["node", "main.js"]
