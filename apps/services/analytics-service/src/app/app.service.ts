import { Injectable } from '@nestjs/common';
import { MetricsService } from '@apply-goal-backend/monitoring';

@Injectable()
export class AppService {
  private requestCounter: any;
  private requestDuration: any;
  private analyticsProcessingCounter: any;
  private analyticsProcessingDuration: any;
  private dataPointsGauge: any;
  private batchSizeHistogram: any;

  constructor(private metricsService: MetricsService) {
    // HTTP metrics
    this.requestCounter = this.metricsService.createCounter(
      'http_requests_total',
      'Total number of HTTP requests processed',
      ['method', 'endpoint', 'status']
    );

    this.requestDuration = this.metricsService.createHistogram(
      'http_request_duration_seconds',
      'HTTP request duration in seconds',
      ['method', 'endpoint']
    );

    // Analytics-specific metrics
    this.analyticsProcessingCounter = this.metricsService.createCounter(
      'analytics_processing_total',
      'Total number of analytics processing operations',
      ['type', 'status']
    );

    this.analyticsProcessingDuration = this.metricsService.createHistogram(
      'analytics_processing_duration_seconds',
      'Analytics processing duration in seconds',
      ['type'],
      [0.1, 0.5, 1, 2, 5, 10, 30]  // buckets for different durations
    );

    this.dataPointsGauge = this.metricsService.createGauge(
      'analytics_data_points_current',
      'Current number of data points being processed',
      ['dataset_type']
    );

    this.batchSizeHistogram = this.metricsService.createHistogram(
      'analytics_batch_size',
      'Size of analytics processing batches',
      ['type'],
      [10, 50, 100, 500, 1000, 5000, 10000]  // buckets for different batch sizes
    );
  }

  // Track HTTP request
  trackHttpRequest(method: string, endpoint: string, status: string, duration: number) {
    this.requestCounter.inc({ method, endpoint, status });
    this.requestDuration.observe({ method, endpoint }, duration);
  }

  // Track analytics processing
  trackAnalyticsProcessing(type: string, status: string) {
    this.analyticsProcessingCounter.inc({ type, status });
  }

  // Track analytics processing duration
  trackAnalyticsProcessingDuration(type: string, durationSeconds: number) {
    this.analyticsProcessingDuration.observe({ type }, durationSeconds);
  }

  // Track current data points
  trackDataPoints(datasetType: string, count: number) {
    this.dataPointsGauge.set({ dataset_type: datasetType }, count);
  }

  // Track batch size
  trackBatchSize(type: string, size: number) {
    this.batchSizeHistogram.observe({ type }, size);
  }

  getData(): { message: string } {
    this.trackHttpRequest('GET', '/api', '200', 0.1);
    return { message: 'Hello Analytics API' };
  }
}
