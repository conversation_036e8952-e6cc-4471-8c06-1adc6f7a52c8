# Build stage
FROM node:18-alpine as builder

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY nx.json ./
COPY tsconfig*.json ./
COPY eslint.config.mjs ./

# Copy source code
COPY apps/services/analytics-service ./apps/services/analytics-service
COPY libs ./libs

# Install dependencies
RUN npm ci

# Build the application
RUN npx nx build analytics-service --prod

# Production stage
FROM node:18-alpine

WORKDIR /app

# Copy built assets from builder
COPY --from=builder /app/dist/apps/services/analytics-service ./

# Install production dependencies
RUN npm ci --only=production

# Set environment variables
ENV NODE_ENV=production
ENV PORT=5002

# Expose the service port
EXPOSE 5002

# Start the service
CMD ["node", "main.js"]
