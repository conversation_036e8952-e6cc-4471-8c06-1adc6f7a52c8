{"name": "analytics-service", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/services/analytics-service/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "nx:run-commands", "options": {"command": "webpack-cli build", "args": ["node-env=production"]}, "configurations": {"development": {"args": ["node-env=development"]}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "analytics-service:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "analytics-service:build:development"}, "production": {"buildTarget": "analytics-service:build:production"}}}, "test": {"options": {"passWithNoTests": true}}}}