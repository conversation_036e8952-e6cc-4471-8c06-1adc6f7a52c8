/**
 * This is not a production server yet!
 * This is only a minimal backend to get started.
 */

import { Logger, ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app/app.module';
import { MetricsService, TracingService } from '@apply-goal-backend/monitoring';
import { Transport, MicroserviceOptions } from '@nestjs/microservices';
import { join } from 'path';
import * as express from 'express';
import { healthRouter } from './routes/health.routes';
import { GrpcExceptionFilter } from './app/filters/grpc-exception.filter';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  
  // Add express middleware for health routes
  app.use(express.json());
  app.use('/health', healthRouter);

  // Setup gRPC Microservice
  const microservice = app.connectMicroservice<MicroserviceOptions>({
    transport: Transport.GRPC,
    options: {
      package: 'university',
      protoPath: join(process.cwd(), 'libs/shared/dto/src/lib/university/university.proto'),
      url: '0.0.0.0:50059',
      loader: {
        longs: String,
        enums: String,
        defaults: true,
        oneofs: true,
      },
    },
  });

  // Apply the global exception filter for gRPC
  microservice.useGlobalFilters(new GrpcExceptionFilter());
  
  // Get monitoring services
  const metricsService = app.get(MetricsService);
  const tracingService = app.get(TracingService);

  // Start tracing
  await tracingService.start();

  // Set global prefix and validation pipe
  const globalPrefix = 'api';
  app.setGlobalPrefix(globalPrefix);
  app.useGlobalPipes(new ValidationPipe());
  
  // Enable CORS
  app.enableCors();

  const port = process.env.PORT || 5020;

  await app.startAllMicroservices();
  await app.listen(port);

  Logger.log(`🚀 HTTP Application is running on: http://localhost:${port}/${globalPrefix}`);
  Logger.log(`🚀 gRPC server is running on: 0.0.0.0:50051`);
  Logger.log(`📊 Metrics server is running on: http://localhost:${port}/metrics`);
}

bootstrap();
