import { Table, Column, DataType, ForeignKey, BelongsTo } from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
import { Course } from './course.model';

@Table({ tableName: 'course_intakes' })
export class CourseIntake extends BaseModel {
  @ForeignKey(() => Course)
  @Column(DataType.BIGINT)
  courseId!: number;

  @Column(DataType.STRING)
  month!: string;

  @BelongsTo(() => Course)
  course!: Course;
}