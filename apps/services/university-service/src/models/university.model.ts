import { Table, Column, DataType, HasMany, ForeignKey } from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';

@Table({ tableName: 'universities' })
export class University extends BaseModel {
  @Column(DataType.BIGINT)
  userId!: number;

  @Column(DataType.TEXT)
  about!: string;

  @Column(DataType.STRING)
  type!: string;

  @Column(DataType.STRING)
  website!: string;

  @Column(DataType.STRING)
  logo!: string;

  @Column(DataType.DATE)
  foundedOn!: Date;

  @Column(DataType.STRING)
  dliNumber!: string;

  @Column(DataType.STRING)
  address!: string;

  @Column(DataType.STRING)
  country!: string;

  @Column(DataType.STRING)
  state!: string;

  @Column(DataType.STRING)
  city!: string;

  @Column(DataType.STRING)
  zip!: string;

  @Column(DataType.STRING)
  contactNumber!: string;

  @Column(DataType.STRING)
  email!: string;
}