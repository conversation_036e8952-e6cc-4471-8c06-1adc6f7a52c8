import { Table, Column, DataType, ForeignKey, BelongsTo } from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
import { Course } from './course.model';
import { Campus } from './campus.model';

@Table({ tableName: 'campus_courses' })
export class CampusCourse extends BaseModel {
  @ForeignKey(() => Course)
  @Column(DataType.BIGINT)
  courseId!: number;

  @ForeignKey(() => Campus)
  @Column(DataType.BIGINT)
  campusId!: number;

  @Column(DataType.STRING)
  country!: string;

  @BelongsTo(() => Course)
  course!: Course;

  @BelongsTo(() => Campus)
  campus!: Campus;
}