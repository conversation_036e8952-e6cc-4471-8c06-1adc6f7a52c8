import { Table, Column, DataType, ForeignKey, BelongsTo, HasMany } from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
import { University } from './university.model';
import { CampusCourse } from './campus-course.model';

@Table({ tableName: 'campuses' })
export class Campus extends BaseModel {
  @ForeignKey(() => University)
  @Column(DataType.BIGINT)
  universityId!: number;

  @Column(DataType.STRING)
  name!: string;

  @Column(DataType.STRING)
  address!: string;

  @Column(DataType.STRING)
  country!: string;

  @Column(DataType.STRING)
  state!: string;

  @Column(DataType.STRING)
  city!: string;

  @Column(DataType.STRING)
  zipCode!: string;

  @BelongsTo(() => University)
  university!: University;

  @HasMany(() => CampusCourse)
  campusCourses!: CampusCourse[];
}
