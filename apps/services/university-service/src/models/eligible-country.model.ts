import { Table, Column, DataType, ForeignKey } from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
import { University } from './university.model';
import { Country } from './country.model';

@Table({ tableName: 'eligible_countries' })
export class EligibleCountry extends BaseModel {
  @ForeignKey(() => University)
  @Column(DataType.BIGINT)
  universityId!: number;

  @ForeignKey(() => Country)
  @Column(DataType.BIGINT)
  countryId!: number;
}