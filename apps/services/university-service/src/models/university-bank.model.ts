import { Table, Column, DataType, Foreign<PERSON>ey, BelongsTo } from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
import { University } from './university.model';

@Table({ tableName: 'university_banks' })
export class UniversityBank extends BaseModel {
  @ForeignKey(() => University)
  @Column(DataType.BIGINT)
  universityId!: number;

  @Column(DataType.STRING)
  accountName!: string;

  @Column(DataType.STRING)
  accountNumber!: string;

  @Column(DataType.STRING)
  holderName!: string;

  @Column(DataType.STRING)
  ibanSwiftCode!: string;

  @Column(DataType.STRING)
  taxId!: string;

  @Column(DataType.STRING)
  vatNumber!: string;

  @Column(DataType.BIGINT)
  currencyId!: number;

  @Column(DataType.STRING)
  paymentFrequency!: string;

  @Column(DataType.STRING)
  paymentTerms!: string;

  @Column(DataType.STRING)
  paymentMethod!: string;

  @BelongsTo(() => University)
  university!: University;
}