import { Table, Column, DataType, ForeignKey, BelongsTo, HasMany } from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
import { University } from './university.model';

@Table({ tableName: 'courses' })
export class Course extends BaseModel {
  @ForeignKey(() => University)
  @Column(DataType.BIGINT)
  universityId!: number;

  @Column(DataType.STRING)
  title!: string;

  @Column(DataType.STRING)
  duration!: string;

  @Column(DataType.STRING)
  fieldOfStudy!: string;

  @Column(DataType.DECIMAL(10, 2))
  applicationFee!: number;

  @Column(DataType.DECIMAL(10, 2))
  tuitionFee!: number;

  @Column(DataType.STRING)
  lastAcademic!: string;

  @Column(DataType.FLOAT)
  minGpa!: number;

  @Column(DataType.INTEGER)
  courseRank!: number;

  @Column(DataType.FLOAT)
  acceptedRate!: number;

  @Column(DataType.TEXT)
  requirements!: string;

  @BelongsTo(() => University)
  university!: University;
}