import { Table, Column, DataType, ForeignKey, BelongsTo } from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
import { Course } from './course.model';
import { University } from './university.model';

@Table({ tableName: 'university_alumni' })
export class UniversityAlumni extends BaseModel {
  @ForeignKey(() => Course)
  @Column(DataType.BIGINT)
  courseId!: number;

  @ForeignKey(() => University)
  @Column(DataType.BIGINT)
  universityId!: number;

  @Column(DataType.STRING)
  name!: string;

  @Column(DataType.STRING)
  organization!: string;

  @Column(DataType.STRING)
  designation!: string;

  @Column(DataType.STRING)
  profile!: string;

  @BelongsTo(() => Course)
  course!: Course;

  @BelongsTo(() => University)
  university!: University;
}