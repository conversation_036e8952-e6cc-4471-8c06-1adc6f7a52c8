import { Table, Column, DataType, Foreign<PERSON>ey, BelongsTo } from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
import { Course } from './course.model';
import { University } from './university.model';

@Table({ tableName: 'university_course_commissions' })
export class UniversityCourseCommission extends BaseModel {
  @ForeignKey(() => University)
  @Column(DataType.BIGINT)
  universityId!: number;

  @ForeignKey(() => Course)
  @Column(DataType.BIGINT)
  courseId!: number;

  @Column(DataType.STRING)
  name!: string;

  @Column(DataType.DECIMAL(10, 2))
  commission!: number;

  @Column(DataType.STRING)
  payoutCircle!: string;

  @Column(DataType.STRING)
  period!: string;

  @Column(DataType.DATE)
  startDate!: Date;

  @Column(DataType.DATE)
  endDate!: Date;

  @Column(DataType.BIGINT)
  currencyId!: number;

  @Column(DataType.STRING)
  paymentFrequency!: string;

  @Column(DataType.STRING)
  paymentTerms!: string;

  @Column(DataType.STRING)
  paymentMethod!: string;

  @Column(DataType.TEXT)
  note!: string;

  @BelongsTo(() => Course)
  course!: Course;

  @BelongsTo(() => University)
  university!: University;
}