import { Table, Column, DataType, ForeignKey, BelongsTo } from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
import { University } from './university.model';

@Table({ tableName: 'tuition_financial_aids' })
export class TuitionFinancialAid extends BaseModel {
  @ForeignKey(() => University)
  @Column(DataType.BIGINT)
  universityId!: number;

  @Column(DataType.FLOAT)
  tuitionFeeDiscount!: number;

  @Column(DataType.BOOLEAN)
  financialAidAcceptance!: boolean;

  @Column(DataType.BOOLEAN)
  scholarshipOpportunity!: boolean;

  @Column(DataType.BOOLEAN)
  accommodationStatus!: boolean;

  @BelongsTo(() => University)
  university!: University;
}