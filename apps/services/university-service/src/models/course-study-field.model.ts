import { Table, Column, DataType, ForeignKey, BelongsTo } from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
import { Course } from './course.model';
import { StudyField } from './study-field.model';

@Table({ tableName: 'course_study_fields' })
export class CourseStudyField extends BaseModel {
  @ForeignKey(() => Course)
  @Column(DataType.BIGINT)
  courseId!: number;

  @ForeignKey(() => StudyField)
  @Column(DataType.BIGINT)
  fieldId!: number;

  @BelongsTo(() => Course)
  course!: Course;

  @BelongsTo(() => StudyField)
  studyField!: StudyField;
}