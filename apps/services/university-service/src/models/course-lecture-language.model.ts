import { Table, Column, DataType, ForeignKey, BelongsTo } from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
import { Course } from './course.model';
import { Language } from './language.model';

@Table({ tableName: 'course_lecture_languages' })
export class CourseLectureLanguage extends BaseModel {
  @ForeignKey(() => Course)
  @Column(DataType.BIGINT)
  courseId!: number;

  @ForeignKey(() => Language)
  @Column(DataType.BIGINT)
  languageId!: number;

  @BelongsTo(() => Course)
  course!: Course;

  @BelongsTo(() => Language)
  language!: Language;
}