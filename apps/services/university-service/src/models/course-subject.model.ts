import { Table, Column, DataType, ForeignKey, BelongsTo } from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
import { Course } from './course.model';

@Table({ tableName: 'course_subjects' })
export class CourseSubject extends BaseModel {
  @ForeignKey(() => Course)
  @Column(DataType.BIGINT)
  courseId!: number;

  @Column(DataType.STRING)
  subject!: string;

  @Column(DataType.INTEGER)
  credit!: number;

  @Column(DataType.TEXT)
  description!: string;

  @BelongsTo(() => Course)
  course!: Course;
}