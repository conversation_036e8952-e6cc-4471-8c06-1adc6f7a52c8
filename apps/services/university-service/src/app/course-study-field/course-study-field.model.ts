import { Table, Column, DataType, ForeignKey, BelongsTo } from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
import { Course } from '../course/course.model';
import { StudyField } from '../study-field/study-field.model';

@Table({ tableName: 'course_study_fields' })
export class CourseStudyField extends BaseModel {
  @ForeignKey(() => Course)
  @Column(DataType.BIGINT)
  courseId!: number;

  @ForeignKey(() => StudyField)
  @Column(DataType.BIGINT)
  studyFieldId!: number;

  @Column({ type: DataType.BOOLEAN, defaultValue: true })
  isActive!: boolean;

  @BelongsTo(() => Course)
  course!: Course;

  @BelongsTo(() => StudyField)
  studyField!: StudyField;
}
