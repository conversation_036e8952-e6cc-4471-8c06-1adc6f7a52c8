import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { CourseStudyField } from './course-study-field.model';
import { BaseRepository } from '@apply-goal-backend/database';
import { 
  CreateCourseStudyFieldDto, 
  UpdateCourseStudyFieldDto,
  GetCourseStudyFieldsRequestDto
} from '@apply-goal-backend/dto';
import { AppService } from '../app.service';
import { Course } from '../course/course.model';
import { StudyField } from '../study-field/study-field.model';
import { DatabaseUtil } from '@apply-goal-backend/utils';

@Injectable()
export class CourseStudyFieldService {
  private courseStudyFieldRepository: BaseRepository<CourseStudyField>;

  constructor(
    @InjectModel(CourseStudyField)
    private courseStudyFieldModel: typeof CourseStudyField,
    @InjectModel(Course)
    private courseModel: typeof Course,
    @InjectModel(StudyField)
    private studyFieldModel: typeof StudyField,
    private readonly appService: AppService
  ) {
    this.courseStudyFieldRepository = new BaseRepository<CourseStudyField>(courseStudyFieldModel);
  }

  async create(createDto: CreateCourseStudyFieldDto): Promise<CourseStudyField> {
    const startTime = Date.now();
    try {
      // Check if course exists
      const courseExists = await DatabaseUtil.entityExists(
        this.courseModel,
        createDto.courseId
      );

      if (!courseExists) {
        this.appService.trackProcessingDuration('course_study_field_create_course_not_found', (Date.now() - startTime) / 1000);
        throw new NotFoundException(`Course with ID ${createDto.courseId} not found`);
      }

      // Check if study field exists
      const studyFieldExists = await DatabaseUtil.entityExists(
        this.studyFieldModel,
        createDto.studyFieldId
      );

      if (!studyFieldExists) {
        this.appService.trackProcessingDuration('course_study_field_create_study_field_not_found', (Date.now() - startTime) / 1000);
        throw new NotFoundException(`Study field with ID ${createDto.studyFieldId} not found`);
      }

      // Check if the association already exists
      const existingAssociation = await this.courseStudyFieldRepository.findOne({
        where: {
          courseId: createDto.courseId,
          studyFieldId: createDto.studyFieldId
        }
      });

      if (existingAssociation) {
        this.appService.trackProcessingDuration('course_study_field_create_conflict', (Date.now() - startTime) / 1000);
        throw new ConflictException(`Association between course ID ${createDto.courseId} and study field ID ${createDto.studyFieldId} already exists`);
      }

      const courseStudyField = await this.courseStudyFieldRepository.create(createDto);
      this.appService.trackProcessingDuration('course_study_field_create_success', (Date.now() - startTime) / 1000);
      return courseStudyField;
    } catch (error) {
      this.appService.trackProcessingDuration('course_study_field_create_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }

  async findAll(query: GetCourseStudyFieldsRequestDto): Promise<{ rows: CourseStudyField[]; count: number }> {
    const { courseId, studyFieldId, isActive, page = 1, limit = 10 } = query;
    const where: any = {};
    
    if (courseId !== undefined) {
      where.courseId = courseId;
    }
    
    if (studyFieldId !== undefined) {
      where.studyFieldId = studyFieldId;
    }
    
    if (isActive !== undefined) {
      where.isActive = isActive;
    }
    
    return this.courseStudyFieldRepository.findAndCountAll({
      where,
      include: [
        {
          model: Course,
          attributes: ['id', 'title']
        },
        {
          model: StudyField,
          attributes: ['id', 'field']
        }
      ],
      offset: (page - 1) * limit,
      limit,
      order: [['createdAt', 'DESC']],
    });
  }

  async findOne(id: number): Promise<CourseStudyField> {
    const courseStudyField = await this.courseStudyFieldRepository.findById(id, {
      include: [
        {
          model: Course,
          attributes: ['id', 'title']
        },
        {
          model: StudyField,
          attributes: ['id', 'field']
        }
      ]
    });
    
    if (!courseStudyField) {
      throw new NotFoundException(`Course study field with ID ${id} not found`);
    }
    
    return courseStudyField;
  }

  async findByCourseId(courseId: number): Promise<CourseStudyField[]> {
    return this.courseStudyFieldRepository.findAll({
      where: { courseId, isActive: true },
      include: [
        {
          model: StudyField,
          attributes: ['id', 'field']
        }
      ],
      order: [['createdAt', 'DESC']]
    });
  }

  async findByStudyFieldId(studyFieldId: number): Promise<CourseStudyField[]> {
    return this.courseStudyFieldRepository.findAll({
      where: { studyFieldId, isActive: true },
      include: [
        {
          model: Course,
          attributes: ['id', 'title']
        }
      ],
      order: [['createdAt', 'DESC']]
    });
  }

  async update(id: number, updateDto: UpdateCourseStudyFieldDto): Promise<CourseStudyField> {
    const startTime = Date.now();
    try {
      const courseStudyField = await this.findOne(id);
      await courseStudyField.update(updateDto);
      this.appService.trackProcessingDuration('course_study_field_update_success', (Date.now() - startTime) / 1000);
      return courseStudyField;
    } catch (error) {
      this.appService.trackProcessingDuration('course_study_field_update_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }

  async remove(id: number): Promise<void> {
    const startTime = Date.now();
    try {
      const courseStudyField = await this.findOne(id);
      await courseStudyField.destroy();
      this.appService.trackProcessingDuration('course_study_field_delete_success', (Date.now() - startTime) / 1000);
    } catch (error) {
      this.appService.trackProcessingDuration('course_study_field_delete_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }
}