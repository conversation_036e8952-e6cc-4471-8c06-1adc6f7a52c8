import { Controller, Get, Post, Body, Param, Put, Delete, Query, ParseIntPipe } from '@nestjs/common';
import { CourseStudyFieldService } from './course-study-field.service';
import { 
  CreateCourseStudyFieldDto, 
  UpdateCourseStudyFieldDto,
  CourseStudyFieldDto,
  GetCourseStudyFieldsRequestDto,
  GetCourseStudyFieldsResponseDto
} from '@apply-goal-backend/dto';
import { AppService } from '../app.service';
import { AuditClientService } from '../audit.service';

@Controller('course-study-fields')
export class CourseStudyFieldController {
  constructor(
    private readonly courseStudyFieldService: CourseStudyFieldService,
    private readonly appService: AppService,
    private readonly auditService: AuditClientService
  ) {}

  @Post()
  async create(@Body() createDto: CreateCourseStudyFieldDto): Promise<CourseStudyFieldDto> {
    const startTime = Date.now();
    try {
      const result = await this.courseStudyFieldService.create(createDto);
      this.appService.trackProcessingDuration('course_study_field_create_api_success', (Date.now() - startTime) / 1000);
      
      // Audit logging
      this.auditService.logAction({
        action: 'CREATE_COURSE_STUDY_FIELD',
        entityType: 'CourseStudyField',
        entityId: result.id.toString(),
        details: createDto
      });
      
      return result as unknown as CourseStudyFieldDto;
    } catch (error) {
      this.appService.trackProcessingDuration('course_study_field_create_api_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }

  @Get()
  async findAll(
    @Query() query: GetCourseStudyFieldsRequestDto
  ): Promise<GetCourseStudyFieldsResponseDto> {
    const page = query.page || 1;
    const limit = query.limit || 10;
    
    const result = await this.courseStudyFieldService.findAll(query);
    
    return {
      total: result.count,
      page,
      limit,
      courseStudyFields: result.rows as unknown as CourseStudyFieldDto[]
    };
  }

  @Get('course/:courseId')
  async findByCourseId(
    @Param('courseId', ParseIntPipe) courseId: number
  ): Promise<CourseStudyFieldDto[]> {
    const fields = await this.courseStudyFieldService.findByCourseId(courseId);
    return fields as unknown as CourseStudyFieldDto[];
  }

  @Get('study-field/:studyFieldId')
  async findByStudyFieldId(
    @Param('studyFieldId', ParseIntPipe) studyFieldId: number
  ): Promise<CourseStudyFieldDto[]> {
    const courses = await this.courseStudyFieldService.findByStudyFieldId(studyFieldId);
    return courses as unknown as CourseStudyFieldDto[];
  }

  @Get(':id')
  async findOne(@Param('id', ParseIntPipe) id: number): Promise<CourseStudyFieldDto> {
    return this.courseStudyFieldService.findOne(id) as unknown as CourseStudyFieldDto;
  }

  @Put(':id')
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateCourseStudyFieldDto
  ): Promise<CourseStudyFieldDto> {
    const startTime = Date.now();
    try {
      const result = await this.courseStudyFieldService.update(id, updateDto);
      this.appService.trackProcessingDuration('course_study_field_update_api_success', (Date.now() - startTime) / 1000);
      
      // Audit logging
      this.auditService.logAction({
        action: 'UPDATE_COURSE_STUDY_FIELD',
        entityType: 'CourseStudyField',
        entityId: id.toString(),
        details: updateDto
      });
      
      return result as unknown as CourseStudyFieldDto;
    } catch (error) {
      this.appService.trackProcessingDuration('course_study_field_update_api_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }

  @Delete(':id')
  async remove(@Param('id', ParseIntPipe) id: number): Promise<{ id: number; success: boolean }> {
    const startTime = Date.now();
    try {
      await this.courseStudyFieldService.remove(id);
      this.appService.trackProcessingDuration('course_study_field_delete_api_success', (Date.now() - startTime) / 1000);
      
      // Audit logging
      this.auditService.logAction({
        action: 'DELETE_COURSE_STUDY_FIELD',
        entityType: 'CourseStudyField',
        entityId: id.toString(),
        details: { id }
      });
      
      return { id, success: true };
    } catch (error) {
      this.appService.trackProcessingDuration('course_study_field_delete_api_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }
}