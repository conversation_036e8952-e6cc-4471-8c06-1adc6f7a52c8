import { <PERSON>du<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { CourseStudyField } from './course-study-field.model';
import { CourseStudyFieldService } from './course-study-field.service';
import { CourseStudyFieldController } from './course-study-field.controller';
import { AuditClientService } from '../audit.service';
import { Course } from '../course/course.model';
import { StudyField } from '../study-field/study-field.model';

@Module({
  imports: [
    SequelizeModule.forFeature([CourseStudyField, Course, StudyField]),
  ],
  controllers: [CourseStudyFieldController],
  providers: [CourseStudyFieldService, AuditClientService],
  exports: [CourseStudyFieldService],
})
export class CourseStudyFieldModule {}