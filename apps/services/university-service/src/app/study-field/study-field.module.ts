import { <PERSON>du<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { StudyField } from './study-field.model';
import { StudyFieldService } from './study-field.service';
import { StudyFieldController } from './study-field.controller';
import { AuditClientService } from '../audit.service';

@Module({
  imports: [
    SequelizeModule.forFeature([StudyField]),
  ],
  controllers: [StudyFieldController],
  providers: [StudyFieldService, AuditClientService],
  exports: [StudyFieldService],
})
export class StudyFieldModule {}