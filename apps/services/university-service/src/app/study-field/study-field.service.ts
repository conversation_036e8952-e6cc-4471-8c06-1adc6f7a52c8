import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { StudyField } from './study-field.model';
import { BaseRepository } from '@apply-goal-backend/database';
import { CreateStudyFieldDto, UpdateStudyFieldDto } from '@apply-goal-backend/dto';
import { Op } from 'sequelize';
import { AppService } from '../app.service';
import { DatabaseUtil } from '@apply-goal-backend/utils';

@Injectable()
export class StudyFieldService {
  private studyFieldRepository: BaseRepository<StudyField>;

  constructor(
    @InjectModel(StudyField)
    private studyFieldModel: typeof StudyField,
    private readonly appService: AppService
  ) {
    this.studyFieldRepository = new BaseRepository<StudyField>(studyFieldModel);
  }

  async create(createStudyFieldDto: CreateStudyFieldDto): Promise<StudyField> {
    const startTime = Date.now();
    try {
      // Check if study field with same name already exists
      const existingField = await this.studyFieldRepository.findOne({
        where: {
          field: createStudyFieldDto.field
        }
      });

      if (existingField) {
        this.appService.trackProcessingDuration('study_field_create_conflict', (Date.now() - startTime) / 1000);
        throw new ConflictException(`Study field with name "${createStudyFieldDto.field}" already exists`);
      }

      // Check if parent field exists if parentFieldId is provided
      if (createStudyFieldDto.parentFieldId) {
        const parentExists = await DatabaseUtil.entityExists(
          this.studyFieldModel,
          createStudyFieldDto.parentFieldId
        );

        if (!parentExists) {
          this.appService.trackProcessingDuration('study_field_create_parent_not_found', (Date.now() - startTime) / 1000);
          throw new NotFoundException(`Parent study field with ID ${createStudyFieldDto.parentFieldId} not found`);
        }
      }

      const studyField = await this.studyFieldRepository.create(createStudyFieldDto);
      this.appService.trackProcessingDuration('study_field_create_success', (Date.now() - startTime) / 1000);
      return studyField;
    } catch (error) {
      this.appService.trackProcessingDuration('study_field_create_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }

  async findAll(page: number = 1, limit: number = 10, isActive?: boolean): Promise<{ rows: StudyField[]; count: number }> {
    const where: any = {};
    
    if (isActive !== undefined) {
      where.isActive = isActive;
    }
    
    return this.studyFieldRepository.findAndCountAll({
      where,
      include: [
        {
          model: StudyField,
          as: 'parentField',
          attributes: ['id', 'field']
        },
        {
          model: StudyField,
          as: 'childFields',
          attributes: ['id', 'field', 'isActive']
        }
      ],
      offset: (page - 1) * limit,
      limit,
      order: [['createdAt', 'DESC']],
    });
  }

  async findOne(id: number): Promise<StudyField> {
    const studyField = await this.studyFieldRepository.findById(id, {
      include: [
        {
          model: StudyField,
          as: 'parentField',
          attributes: ['id', 'field']
        },
        {
          model: StudyField,
          as: 'childFields',
          attributes: ['id', 'field', 'isActive']
        }
      ]
    });
    
    if (!studyField) {
      throw new NotFoundException(`Study field with ID ${id} not found`);
    }
    
    return studyField;
  }

  async update(id: number, updateStudyFieldDto: UpdateStudyFieldDto): Promise<StudyField> {
    const startTime = Date.now();
    try {
      // If updating field name, check for uniqueness
      if (updateStudyFieldDto.field) {
        const existingField = await this.studyFieldRepository.findOne({
          where: {
            field: updateStudyFieldDto.field,
            id: { [Op.ne]: id } // Exclude current field
          }
        });

        if (existingField) {
          this.appService.trackProcessingDuration('study_field_update_conflict', (Date.now() - startTime) / 1000);
          throw new ConflictException(`Study field with name "${updateStudyFieldDto.field}" already exists`);
        }
      }

      // Check if parent field exists if parentFieldId is provided
      if (updateStudyFieldDto.parentFieldId) {
        // Prevent circular reference (a field cannot be its own parent)
        if (updateStudyFieldDto.parentFieldId === id) {
          throw new ConflictException('A study field cannot be its own parent');
        }

        const parentExists = await DatabaseUtil.entityExists(
          this.studyFieldModel,
          updateStudyFieldDto.parentFieldId
        );

        if (!parentExists) {
          this.appService.trackProcessingDuration('study_field_update_parent_not_found', (Date.now() - startTime) / 1000);
          throw new NotFoundException(`Parent study field with ID ${updateStudyFieldDto.parentFieldId} not found`);
        }
      }

      const studyField = await this.findOne(id);
      await studyField.update(updateStudyFieldDto);
      this.appService.trackProcessingDuration('study_field_update_success', (Date.now() - startTime) / 1000);
      return studyField;
    } catch (error) {
      this.appService.trackProcessingDuration('study_field_update_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }

  async remove(id: number): Promise<void> {
    const startTime = Date.now();
    try {
      const deleted = await this.studyFieldRepository.delete(id);
      
      if (deleted === 0) {
        this.appService.trackProcessingDuration('study_field_delete_not_found', (Date.now() - startTime) / 1000);
        throw new NotFoundException(`Study field with ID ${id} not found`);
      }
      
      this.appService.trackProcessingDuration('study_field_delete_success', (Date.now() - startTime) / 1000);
    } catch (error) {
      this.appService.trackProcessingDuration('study_field_delete_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }

  async getParentFields(): Promise<StudyField[]> {
    return this.studyFieldRepository.findAll({
      where: {
        parentFieldId: null,
        isActive: true
      },
      include: [
        {
          model: StudyField,
          as: 'childFields',
          attributes: ['id', 'field', 'isActive'],
          where: { isActive: true },
          required: false
        }
      ],
      order: [['field', 'ASC']]
    });
  }

  async getChildFields(parentFieldId: number): Promise<StudyField[]> {
    return this.studyFieldRepository.findAll({
      where: {
        parentFieldId,
        isActive: true
      },
      order: [['field', 'ASC']]
    });
  }
}
