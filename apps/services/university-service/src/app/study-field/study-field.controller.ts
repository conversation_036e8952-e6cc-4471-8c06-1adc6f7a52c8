import { Controller, Get, Post, Body, Param, Put, Delete, Query, ParseIntPipe, ParseBoolPipe } from '@nestjs/common';
import { StudyFieldService } from './study-field.service';
import { 
  CreateStudyFieldDto, 
  UpdateStudyFieldDto,
  StudyFieldDto
} from '@apply-goal-backend/dto';
import { AppService } from '../app.service';
import { AuditClientService } from '../audit.service';

@Controller('study-fields')
export class StudyFieldController {
  constructor(
    private readonly studyFieldService: StudyFieldService,
    private readonly appService: AppService,
    private readonly auditService: AuditClientService
  ) {}

  @Post()
  async create(@Body() createStudyFieldDto: CreateStudyFieldDto): Promise<StudyFieldDto> {
    const startTime = Date.now();
    try {
      console.log("createStudyFieldDto",createStudyFieldDto);
      const result = await this.studyFieldService.create(createStudyFieldDto);
      this.appService.trackProcessingDuration('study_field_create_api_success', (Date.now() - startTime) / 1000);
      
      // Audit logging
      this.auditService.logAction({
        action: 'CREATE_STUDY_FIELD',
        entityType: 'StudyField',
        entityId: result.id.toString(),
        details: createStudyFieldDto
      });
      
      return result as unknown as StudyFieldDto;
    } catch (error) {
      this.appService.trackProcessingDuration('study_field_create_api_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }

  @Get()
  async findAll(
    @Query('page', new ParseIntPipe({ optional: true })) page: number = 1,
    @Query('limit', new ParseIntPipe({ optional: true })) limit: number = 10,
    @Query('isActive', new ParseBoolPipe({ optional: true })) isActive?: boolean
  ): Promise<{ rows: StudyFieldDto[]; count: number }> {
    const result = await this.studyFieldService.findAll(page, limit, isActive);
    return {
      rows: result.rows as unknown as StudyFieldDto[],
      count: result.count
    };
  }

  @Get('parent-fields')
  async getParentFields(): Promise<StudyFieldDto[]> {
    const result = await this.studyFieldService.getParentFields();
    return result as unknown as StudyFieldDto[];
  }

  @Get('child-fields/:parentField')
  async getChildFields(@Param('parentField') parentField: number): Promise<StudyFieldDto[]> {
    const result = await this.studyFieldService.getChildFields(parentField);
    return result as unknown as StudyFieldDto[];
  }

  @Get(':id')
  async findOne(@Param('id', ParseIntPipe) id: number): Promise<StudyFieldDto> {
    return this.studyFieldService.findOne(id) as unknown as StudyFieldDto;
  }

  @Put(':id')
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateStudyFieldDto: UpdateStudyFieldDto
  ): Promise<StudyFieldDto> {
    const startTime = Date.now();
    try {
      const result = await this.studyFieldService.update(id, updateStudyFieldDto);
      this.appService.trackProcessingDuration('study_field_update_api_success', (Date.now() - startTime) / 1000);
      
      // Audit logging
      this.auditService.logAction({
        action: 'UPDATE_STUDY_FIELD',
        entityType: 'StudyField',
        entityId: id.toString(),
        details: updateStudyFieldDto
      });
      
      return result as unknown as StudyFieldDto;
    } catch (error) {
      this.appService.trackProcessingDuration('study_field_update_api_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }

  @Delete(':id')
  async remove(@Param('id', ParseIntPipe) id: number): Promise<{ id: number; success: boolean }> {
    const startTime = Date.now();
    try {
      await this.studyFieldService.remove(id);
      this.appService.trackProcessingDuration('study_field_delete_api_success', (Date.now() - startTime) / 1000);
      
      // Audit logging
      this.auditService.logAction({
        action: 'DELETE_STUDY_FIELD',
        entityType: 'StudyField',
        entityId: id.toString(),
        details: { id }
      });
      
      return { id, success: true };
    } catch (error) {
      this.appService.trackProcessingDuration('study_field_delete_api_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }
}