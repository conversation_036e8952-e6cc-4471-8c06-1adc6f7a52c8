import { Table, Column, DataType, HasMany, ForeignKey, BelongsTo } from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';

@Table({ tableName: 'study_fields' })
export class StudyField extends BaseModel {
  @Column(DataType.STRING)
  field!: string;

  @ForeignKey(() => StudyField)
  @Column({ type: DataType.BIGINT, allowNull: true })
  parentFieldId!: number | null;

  @BelongsTo(() => StudyField, 'parentFieldId')
  parentField!: StudyField;

  @HasMany(() => StudyField, 'parentFieldId')
  childFields!: StudyField[];

  @Column({ type: DataType.BOOLEAN, defaultValue: true })
  isActive!: boolean;
}
