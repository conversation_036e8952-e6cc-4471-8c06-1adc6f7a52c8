// import { Injectable } from '@nestjs/common';
// import { InjectModel } from '@nestjs/sequelize';
// import { Country } from '../../models/country.model';
// import { CreateCountryRequestDto, UpdateCountryRequestDto } from '@apply-goal-backend/dto';

// @Injectable()
// export class CountryRepository {
//   constructor(
//     @InjectModel(Country)
//     private countryModel: typeof Country,
//   ) {}

//   async create(data: CreateCountryRequestDto): Promise<Country> {
//     return this.countryModel.create({ ...data });
//   }

//   async findById(id: number): Promise<Country | null> {
//     return this.countryModel.findByPk(id);
//   }

//   async findAll(page: number = 1, limit: number = 10): Promise<{ rows: Country[]; count: number }> {
//     return this.countryModel.findAndCountAll({
//       offset: (page - 1) * limit,
//       limit,
//     });
//   }

//   async update(id: number, data: UpdateCountryRequestDto): Promise<[number, Country[]]> {
//     return this.countryModel.update(data, {
//       where: { id },
//       returning: true,
//     });
//   }

//   async delete(id: number): Promise<number> {
//     return this.countryModel.destroy({
//       where: { id },
//     });
//   }
// }