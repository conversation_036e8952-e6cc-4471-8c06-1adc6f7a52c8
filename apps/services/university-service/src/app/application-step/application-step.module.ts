import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { ApplicationStep } from './application-step.model';
import { ApplicationStepService } from './application-step.service';
import { ApplicationStepController } from './application-step.controller';
import { AuditClientService } from '../audit.service';
import { AppService } from '../app.service';

@Module({
  imports: [
    SequelizeModule.forFeature([ApplicationStep]),
  ],
  controllers: [ApplicationStepController],
  providers: [ApplicationStepService, AuditClientService],
  exports: [ApplicationStepService],
})
export class ApplicationStepModule {}