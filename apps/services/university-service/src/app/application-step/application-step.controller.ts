import { Controller, Get, Post, Body, Param, Put, Delete, Query, ParseIntPipe } from '@nestjs/common';
import { ApplicationStepService } from './application-step.service';
import { CreateApplicationStepDto, UpdateApplicationStepDto } from '@apply-goal-backend/dto';
import { AppService } from '../app.service';
import { AuditClientService } from '../audit.service';

@Controller('application-steps')
export class ApplicationStepController {
  constructor(
    private readonly applicationStepService: ApplicationStepService,
    private readonly appService: AppService,
    private readonly auditService: AuditClientService
  ) {}

  @Post()
  async create(@Body() createApplicationStepDto: CreateApplicationStepDto) {
    const startTime = Date.now();
    try {
      const result = await this.applicationStepService.create(createApplicationStepDto);
      this.appService.trackProcessingDuration('application_step_create_api_success', (Date.now() - startTime) / 1000);
      
      // Audit logging
      this.auditService.logAction({
        action: 'CREATE_APPLICATION_STEP',
        entityType: 'ApplicationStep',
        entityId: result.id.toString(),
        details: createApplicationStepDto
      });
      
      return result;
    } catch (error) {
      this.appService.trackProcessingDuration('application_step_create_api_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }

  @Get()
  async findAll(@Query('universityId') universityId?: number) {
    const startTime = Date.now();
    try {
      const result = await this.applicationStepService.findAll(universityId);
      this.appService.trackProcessingDuration('application_step_find_all_api_success', (Date.now() - startTime) / 1000);
      return result;
    } catch (error) {
      this.appService.trackProcessingDuration('application_step_find_all_api_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }

  @Get(':id')
  async findOne(@Param('id', ParseIntPipe) id: number) {
    const startTime = Date.now();
    try {
      const result = await this.applicationStepService.findOne(id);
      this.appService.trackProcessingDuration('application_step_find_one_api_success', (Date.now() - startTime) / 1000);
      return result;
    } catch (error) {
      this.appService.trackProcessingDuration('application_step_find_one_api_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }

  @Put(':id')
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateApplicationStepDto: UpdateApplicationStepDto
  ) {
    const startTime = Date.now();
    try {
      const result = await this.applicationStepService.update(id, updateApplicationStepDto);
      this.appService.trackProcessingDuration('application_step_update_api_success', (Date.now() - startTime) / 1000);
      
      // Audit logging
      this.auditService.logAction({
        action: 'UPDATE_APPLICATION_STEP',
        entityType: 'ApplicationStep',
        entityId: id.toString(),
        details: updateApplicationStepDto
      });
      
      return result;
    } catch (error) {
      this.appService.trackProcessingDuration('application_step_update_api_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }

  @Delete(':id')
  async remove(@Param('id', ParseIntPipe) id: number) {
    const startTime = Date.now();
    try {
      await this.applicationStepService.remove(id);
      this.appService.trackProcessingDuration('application_step_delete_api_success', (Date.now() - startTime) / 1000);
      
      // Audit logging
      this.auditService.logAction({
        action: 'DELETE_APPLICATION_STEP',
        entityType: 'ApplicationStep',
        entityId: id.toString(),
        details: { id }
      });
      
      return { success: true };
    } catch (error) {
      this.appService.trackProcessingDuration('application_step_delete_api_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }
}