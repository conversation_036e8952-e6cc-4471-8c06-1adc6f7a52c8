import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { ApplicationStep } from './application-step.model';
import { BaseRepository } from '@apply-goal-backend/database';
import { CreateApplicationStepDto, UpdateApplicationStepDto } from '@apply-goal-backend/dto';
import { AppService } from '../app.service';

@Injectable()
export class ApplicationStepService {
  private applicationStepRepository: BaseRepository<ApplicationStep>;

  constructor(
    @InjectModel(ApplicationStep)
    private applicationStepModel: typeof ApplicationStep,
    private readonly appService: AppService
  ) {
    this.applicationStepRepository = new BaseRepository<ApplicationStep>(applicationStepModel);
  }

  async create(createApplicationStepDto: CreateApplicationStepDto): Promise<ApplicationStep> {
    const startTime = Date.now();
    try {
      const applicationStep = await this.applicationStepRepository.create(createApplicationStepDto);
      this.appService.trackProcessingDuration('application_step_create_success', (Date.now() - startTime) / 1000);
      return applicationStep;
    } catch (error) {
      this.appService.trackProcessingDuration('application_step_create_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }

  async findAll(universityId?: number): Promise<ApplicationStep[]> {
    const where = universityId ? { universityId } : {};
    return this.applicationStepRepository.findAll({ where });
  }

  async findOne(id: number): Promise<ApplicationStep> {
    const applicationStep = await this.applicationStepRepository.findById(id);
    if (!applicationStep) {
      throw new NotFoundException(`Application Step with ID ${id} not found`);
    }
    return applicationStep;
  }

  async update(id: number, updateApplicationStepDto: UpdateApplicationStepDto): Promise<ApplicationStep> {
    const applicationStep = await this.findOne(id);
    await applicationStep.update(updateApplicationStepDto);
    return applicationStep;
  }

  async remove(id: number): Promise<void> {
    const deleted = await this.applicationStepRepository.delete(id);
    
    if (deleted === 0) {
      throw new NotFoundException(`Application Step with ID ${id} not found`);
    }
  }

  // async createBulkForUniversity(universityId: number, steps: any[]): Promise<ApplicationStep[]> {
    async createBulkForUniversity(universityId: number, steps: any[]): Promise<any> {
    const startTime = Date.now();
    try {
      console.log("CheckApplicationStep",steps)
      // const applicationSteps = await Promise.all(
      //   steps.map(step => this.create({
      //     universityId,
      //     stepNumber: step.orderNumber,
      //     description: step.step,
      //     isActive: true
      //   }))
      // );
      // this.appService.trackProcessingDuration('application_step_bulk_create_success', (Date.now() - startTime) / 1000);
      return "applicationSteps";
    } catch (error) {
      this.appService.trackProcessingDuration('application_step_bulk_create_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }

  // async removeAllForUniversity(universityId: number): Promise<void> {
  //   await this.applicationStepRepository.destroy({
  //     where: { universityId }
  //   });
  // }
}