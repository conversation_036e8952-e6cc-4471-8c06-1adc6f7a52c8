import { Table, Column, DataType, ForeignKey, BelongsTo } from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
import { University } from '../university/university.model';

@Table({ tableName: 'application_steps' })
export class ApplicationStep extends BaseModel {
  @ForeignKey(() => University)
  @Column(DataType.BIGINT)
  universityId!: number;

  @Column(DataType.INTEGER)
  stepNumber!: number;

  @Column(DataType.TEXT)
  description!: string;

  @Column({ type: DataType.BOOLEAN, defaultValue: true })
  isActive!: boolean;

  @BelongsTo(() => University)
  university!: University;
}