import { Table, Column, DataType, ForeignKey, BelongsTo } from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
import { University } from '../university/university.model';

@Table({ tableName: 'application_fees' })
export class ApplicationFee extends BaseModel {
  @ForeignKey(() => University)
  @Column(DataType.BIGINT)
  universityId!: number;

  @Column(DataType.INTEGER)
  stepNumber!: number;

  @Column(DataType.TEXT)
  description!: string;

  @Column(DataType.TEXT)
  minApplicationCriteria!: string;

  @Column(DataType.FLOAT)
  standardApplicationFee!: number;

  @Column(DataType.FLOAT)
  avgTuitionFee!: number;

  @Column(DataType.FLOAT)
  costOfLiving!: number;

  @Column(DataType.STRING)
  avgUndergraduateProgram!: string;

  @Column(DataType.STRING)
  avgGraduateProgram!: string;

  @Column(DataType.STRING)
  discountType!: string;

  @Column(DataType.FLOAT)
  discountValue!: number;

  @Column(DataType.DATE)
  startDate!: Date;

  @Column(DataType.DATE)
  endDate!: Date;

  @Column(DataType.STRING)
  promoCode!: string;

  @Column(DataType.INTEGER)
  maxRedemptionAllowed!: number;

  @Column({ type: DataType.BOOLEAN, defaultValue: true })
  isActive!: boolean;

  @BelongsTo(() => University)
  university!: University;
}