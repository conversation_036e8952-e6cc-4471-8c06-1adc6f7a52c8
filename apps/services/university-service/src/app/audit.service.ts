import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { Client, ClientGrpc } from '@nestjs/microservices';
import { Transport } from '@nestjs/microservices';
import { join } from 'path';
import { Observable, throwError } from 'rxjs';
import { catchError, timeout } from 'rxjs/operators';
import { AppService } from './app.service';

// Define the interface for the audit service
interface AuditService {
  createAuditLog(data: {
    action: string;
    entityType: string;
    entityId: string;
    userId?: string;
    details?: any;
    ipAddress?: string;
    userAgent?: string;
    timestamp?: string;
    service: string;
  }): Observable<{ success: boolean; id: string }>;
}

@Injectable()
export class AuditClientService implements OnModuleInit {
  private readonly logger = new Logger(AuditClientService.name);

  @Client({
    transport: Transport.GRPC,
    options: {
      package: 'audit',
      protoPath: join(process.cwd(), 'libs/shared/dto/src/lib/audit/audit.proto'),
      url: 'audit-logging:50051',
      loader: {
        keepCase: true,
        longs: String,
        enums: String,
        defaults: true,
        oneofs: true,
      },
    },
  })
  private readonly client: ClientGrpc;

  private auditService: AuditService;

  constructor(private readonly appService: AppService) {}

  onModuleInit() {
    this.auditService = this.client.getService<AuditService>('AuditService');
  }

  private handleError(operation = 'operation') {
    return (error: any) => {
      this.logger.error(`${operation} failed: ${error.message}`, error.stack);
      return throwError(() => error);
    };
  }

  logAction(data: {
    action: string;
    entityType: string;
    entityId: string;
    userId?: string;
    details?: any;
    ipAddress?: string;
    userAgent?: string;
  }) {
    const startTime = Date.now();
    
    try {
      const auditLog = {
        ...data,
        timestamp: new Date().toISOString(),
        service: 'university-service',
      };

      this.auditService
        .createAuditLog(auditLog)
        .pipe(
          timeout(5000), // 5 second timeout
          catchError(this.handleError('createAuditLog'))
        )
        .subscribe({
          next: (response) => {
            this.appService.trackProcessingDuration(
              'audit_log_success',
              (Date.now() - startTime) / 1000
            );
            this.logger.debug(
              `Audit log created successfully: ${response.id}`
            );
          },
          error: (error) => {
            this.appService.trackProcessingDuration(
              'audit_log_error',
              (Date.now() - startTime) / 1000
            );
            this.logger.error(
              `Failed to create audit log: ${error.message}`,
              error.stack
            );
          },
        });
    } catch (error) {
      this.appService.trackProcessingDuration(
        'audit_log_error',
        (Date.now() - startTime) / 1000
      );
      this.logger.error(
        `Error preparing audit log: ${error.message}`,
        error.stack
      );
    }
  }
}
