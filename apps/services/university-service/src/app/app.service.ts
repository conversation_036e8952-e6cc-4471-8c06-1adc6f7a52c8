import { Injectable } from '@nestjs/common';
import { MetricsService } from '@apply-goal-backend/monitoring';

@Injectable()
export class AppService {
  private requestCounter: any;
  private requestDuration: any;
  private universityCounter: any;
  private programCounter: any;
  private applicationCounter: any;
  private processingDuration: any;
  private activeUniversitiesGauge: any;
  private activeProgramsGauge: any;
  private countryCounter: any;
  private auditLogCounter: any;
  private grpcRequestCounter: any;
  private grpcRequestDuration: any;

  constructor(private readonly metricsService: MetricsService) {
    // Initialize metrics
    this.requestCounter = this.metricsService.createCounter(
      'http_requests_total',
      'Total number of HTTP requests',
      ['method', 'endpoint', 'status']
    );

    this.requestDuration = this.metricsService.createHistogram(
      'http_request_duration_seconds',
      'HTTP request duration in seconds',
      ['method', 'endpoint'],
      [0.1, 0.3, 0.5, 0.7, 1, 3, 5, 7, 10]
    );

    this.universityCounter = this.metricsService.createCounter(
      'university_operations_total',
      'Total number of university operations',
      ['status', 'type']
    );

    this.programCounter = this.metricsService.createCounter(
      'program_operations_total',
      'Total number of program operations',
      ['university_id', 'level', 'status']
    );

    this.applicationCounter = this.metricsService.createCounter(
      'application_operations_total',
      'Total number of application operations',
      ['status', 'program_type']
    );

    this.processingDuration = this.metricsService.createHistogram(
      'processing_duration_seconds',
      'Processing duration in seconds',
      ['operation_type'],
      [0.1, 0.3, 0.5, 0.7, 1, 3, 5, 7, 10]
    );

    this.activeUniversitiesGauge = this.metricsService.createGauge(
      'active_universities_current',
      'Current number of active universities',
      ['region', 'type']
    );

    this.activeProgramsGauge = this.metricsService.createGauge(
      'active_programs_current',
      'Current number of active programs',
      ['university_id', 'level']
    );

    // Country metrics
    this.countryCounter = this.metricsService.createCounter(
      'country_operations_total',
      'Total number of country operations',
      ['operation', 'status']
    );

    // Audit log metrics
    this.auditLogCounter = this.metricsService.createCounter(
      'audit_log_operations_total',
      'Total number of audit log operations',
      ['status', 'action']
    );

    // gRPC metrics
    this.grpcRequestCounter = this.metricsService.createCounter(
      'grpc_requests_total',
      'Total number of gRPC requests',
      ['method', 'status']
    );

    this.grpcRequestDuration = this.metricsService.createHistogram(
      'grpc_request_duration_seconds',
      'gRPC request duration in seconds',
      ['method'],
      [0.1, 0.3, 0.5, 0.7, 1, 3, 5, 7, 10]
    );
  }

  // Track HTTP request
  trackHttpRequest(method: string, endpoint: string, status: string, duration: number) {
    this.requestCounter.inc({ method, endpoint, status });
    this.requestDuration.observe({ method, endpoint }, duration);
  }

  // Track university operations
  trackUniversity(status: string, type: string) {
    this.universityCounter.inc({ status, type });
  }

  // Track program operations
  trackProgram(universityId: string, level: string, status: string) {
    this.programCounter.inc({ university_id: universityId, level, status });
  }

  // Track application operations
  trackApplication(status: string, programType: string) {
    this.applicationCounter.inc({ status, program_type: programType });
  }

  // Track processing duration
  trackProcessingDuration(operationType: string, durationSeconds: number) {
    this.processingDuration.observe({ operation_type: operationType }, durationSeconds);
  }

  // Update active universities count
  updateActiveUniversities(region: string, type: string, count: number) {
    this.activeUniversitiesGauge.set({ region, type }, count);
  }

  // Update active programs count
  updateActivePrograms(universityId: string, level: string, count: number) {
    this.activeProgramsGauge.set({ university_id: universityId, level }, count);
  }

  // Track country operations
  trackCountryOperation(operation: string, status: string) {
    this.countryCounter.inc({ operation, status });
  }

  // Track audit log operations
  trackAuditLog(status: string, action: string) {
    this.auditLogCounter.inc({ status, action });
  }

  // Track gRPC request
  trackGrpcRequest(method: string, status: string) {
    this.grpcRequestCounter.inc({ method, status });
  }

  // Track gRPC request duration
  trackGrpcRequestDuration(method: string, durationSeconds: number) {
    this.grpcRequestDuration.observe({ method }, durationSeconds);
  }

  // Combined method to track gRPC metrics
  trackGrpcOperation(method: string, status: string, durationSeconds: number) {
    this.trackGrpcRequest(method, status);
    this.trackGrpcRequestDuration(method, durationSeconds);
  }

  // Combined method to track country operations with duration
  trackCountryOperationWithDuration(operation: string, status: string, durationSeconds: number) {
    this.trackCountryOperation(operation, status);
    this.trackProcessingDuration(`country_${operation}`, durationSeconds);
  }

  // Combined method to track audit log with duration
  trackAuditLogWithDuration(action: string, status: string, durationSeconds: number) {
    this.trackAuditLog(status, action);
    this.trackProcessingDuration(`audit_${action}`, durationSeconds);
  }

  getData(): { message: string } {
    this.trackHttpRequest('GET', '/api', '200', 0.1);
    return { message: 'Hello from University Service' };
  }
}
