import { Table, Column, DataType, ForeignKey, BelongsTo } from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
import { University } from '../university/university.model';

@Table({ tableName: 'semester_timeframes' })
export class SemesterTimeframe extends BaseModel {
  @ForeignKey(() => University)
  @Column(DataType.BIGINT)
  universityId!: number;

  @Column(DataType.INTEGER)
  semesterNumber!: number;

  @Column(DataType.STRING)
  duration!: string;

  @Column({ type: DataType.BOOLEAN, defaultValue: true })
  isActive!: boolean;

  @BelongsTo(() => University)
  university!: University;
}