import { Catch, ArgumentsHost, Logger, HttpException, ConflictException, NotFoundException, BadRequestException, UnauthorizedException, ForbiddenException } from '@nestjs/common';
import { BaseRpcExceptionFilter } from '@nestjs/microservices';
import { Observable, throwError } from 'rxjs';
import { status } from '@grpc/grpc-js';

@Catch()
export class GrpcExceptionFilter extends BaseRpcExceptionFilter {
  private readonly logger = new Logger(GrpcExceptionFilter.name);
  private readonly ignoredPaths = ['/favicon.ico'];

  catch(exception: any, host: ArgumentsHost): Observable<any> {
    // Check if this is an HTTP request we should ignore
    if (host.getType() === 'http') {
      const ctx = host.switchToHttp();
      const request = ctx.getRequest<Request>();
      
      // Ignore certain paths like favicon.ico
      if (this.ignoredPaths.includes((request as any).path)) {
        return throwError(() => exception);
      }
    }
    
    this.logger.error(`GRPC Exception: ${exception?.message || 'Unknown error'}`, exception?.stack);
    
    // Map NestJS exceptions to gRPC status codes
    let code = status.INTERNAL;
    
    // Check exception type directly instead of using instanceof
    if (exception instanceof ConflictException) {
      code = status.ALREADY_EXISTS;
    } else if (exception instanceof NotFoundException) {
      code = status.NOT_FOUND;
    } else if (exception instanceof BadRequestException) {
      code = status.INVALID_ARGUMENT;
    } else if (exception instanceof UnauthorizedException) {
      code = status.UNAUTHENTICATED;
    } else if (exception instanceof ForbiddenException) {
      code = status.PERMISSION_DENIED;
    } else if (exception.name === 'ConflictException') {
      code = status.ALREADY_EXISTS;
    } else if (exception.name === 'NotFoundException') {
      code = status.NOT_FOUND;
    } else if (exception.name === 'BadRequestException') {
      code = status.INVALID_ARGUMENT;
    } else if (exception.name === 'UnauthorizedException') {
      code = status.UNAUTHENTICATED;
    } else if (exception.name === 'ForbiddenException') {
      code = status.PERMISSION_DENIED;
    }
    
    // Format the error for gRPC
    const grpcError = {
      code: code,
      message: exception?.message || 'Internal server error',
      details: exception?.stack || '',
    };
    
    // Return a properly formatted gRPC error
    return throwError(() => grpcError);
  }
}
