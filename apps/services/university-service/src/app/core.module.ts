// src/core/core.module.ts
import { Module, Global } from '@nestjs/common';
import { MonitoringModule } from '@apply-goal-backend/monitoring';
import { AppService } from './app.service';
import { AuditClientService } from './audit.service';

@Global() // Make this module global so its providers are available everywhere
@Module({
  imports: [
    MonitoringModule.forRoot({
      metrics: {
        serviceName: 'university-service',
        serviceVersion: '1.0.0',
        port: parseInt(process.env.METRICS_PORT || '5009', 10),
        path: '/metrics',
        labels: {
          environment: process.env.NODE_ENV || 'development',
        },
      },
      tracing: {
        serviceName: 'university-service',
        jaegerEndpoint: 'http://jaeger:4318/v1/traces',
      },
    }),
  ],
  providers: [AppService, AuditClientService],
  exports: [AppService, AuditClientService, MonitoringModule],
})
export class CoreModule {}
