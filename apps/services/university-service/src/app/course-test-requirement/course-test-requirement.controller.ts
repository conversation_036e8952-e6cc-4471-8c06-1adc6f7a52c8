import { Controller, Get, Post, Body, Param, Put, Delete, Query, ParseIntPipe } from '@nestjs/common';
import { CourseTestRequirementService } from './course-test-requirement.service';
import { 
  CourseTestRequirementDto,
  CreateCourseTestRequirementRequestDto,
  UpdateCourseTestRequirementRequestDto,
  GetAllCourseTestRequirementsRequestDto,
  GetAllCourseTestRequirementsResponseDto
} from '@apply-goal-backend/dto';
import { AppService } from '../app.service';
import { AuditClientService } from '../audit.service';

@Controller('course-test-requirements')
export class CourseTestRequirementController {
  constructor(
    private readonly courseTestRequirementService: CourseTestRequirementService,
    private readonly appService: AppService,
    private readonly auditService: AuditClientService
  ) {}

  @Post()
  async create(@Body() createDto: CreateCourseTestRequirementRequestDto): Promise<CourseTestRequirementDto> {
    const startTime = Date.now();
    try {
      const result = await this.courseTestRequirementService.create(createDto);
      this.appService.trackProcessingDuration('course_test_requirement_create_api_success', (Date.now() - startTime) / 1000);
      
      // Audit logging
      this.auditService.logAction({
        action: 'CREATE_COURSE_TEST_REQUIREMENT',
        entityType: 'CourseTestRequirement',
        entityId: result.id.toString(),
        details: createDto
      });
      
      return result as unknown as CourseTestRequirementDto;
    } catch (error) {
      this.appService.trackProcessingDuration('course_test_requirement_create_api_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }

  @Get()
  async findAll(
    @Query() query: GetAllCourseTestRequirementsRequestDto
  ): Promise<GetAllCourseTestRequirementsResponseDto> {
    const page = query.page || 1;
    const limit = query.limit || 10;
    
    const result = await this.courseTestRequirementService.findAll(query);
    
    return {
      total: result.count,
      page,
      limit,
      // courseTestRequirements: result.rows as unknown as CourseTestRequirementDto[]
    };
  }

  @Get('course/:courseId')
  async findByCourseId(
    @Param('courseId', ParseIntPipe) courseId: number
  ): Promise<CourseTestRequirementDto[]> {
    const requirements = await this.courseTestRequirementService.findByCourseId(courseId);
    return requirements as unknown as CourseTestRequirementDto[];
  }

  @Get(':id')
  async findOne(@Param('id', ParseIntPipe) id: number): Promise<CourseTestRequirementDto> {
    return this.courseTestRequirementService.findOne(id) as unknown as CourseTestRequirementDto;
  }

  @Put(':id')
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateCourseTestRequirementRequestDto
  ): Promise<CourseTestRequirementDto> {
    const startTime = Date.now();
    try {
      const result = await this.courseTestRequirementService.update(id, updateDto);
      this.appService.trackProcessingDuration('course_test_requirement_update_api_success', (Date.now() - startTime) / 1000);
      
      // Audit logging
      this.auditService.logAction({
        action: 'UPDATE_COURSE_TEST_REQUIREMENT',
        entityType: 'CourseTestRequirement',
        entityId: id.toString(),
        details: updateDto
      });
      
      return result as unknown as CourseTestRequirementDto;
    } catch (error) {
      this.appService.trackProcessingDuration('course_test_requirement_update_api_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }

  @Delete(':id')
  async remove(@Param('id', ParseIntPipe) id: number): Promise<{ id: number; success: boolean }> {
    const startTime = Date.now();
    try {
      await this.courseTestRequirementService.remove(id);
      this.appService.trackProcessingDuration('course_test_requirement_delete_api_success', (Date.now() - startTime) / 1000);
      
      // Audit logging
      this.auditService.logAction({
        action: 'DELETE_COURSE_TEST_REQUIREMENT',
        entityType: 'CourseTestRequirement',
        entityId: id.toString(),
        details: { id }
      });
      
      return { id, success: true };
    } catch (error) {
      this.appService.trackProcessingDuration('course_test_requirement_delete_api_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }
}