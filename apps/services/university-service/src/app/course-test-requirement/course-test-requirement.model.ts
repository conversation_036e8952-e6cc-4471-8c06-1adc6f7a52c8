import { Table, Column, DataType, ForeignKey, BelongsTo } from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
import { Course } from '../course/course.model';

@Table({ tableName: 'course_test_requirements' })
export class CourseTestRequirement extends BaseModel {
  @ForeignKey(() => Course)
  @Column(DataType.BIGINT)
  courseId!: number;

  @Column(DataType.STRING)
  testName!: string;

  @Column(DataType.FLOAT)
  scoreOverall!: number;

  @Column(DataType.FLOAT)
  reading!: number;

  @Column(DataType.FLOAT)
  writing!: number;

  @Column(DataType.FLOAT)
  listening!: number;

  @Column(DataType.FLOAT)
  speaking!: number;

  @BelongsTo(() => Course)
  course!: Course;
}