import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { CourseTestRequirement } from './course-test-requirement.model';
import { CourseTestRequirementService } from './course-test-requirement.service';
import { CourseTestRequirementController } from './course-test-requirement.controller';
import { AuditClientService } from '../audit.service';
import { Course } from '../course/course.model';

@Module({
  imports: [
    SequelizeModule.forFeature([CourseTestRequirement, Course]),
  ],
  controllers: [CourseTestRequirementController],
  providers: [CourseTestRequirementService, AuditClientService],
  exports: [CourseTestRequirementService],
})
export class CourseTestRequirementModule {}