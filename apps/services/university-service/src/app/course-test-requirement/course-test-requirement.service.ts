import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { CourseTestRequirement } from './course-test-requirement.model';
import { BaseRepository } from '@apply-goal-backend/database';
import { 
  CreateCourseTestRequirementRequestDto, 
  UpdateCourseTestRequirementRequestDto,
  GetAllCourseTestRequirementsRequestDto
} from '@apply-goal-backend/dto';
import { AppService } from '../app.service';
import { Course } from '../course/course.model';

@Injectable()
export class CourseTestRequirementService {
  private courseTestRequirementRepository: BaseRepository<CourseTestRequirement>;

  constructor(
    @InjectModel(CourseTestRequirement)
    private courseTestRequirementModel: typeof CourseTestRequirement,
    private readonly appService: AppService
  ) {
    this.courseTestRequirementRepository = new BaseRepository<CourseTestRequirement>(courseTestRequirementModel);
  }

  async create(createDto: CreateCourseTestRequirementRequestDto): Promise<CourseTestRequirement> {
    const startTime = Date.now();
    try {
      // Check if test requirement with same name already exists for this course
      const existingRequirement = await this.courseTestRequirementRepository.findOne({
        where: {
          courseId: createDto.courseId,
          testName: createDto.testName
        }
      });

      if (existingRequirement) {
        this.appService.trackProcessingDuration('course_test_requirement_create_conflict', (Date.now() - startTime) / 1000);
        throw new ConflictException(`Test requirement with name "${createDto.testName}" already exists for this course`);
      }

      const requirement = await this.courseTestRequirementRepository.create(createDto);
      this.appService.trackProcessingDuration('course_test_requirement_create_success', (Date.now() - startTime) / 1000);
      return requirement;
    } catch (error) {
      this.appService.trackProcessingDuration('course_test_requirement_create_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }

  async findAll(
    queryParams: GetAllCourseTestRequirementsRequestDto
  ): Promise<{ rows: CourseTestRequirement[]; count: number }> {
    const { courseId, page = 1, limit = 10 } = queryParams;
    const where: any = {};
    
    if (courseId) {
      where.courseId = courseId;
    }
    
    return this.courseTestRequirementRepository.findAndCountAll({
      where,
      include: [
        {
          model: Course,
          attributes: ['id', 'title']
        }
      ],
      offset: (page - 1) * limit,
      limit,
      order: [['createdAt', 'DESC']],
    });
  }

  async findOne(id: number): Promise<CourseTestRequirement> {
    const requirement = await this.courseTestRequirementRepository.findOne({
      where: { id },
      include: [
        {
          model: Course,
          attributes: ['id', 'title']
        }
      ]
    });
    
    if (!requirement) {
      throw new NotFoundException(`Course test requirement with ID ${id} not found`);
    }
    
    return requirement;
  }

  async update(id: number, updateDto: UpdateCourseTestRequirementRequestDto): Promise<CourseTestRequirement> {
    const startTime = Date.now();
    try {
      const requirement = await this.findOne(id);
      
      // If updating test name, check for uniqueness
      if (updateDto.testName && updateDto.testName !== requirement.testName) {
        const existingRequirement = await this.courseTestRequirementRepository.findOne({
          where: {
            courseId: requirement.courseId,
            testName: updateDto.testName,
            // id: { [Op.ne]: id } // Exclude current requirement
          }
        });

        if (existingRequirement) {
          this.appService.trackProcessingDuration('course_test_requirement_update_conflict', (Date.now() - startTime) / 1000);
          throw new ConflictException(`Test requirement with name "${updateDto.testName}" already exists for this course`);
        }
      }

      await requirement.update(updateDto);
      this.appService.trackProcessingDuration('course_test_requirement_update_success', (Date.now() - startTime) / 1000);
      return this.findOne(id); // Fetch again to get the relations
    } catch (error) {
      this.appService.trackProcessingDuration('course_test_requirement_update_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }

  async remove(id: number): Promise<void> {
    const startTime = Date.now();
    try {
      const deleted = await this.courseTestRequirementRepository.delete(id);
      
      if (deleted === 0) {
        this.appService.trackProcessingDuration('course_test_requirement_delete_not_found', (Date.now() - startTime) / 1000);
        throw new NotFoundException(`Course test requirement with ID ${id} not found`);
      }
      
      this.appService.trackProcessingDuration('course_test_requirement_delete_success', (Date.now() - startTime) / 1000);
    } catch (error) {
      this.appService.trackProcessingDuration('course_test_requirement_delete_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }

  async findByCourseId(courseId: number): Promise<CourseTestRequirement[]> {
    return this.courseTestRequirementRepository.findAll({
      where: { courseId },
      order: [['testName', 'ASC']]
    });
  }
}