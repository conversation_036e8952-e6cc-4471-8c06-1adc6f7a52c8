import { Controller, Get, Post, Body, Param, Put, Delete, Query, ParseIntPipe } from '@nestjs/common';
import { PrimaryContactService } from './primary-contact.service';
import { 
  CreatePrimaryContactRequestDto, 
  CreatePrimaryContactResponseDto, 
  UpdatePrimaryContactRequestDto, 
  UpdatePrimaryContactResponseDto,
  GetPrimaryContactRequestDto,
  GetPrimaryContactResponseDto,
  DeletePrimaryContactRequestDto,
  DeletePrimaryContactResponseDto
} from '@apply-goal-backend/dto';
import { AppService } from '../app.service';
import { AuditClientService } from '../audit.service';

@Controller('primary-contacts')
export class PrimaryContactController {
  constructor(
    private readonly primaryContactService: PrimaryContactService,
    private readonly appService: AppService,
    private readonly auditService: AuditClientService
  ) {}

  @Post()
  async create(@Body() createPrimaryContactDto: CreatePrimaryContactRequestDto): Promise<CreatePrimaryContactResponseDto> {
    const startTime = Date.now();
    try {
      const result = await this.primaryContactService.create(createPrimaryContactDto);
      this.appService.trackProcessingDuration('primary_contact_create_api_success', (Date.now() - startTime) / 1000);
      
      // Audit logging
      this.auditService.logAction({
        action: 'CREATE_PRIMARY_CONTACT',
        entityType: 'PrimaryContact',
        entityId: result.id.toString(),
        details: createPrimaryContactDto
      });
      
      return result as CreatePrimaryContactResponseDto;
    } catch (error) {
      this.appService.trackProcessingDuration('primary_contact_create_api_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }

  @Get('university/:universityId')
  async findByUniversityId(@Param('universityId', ParseIntPipe) universityId: number): Promise<GetPrimaryContactResponseDto> {
    return this.primaryContactService.findByUniversityId(universityId) as unknown as GetPrimaryContactResponseDto;
  }

  @Get(':id')
  async findOne(@Param('id', ParseIntPipe) id: number): Promise<GetPrimaryContactResponseDto> {
    return this.primaryContactService.findOne(id) as unknown as GetPrimaryContactResponseDto;
  }

  @Put(':id')
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updatePrimaryContactDto: UpdatePrimaryContactRequestDto
  ): Promise<UpdatePrimaryContactResponseDto> {
    const startTime = Date.now();
    try {
      const result = await this.primaryContactService.update(id, updatePrimaryContactDto);
      this.appService.trackProcessingDuration('primary_contact_update_api_success', (Date.now() - startTime) / 1000);
      
      // Audit logging
      this.auditService.logAction({
        action: 'UPDATE_PRIMARY_CONTACT',
        entityType: 'PrimaryContact',
        entityId: id.toString(),
        details: updatePrimaryContactDto
      });
      
      return result as UpdatePrimaryContactResponseDto;
    } catch (error) {
      this.appService.trackProcessingDuration('primary_contact_update_api_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }

  @Delete(':id')
  async remove(@Param('id', ParseIntPipe) id: number): Promise<DeletePrimaryContactResponseDto> {
    const startTime = Date.now();
    try {
      await this.primaryContactService.remove(id);
      this.appService.trackProcessingDuration('primary_contact_delete_api_success', (Date.now() - startTime) / 1000);
      
      // Audit logging
      this.auditService.logAction({
        action: 'DELETE_PRIMARY_CONTACT',
        entityType: 'PrimaryContact',
        entityId: id.toString(),
        details: { id }
      });
      
      return { id, success: true };
    } catch (error) {
      this.appService.trackProcessingDuration('primary_contact_delete_api_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }
}