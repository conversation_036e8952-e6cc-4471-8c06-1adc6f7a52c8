import { Modu<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { PrimaryContact } from './primary-contact.model';
import { PrimaryContactService } from './primary-contact.service';
import { PrimaryContactController } from './primary-contact.controller';

@Module({
  imports: [
    SequelizeModule.forFeature([PrimaryContact]),
  ],
  controllers: [PrimaryContactController],
  providers: [PrimaryContactService],
  exports: [PrimaryContactService],
})
export class PrimaryContactModule {}