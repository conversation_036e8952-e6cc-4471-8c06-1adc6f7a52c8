import { Table, Column, DataType, ForeignKey, BelongsTo } from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
import { University } from '../university/university.model';

@Table({ tableName: 'primary_contacts' })
export class PrimaryContact extends BaseModel {
  @ForeignKey(() => University)
  @Column(DataType.BIGINT)
  universityId!: number;

  @Column(DataType.STRING)
  phoneNumber!: string;

  @Column(DataType.STRING)
  email!: string;

  @BelongsTo(() => University)
  university!: University;
}