import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { PrimaryContact } from './primary-contact.model';
import { BaseRepository } from '@apply-goal-backend/database';
import { CreatePrimaryContactRequestDto, UpdatePrimaryContactRequestDto } from '@apply-goal-backend/dto';
import { AppService } from '../app.service';

@Injectable()
export class PrimaryContactService {
  private primaryContactRepository: BaseRepository<PrimaryContact>;

  constructor(
    @InjectModel(PrimaryContact)
    private primaryContactModel: typeof PrimaryContact,
    private readonly appService: AppService
  ) {
    this.primaryContactRepository = new BaseRepository<PrimaryContact>(primaryContactModel);
  }

  async create(createPrimaryContactDto: CreatePrimaryContactRequestDto): Promise<PrimaryContact> {
    const startTime = Date.now();
    try {
      // Check if a primary contact already exists for this university
      const existingContact = await this.primaryContactRepository.findOne({
        where: { universityId: createPrimaryContactDto.universityId }
      });

      // If exists, update it instead of creating a new one
      if (existingContact) {
        await existingContact.update(createPrimaryContactDto);
        this.appService.trackProcessingDuration('primary_contact_update_success', (Date.now() - startTime) / 1000);
        return existingContact;
      }

      // Create new primary contact
      const primaryContact = await this.primaryContactRepository.create(createPrimaryContactDto);
      this.appService.trackProcessingDuration('primary_contact_create_success', (Date.now() - startTime) / 1000);
      return primaryContact;
    } catch (error) {
      this.appService.trackProcessingDuration('primary_contact_create_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }

  async findByUniversityId(universityId: number): Promise<PrimaryContact> {
    const primaryContact = await this.primaryContactRepository.findOne({
      where: { universityId }
    });
    
    if (!primaryContact) {
      throw new NotFoundException(`Primary contact for university with ID ${universityId} not found`);
    }
    
    return primaryContact;
  }

  async findOne(id: number): Promise<PrimaryContact> {
    const primaryContact = await this.primaryContactRepository.findById(id);
    
    if (!primaryContact) {
      throw new NotFoundException(`Primary contact with ID ${id} not found`);
    }
    
    return primaryContact;
  }

  async update(id: number, updatePrimaryContactDto: UpdatePrimaryContactRequestDto): Promise<PrimaryContact> {
    const startTime = Date.now();
    try {
      const primaryContact = await this.findOne(id);
      await primaryContact.update(updatePrimaryContactDto);
      this.appService.trackProcessingDuration('primary_contact_update_success', (Date.now() - startTime) / 1000);
      return primaryContact;
    } catch (error) {
      this.appService.trackProcessingDuration('primary_contact_update_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }

  async remove(id: number): Promise<void> {
    const deleted = await this.primaryContactRepository.delete(id);
    
    if (deleted === 0) {
      throw new NotFoundException(`Primary contact with ID ${id} not found`);
    }
  }

  async removeByUniversityId(universityId: number): Promise<void> {
    // await this.primaryContactRepository.destroy({
    //   where: { universityId }
    // });
  }
}