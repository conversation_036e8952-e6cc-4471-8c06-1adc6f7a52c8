import { <PERSON>du<PERSON>, MiddlewareConsumer, NestModule } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { getDatabaseConfig } from '@apply-goal-backend/database';
import { AppController } from './app.controller';
import { MetricsController } from './metrics.controller';
import { MetricsMiddleware } from './middleware/metrics.middleware';
import { CountryModule } from './country/country.module';
import { LanguageModule } from './language/language.module';
import { UniversityModule } from './university/university.module';
import { ApplicationStepModule } from './application-step/application-step.module';
import { CampusModule } from './campus/campus.module';
import { PrimaryContactModule } from './primary-contact/primary-contact.module';
import { StudyFieldModule } from './study-field/study-field.module';
import { CourseModule } from './course/course.module';
import { CourseTestRequirementModule } from './course-test-requirement/course-test-requirement.module';
import { CoreModule } from './core.module';
import { CourseStudyFieldModule } from './course-study-field/course-study-field.module';
import { APP_FILTER } from '@nestjs/core';
import { GrpcExceptionFilter } from './filters/grpc-exception.filter';

@Module({
  imports: [
    CoreModule,
    SequelizeModule.forRoot({
      ...getDatabaseConfig('university'),
      autoLoadModels: true,
      synchronize: true,
    }),
    CountryModule,
    LanguageModule,
    ApplicationStepModule,
    UniversityModule,
    CampusModule,
    PrimaryContactModule,
    StudyFieldModule,
    CourseModule,
    CourseTestRequirementModule,
    CourseStudyFieldModule,
  ],
  controllers: [AppController, MetricsController],

})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(MetricsMiddleware).exclude('/metrics').forRoutes('*');
  }
}
