import { Table, Column, DataType, Has<PERSON>any, HasOne } from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
import { Campus } from '../campus/campus.model';
import { PrimaryContact } from '../primary-contact/primary-contact.model';
import { Course } from '../course/course.model';

@Table({ tableName: 'universities' })
export class University extends BaseModel {
  @Column(DataType.BIGINT)
  userId!: number;

  @Column(DataType.TEXT)
  name!: string;

  @Column(DataType.TEXT)
  about!: string;

  @Column(DataType.STRING)
  type!: string;

  @Column(DataType.STRING)
  website!: string;

  @Column(DataType.DATE)
  foundedOn!: Date;

  @Column(DataType.STRING)
  dliNumber!: string;

  @Column(DataType.STRING)
  address!: string;

  @Column(DataType.STRING)
  country!: string;

  @Column(DataType.STRING)
  state!: string;

  @Column(DataType.STRING)
  city!: string;

  @Column(DataType.STRING)
  postalCode!: string;

  @HasMany(() => Campus)
  campuses!: Campus[];

  @HasOne(() => PrimaryContact)
  primaryContact!: PrimaryContact;

  @HasMany(() => Course)
  courses!: Course[];
}
