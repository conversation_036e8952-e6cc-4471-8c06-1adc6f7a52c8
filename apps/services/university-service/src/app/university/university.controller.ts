import { Controller, Get, Post, Body, Param, Put, Delete, Query, ParseIntPipe } from '@nestjs/common';
import { UniversityService } from './university.service';
import { CreateUniversityDto, UniversityFullPayloadDto, UpdateUniversityDto } from '@apply-goal-backend/dto';
import { AppService } from '../app.service';
import { AuditClientService } from '../audit.service';

@Controller('universities')
export class UniversityController {
  constructor(
    private readonly universityService: UniversityService,
    private readonly appService: AppService,
    private readonly auditService: AuditClientService
  ) {}

  @Post()
  async create(@Body() universityPayloadDto: UniversityFullPayloadDto) {
    const startTime = Date.now();
    try {
      console.log("UniversityPayload",universityPayloadDto);
      const result = await this.universityService.create(universityPayloadDto);
      // this.appService.trackProcessingDuration('university_create_api_success', (Date.now() - startTime) / 1000);
      // console.log("results",result);
      
      // Audit logging
      // this.auditService.logAction({
      //   action: 'CREATE_UNIVERSITY',
      //   entityType: 'University',
      //   entityId: result.id.toString(),
      //   details: universityPayloadDto
      // });
      
      return result;
    } catch (error) {
      this.appService.trackProcessingDuration('university_create_api_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }

  @Get()
  async findAll(@Query('page') page = 1, @Query('limit') limit = 10) {
    const startTime = Date.now();
    try {
      const result = await this.universityService.findAll(page, limit);
      this.appService.trackProcessingDuration('university_find_all_api_success', (Date.now() - startTime) / 1000);
      return result;
    } catch (error) {
      this.appService.trackProcessingDuration('university_find_all_api_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }

  @Get(':id')
  async findOne(@Param('id', ParseIntPipe) id: number) {
    const startTime = Date.now();
    try {
      const result = await this.universityService.findOne(id);
      this.appService.trackProcessingDuration('university_find_one_api_success', (Date.now() - startTime) / 1000);
      return result;
    } catch (error) {
      this.appService.trackProcessingDuration('university_find_one_api_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }

  @Put(':id')
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateUniversityDto: UpdateUniversityDto
  ) {
    const startTime = Date.now();
    try {
      const result = await this.universityService.update(id, updateUniversityDto);
      this.appService.trackProcessingDuration('university_update_api_success', (Date.now() - startTime) / 1000);
      
      // Audit logging
      this.auditService.logAction({
        action: 'UPDATE_UNIVERSITY',
        entityType: 'University',
        entityId: id.toString(),
        details: updateUniversityDto
      });
      
      return result;
    } catch (error) {
      this.appService.trackProcessingDuration('university_update_api_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }

  @Delete(':id')
  async remove(@Param('id', ParseIntPipe) id: number) {
    const startTime = Date.now();
    try {
      await this.universityService.remove(id);
      this.appService.trackProcessingDuration('university_delete_api_success', (Date.now() - startTime) / 1000);
      
      // Audit logging
      this.auditService.logAction({
        action: 'DELETE_UNIVERSITY',
        entityType: 'University',
        entityId: id.toString(),
        details: { id }
      });
      
      return { success: true };
    } catch (error) {
      this.appService.trackProcessingDuration('university_delete_api_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }
}