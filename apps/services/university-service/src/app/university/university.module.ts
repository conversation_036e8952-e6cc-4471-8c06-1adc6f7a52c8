
import { <PERSON>du<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { University } from './university.model';
import { UniversityService } from './university.service';
import { UniversityController } from './university.controller';
import { AuditClientService } from '../audit.service';
import { ApplicationStepModule } from '../application-step/application-step.module';
import { CampusModule } from '../campus/campus.module';
import { PrimaryContactModule } from '../primary-contact/primary-contact.module';

@Module({
  imports: [
    SequelizeModule.forFeature([University]),
    ApplicationStepModule,
    CampusModule,
    PrimaryContactModule,
  ],
  controllers: [UniversityController],
  providers: [UniversityService, AuditClientService],
  exports: [UniversityService],
})
export class UniversityModule {}

