import { Modu<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { Language } from './language.model';
import { LanguageService } from './language.service';
import { LanguageController } from './language.controller';
import { AuditClientService } from '../audit.service';

@Module({
  imports: [
    SequelizeModule.forFeature([Language]),
  ],
  controllers: [LanguageController],
  providers: [LanguageService, AuditClientService],
  exports: [LanguageService],
})
export class LanguageModule {}