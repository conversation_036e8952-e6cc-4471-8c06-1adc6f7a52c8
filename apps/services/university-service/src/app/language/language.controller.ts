import { Controller, Get, Post, Body, Param, Put, Delete, Query, ParseIntPipe } from '@nestjs/common';
import { LanguageService } from './language.service';
import { CreateLanguageRequestDto, UpdateLanguageRequestDto } from '@apply-goal-backend/dto';
import { AppService } from '../app.service';
import { AuditClientService } from '../audit.service';

@Controller('languages')
export class LanguageController {
  constructor(
    private readonly languageService: LanguageService,
    private readonly appService: AppService,
    private readonly auditService: AuditClientService
  ) {}

  @Post()
  async create(@Body() createLanguageDto: CreateLanguageRequestDto) {
    const startTime = Date.now();
    try {
      const result = await this.languageService.create(createLanguageDto);
      this.appService.trackProcessingDuration('language_create_api_success', (Date.now() - startTime) / 1000);
      
      // Audit logging
      this.auditService.logAction({
        action: 'CREATE_LANGUAGE',
        entityType: 'Language',
        entityId: result.id.toString(),
        details: createLanguageDto
      });
      
      return result;
    } catch (error) {
      this.appService.trackProcessingDuration('language_create_api_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }

  @Get()
  async findAll(@Query('page') page = 1, @Query('limit') limit = 10) {
    const startTime = Date.now();
    try {
      const result = await this.languageService.findAll(page, limit);
      this.appService.trackProcessingDuration('language_find_all_api_success', (Date.now() - startTime) / 1000);
      return result;
    } catch (error) {
      this.appService.trackProcessingDuration('language_find_all_api_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }

  @Get(':id')
  async findOne(@Param('id', ParseIntPipe) id: number) {
    const startTime = Date.now();
    try {
      const result = await this.languageService.findOne(id);
      this.appService.trackProcessingDuration('language_find_one_api_success', (Date.now() - startTime) / 1000);
      return result;
    } catch (error) {
      this.appService.trackProcessingDuration('language_find_one_api_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }

  @Put(':id')
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateLanguageDto: UpdateLanguageRequestDto
  ) {
    const startTime = Date.now();
    try {
      const result = await this.languageService.update(id, updateLanguageDto);
      this.appService.trackProcessingDuration('language_update_api_success', (Date.now() - startTime) / 1000);
      
      // Audit logging
      this.auditService.logAction({
        action: 'UPDATE_LANGUAGE',
        entityType: 'Language',
        entityId: id.toString(),
        details: updateLanguageDto
      });
      
      return result;
    } catch (error) {
      this.appService.trackProcessingDuration('language_update_api_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }

  @Delete(':id')
  async remove(@Param('id', ParseIntPipe) id: number) {
    const startTime = Date.now();
    try {
      await this.languageService.remove(id);
      this.appService.trackProcessingDuration('language_delete_api_success', (Date.now() - startTime) / 1000);
      
      // Audit logging
      this.auditService.logAction({
        action: 'DELETE_LANGUAGE',
        entityType: 'Language',
        entityId: id.toString(),
        details: { id }
      });
      
      return { success: true };
    } catch (error) {
      this.appService.trackProcessingDuration('language_delete_api_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }
}
