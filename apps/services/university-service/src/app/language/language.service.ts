import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Language } from './language.model';
import { BaseRepository } from '@apply-goal-backend/database';
import { CreateLanguageRequestDto, UpdateLanguageRequestDto } from '@apply-goal-backend/dto';
import { Op } from 'sequelize';
import { AppService } from '../app.service';

@Injectable()
export class LanguageService {
  private languageRepository: BaseRepository<Language>;

  constructor(
    @InjectModel(Language)
    private languageModel: typeof Language,
    private readonly appService: AppService
  ) {
    this.languageRepository = new BaseRepository<Language>(languageModel);
  }

  async create(createLanguageDto: CreateLanguageRequestDto): Promise<Language> {
    const startTime = Date.now();
    try {
      // Check if language with same name already exists
      const existingLanguage = await this.languageRepository.findOne({
        where: {
          name: createLanguageDto.name
        }
      });

      if (existingLanguage) {
        this.appService.trackProcessingDuration('language_create_conflict', (Date.now() - startTime) / 1000);
        throw new ConflictException(`Language with name "${createLanguageDto.name}" already exists`);
      }

      return this.languageRepository.create(createLanguageDto);
    } catch (error) {
      this.appService.trackProcessingDuration('language_create_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }

  async findAll(page: number = 1, limit: number = 10): Promise<{ rows: Language[]; count: number }> {
    return this.languageRepository.findAndCountAll({
      offset: (page - 1) * limit,
      limit,
      order: [['createdAt', 'DESC']],
    });
  }

  async findOne(id: number): Promise<Language> {
    const language = await this.languageRepository.findById(id);
    if (!language) {
      throw new NotFoundException(`Language with ID ${id} not found`);
    }
    return language;
  }

  async update(id: number, updateLanguageDto: UpdateLanguageRequestDto): Promise<Language> {
    // If updating name, check for uniqueness
    if (updateLanguageDto.name) {
      const existingLanguage = await this.languageRepository.findOne({
        where: {
          name: updateLanguageDto.name,
          id: { [Op.ne]: id } // Exclude current language
        }
      });

      if (existingLanguage) {
        throw new ConflictException(`Language with name "${updateLanguageDto.name}" already exists`);
      }
    }

    const language = await this.findOne(id);
    await language.update(updateLanguageDto);
    return language;
  }

  async remove(id: number): Promise<void> {
    const deleted = await this.languageRepository.delete(id);
    
    if (deleted === 0) {
      throw new NotFoundException(`Language with ID ${id} not found`);
    }
  }
}