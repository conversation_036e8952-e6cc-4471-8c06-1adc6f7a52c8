import { Table, Column, DataType, ForeignKey, BelongsTo } from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
import { University } from '../university/university.model';

@Table({ tableName: 'files' })
export class File extends BaseModel {
  @ForeignKey(() => University)
  @Column(DataType.BIGINT)
  universityId!: number;

  @Column(DataType.STRING)
  fileName!: string;

  @Column(DataType.STRING)
  fileType!: string;

  @Column(DataType.STRING)
  fileUrl!: string;

  @Column({ type: DataType.BOOLEAN, defaultValue: true })
  isActive!: boolean;

  @BelongsTo(() => University)
  university!: University;
}