import { Table, Column, <PERSON>Type, <PERSON><PERSON>ey, BelongsTo, HasMany } from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
import { University } from '../university/university.model';
import { CourseTestRequirement } from '../course-test-requirement/course-test-requirement.model';
// import { CourseSubject } from '../course-subject/course-subject.model';
// import { CourseTestRequirement } from '../course-test-requirement/course-test-requirement.model';
// import { CourseIntake } from '../course-intake/course-intake.model';
// import { CourseLectureLanguage } from '../course-lecture-language/course-lecture-language.model';
// import { CourseStudyField } from '../course-study-field/course-study-field.model';

@Table({ tableName: 'courses' })
export class Course extends BaseModel {
  @ForeignKey(() => University)
  @Column(DataType.BIGINT)
  universityId!: number;

  @Column(DataType.STRING)
  title!: string;

  @Column(DataType.STRING)
  duration!: string;

  @Column(DataType.FLOAT)
  fieldOfStudy!: number;

  @Column(DataType.DECIMAL(10, 2))
  applicationFee!: number;

  @Column(DataType.DECIMAL(10, 2))
  tuitionFee!: number;

  @Column(DataType.STRING)
  lastAcademic!: string;

  @Column(DataType.FLOAT)
  minGpa!: number;

  @Column(DataType.INTEGER)
  courseRank!: number;

  @Column(DataType.FLOAT)
  acceptedRate!: number;

  @Column(DataType.TEXT)
  requirements!: string;

  @Column({ type: DataType.BOOLEAN, defaultValue: true })
  isActive!: boolean;

  @BelongsTo(() => University)
  university!: University;

  @HasMany(() => CourseTestRequirement)
  testRequirements!: CourseTestRequirement[];

  // @HasMany(() => CourseSubject)
  // subjects!: CourseSubject[];

  // @HasMany(() => CourseTestRequirement)
  // testRequirements!: CourseTestRequirement[];

  // @HasMany(() => CourseIntake)
  // intakes!: CourseIntake[];

  // @HasMany(() => CourseLectureLanguage)
  // lectureLanguages!: CourseLectureLanguage[];

  // @HasMany(() => CourseStudyField)
  // studyFields!: CourseStudyField[];
}