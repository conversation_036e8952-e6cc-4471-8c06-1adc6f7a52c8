import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Course } from './course.model';
import { BaseRepository } from '@apply-goal-backend/database';
import { CreateCourseRequestDto, UpdateCourseRequestDto } from '@apply-goal-backend/dto';
import { Op } from 'sequelize';
import { AppService } from '../app.service';
import { University } from '../university/university.model';
import { DatabaseUtil } from '@apply-goal-backend/utils';

@Injectable()
export class CourseService {
  private courseRepository: BaseRepository<Course>;

  constructor(
    @InjectModel(Course)
    private courseModel: typeof Course,
    @InjectModel(University)
    private universityModel: typeof University,
    private readonly appService: AppService
  ) {
    this.courseRepository = new BaseRepository<Course>(courseModel);
  }

  async create(createCourseDto: CreateCourseRequestDto): Promise<Course> {
    const startTime = Date.now();
    try {
      // Check if university exists
      const universityExists = await DatabaseUtil.entityExists(
        this.universityModel, 
        createCourseDto.universityId
      );
      
      if (!universityExists) {
        this.appService.trackProcessingDuration('course_create_university_not_found', (Date.now() - startTime) / 1000);
        throw new NotFoundException(`University with ID ${createCourseDto.universityId} not found`);
      }

      // Check if course with same title already exists for this university
      const existingCourse = await this.courseRepository.findOne({
        where: {
          universityId: createCourseDto.universityId,
          title: createCourseDto.title
        }
      });

      if (existingCourse) {
        this.appService.trackProcessingDuration('course_create_conflict', (Date.now() - startTime) / 1000);
        throw new ConflictException(`Course with title "${createCourseDto.title}" already exists for this university`);
      }

      const course = await this.courseRepository.create(createCourseDto);
      this.appService.trackProcessingDuration('course_create_success', (Date.now() - startTime) / 1000);
      return course;
    } catch (error) {
      this.appService.trackProcessingDuration('course_create_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }

  async findAll(
    page: number = 1, 
    limit: number = 10, 
    universityId?: number
  ): Promise<{ rows: Course[]; count: number }> {
    const where: any = {};
    
    if (universityId) {
      where.universityId = universityId;
    }
    
    return this.courseRepository.findAndCountAll({
      where,
      include: [
        {
          model: University,
          attributes: ['id', 'name', 'logo']
        }
      ],
      offset: (page - 1) * limit,
      limit,
      order: [['createdAt', 'DESC']],
    });
  }

  async findOne(id: number): Promise<Course> {
    const course = await this.courseRepository.findOne({
      where: { id },
      include: [
        {
          model: University,
          attributes: ['id', 'name', 'logo']
        }
      ]
    });
    
    if (!course) {
      throw new NotFoundException(`Course with ID ${id} not found`);
    }
    
    return course;
  }

  async update(id: number, updateCourseDto: UpdateCourseRequestDto): Promise<Course> {
    const startTime = Date.now();
    try {
      // If updating title, check for uniqueness
      if (updateCourseDto.title) {
        const course = await this.findOne(id);
        const existingCourse = await this.courseRepository.findOne({
          where: {
            universityId: course.universityId,
            title: updateCourseDto.title,
            id: { [Op.ne]: id } // Exclude current course
          }
        });

        if (existingCourse) {
          this.appService.trackProcessingDuration('course_update_conflict', (Date.now() - startTime) / 1000);
          throw new ConflictException(`Course with title "${updateCourseDto.title}" already exists for this university`);
        }
      }

      const course = await this.findOne(id);
      await course.update(updateCourseDto);
      this.appService.trackProcessingDuration('course_update_success', (Date.now() - startTime) / 1000);
      return this.findOne(id); // Fetch again to get the relations
    } catch (error) {
      this.appService.trackProcessingDuration('course_update_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }

  async remove(id: number): Promise<void> {
    const startTime = Date.now();
    try {
      const deleted = await this.courseRepository.delete(id);
      
      if (deleted === 0) {
        this.appService.trackProcessingDuration('course_delete_not_found', (Date.now() - startTime) / 1000);
        throw new NotFoundException(`Course with ID ${id} not found`);
      }
      
      this.appService.trackProcessingDuration('course_delete_success', (Date.now() - startTime) / 1000);
    } catch (error) {
      this.appService.trackProcessingDuration('course_delete_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }

  async findByUniversityId(universityId: number): Promise<Course[]> {
    return this.courseRepository.findAll({
      where: { universityId },
      order: [['title', 'ASC']]
    });
  }
}
