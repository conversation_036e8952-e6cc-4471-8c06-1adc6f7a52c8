import { Modu<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { Course } from './course.model';
import { CourseService } from './course.service';
import { CourseController } from './course.controller';
import { AuditClientService } from '../audit.service';
import { University } from '../university/university.model';

@Module({
  imports: [
    SequelizeModule.forFeature([Course, University]),
  ],
  controllers: [CourseController],
  providers: [CourseService, AuditClientService],
  exports: [CourseService],
})
export class CourseModule {}