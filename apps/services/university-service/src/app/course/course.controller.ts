import { Controller, Get, Post, Body, Param, Put, Delete, Query, ParseIntPipe } from '@nestjs/common';
import { CourseService } from './course.service';
import { 
  CreateCourseRequestDto, 
  CreateCourseResponseDto,
  UpdateCourseRequestDto, 
  UpdateCourseResponseDto,
  GetCourseResponseDto,
  GetAllCoursesRequestDto,
  GetAllCoursesResponseDto,
  DeleteCourseResponseDto,
  CourseDto
} from '@apply-goal-backend/dto';
import { AppService } from '../app.service';
import { AuditClientService } from '../audit.service';

@Controller('courses')
export class CourseController {
  constructor(
    private readonly courseService: CourseService,
    private readonly appService: AppService,
    private readonly auditService: AuditClientService
  ) {}

  @Post()
  async create(@Body() createCourseDto: CreateCourseRequestDto): Promise<CreateCourseResponseDto> {
    const startTime = Date.now();
    try {
      console.log("createCourseDto",createCourseDto);
      const result = await this.courseService.create(createCourseDto);
      this.appService.trackProcessingDuration('course_create_api_success', (Date.now() - startTime) / 1000);
      
      // Audit logging
      this.auditService.logAction({
        action: 'CREATE_COURSE',
        entityType: 'Course',
        entityId: result.id.toString(),
        details: createCourseDto
      });
      
      return result as unknown as CreateCourseResponseDto;
    } catch (error) {
      this.appService.trackProcessingDuration('course_create_api_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }

  @Get()
  async findAll(
    @Query() query: GetAllCoursesRequestDto
  ): Promise<GetAllCoursesResponseDto> {
    const page = query.page || 1;
    const limit = query.limit || 10;
    const universityId = query.universityId;
    
    const result = await this.courseService.findAll(page, limit, universityId);
    
    return {
      total: result.count,
      page,
      limit,
      courses: result.rows as unknown as CourseDto[]
    };
  }

  @Get('university/:universityId')
  async findByUniversityId(
    @Param('universityId', ParseIntPipe) universityId: number
  ): Promise<CourseDto[]> {
    const courses = await this.courseService.findByUniversityId(universityId);
    return courses as unknown as CourseDto[];
  }

  @Get(':id')
  async findOne(@Param('id', ParseIntPipe) id: number): Promise<GetCourseResponseDto> {
    return this.courseService.findOne(id) as unknown as GetCourseResponseDto;
  }

  @Put(':id')
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateCourseDto: UpdateCourseRequestDto
  ): Promise<UpdateCourseResponseDto> {
    const startTime = Date.now();
    try {
      const result = await this.courseService.update(id, updateCourseDto);
      this.appService.trackProcessingDuration('course_update_api_success', (Date.now() - startTime) / 1000);
      
      // Audit logging
      this.auditService.logAction({
        action: 'UPDATE_COURSE',
        entityType: 'Course',
        entityId: id.toString(),
        details: updateCourseDto
      });
      
      return result as unknown as UpdateCourseResponseDto;
    } catch (error) {
      this.appService.trackProcessingDuration('course_update_api_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }

  @Delete(':id')
  async remove(@Param('id', ParseIntPipe) id: number): Promise<DeleteCourseResponseDto> {
    const startTime = Date.now();
    try {
      await this.courseService.remove(id);
      this.appService.trackProcessingDuration('course_delete_api_success', (Date.now() - startTime) / 1000);
      
      // Audit logging
      this.auditService.logAction({
        action: 'DELETE_COURSE',
        entityType: 'Course',
        entityId: id.toString(),
        details: { id }
      });
      
      return { id, success: true };
    } catch (error) {
      this.appService.trackProcessingDuration('course_delete_api_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }
}