import { Test, TestingModule } from '@nestjs/testing';
import { ValidationPipe } from '@nestjs/common';
import { CountryController } from './country.controller';
import { CountryService } from './country.service';
import { CreateCountryDto } from '@apply-goal-backend/dto';

describe('CountryController', () => {
  let controller: CountryController;
  let service: CountryService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CountryController],
      providers: [
        {
          provide: CountryService,
          useValue: {
            create: jest.fn(),
            findAll: jest.fn(),
            findOne: jest.fn(),
            update: jest.fn(),
            remove: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<CountryController>(CountryController);
    service = module.get<CountryService>(CountryService);
    
    const app = module.createNestApplication();
    app.useGlobalPipes(new ValidationPipe({ transform: true }));
    await app.init();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create', () => {
    it('should create a country with valid data', async () => {
      // Arrange
      const createDto: CreateCountryDto = {
        countryName: 'Test Country',
        continent: 'Test Continent',
      };
      const expectedResult = { id: 1, ...createDto };
      jest.spyOn(service, 'create').mockResolvedValue({ id: 1, countryName: createDto.countryName, continent: createDto.continent, createdAt: new Date(), updatedAt: new Date() } as any);

      // Act
      const result = await controller.create(createDto);

      // Assert
      expect(result).toEqual(expectedResult);
      expect(service.create).toHaveBeenCalledWith(createDto);
    });

    it('should throw error if countryName is missing', async () => {
      // Arrange
      const invalidDto = {
        continent: 'Test Continent',
      };

      // Act & Assert
      await expect(async () => {
        await controller.create(invalidDto as any);
      }).rejects.toThrow();
    });

    it('should throw error if continent is missing', async () => {
      // Arrange
      const invalidDto = {
        countryName: 'Test Country',
      };

      // Act & Assert
      await expect(async () => {
        await controller.create(invalidDto as any);
      }).rejects.toThrow();
    });
  });
});
