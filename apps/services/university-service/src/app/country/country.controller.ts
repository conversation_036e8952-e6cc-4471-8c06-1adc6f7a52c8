import { Controller, Get, Post, Body, Param, Put, Delete, Query, ParseIntPipe } from '@nestjs/common';
import { CountryService } from './country.service';
import { CreateCountryDto, UpdateCountryDto } from '@apply-goal-backend/dto';
import { AppService } from '../app.service';
import { AuditClientService } from '../audit.service';

@Controller('countries')
export class CountryController {
  constructor(
    private readonly countryService: CountryService,
    private readonly appService: AppService,
    private readonly auditService: AuditClientService
  ) {}

  @Post()
  async create(@Body() createCountryDto: CreateCountryDto) {
    const startTime = Date.now();
    try {
      const result = await this.countryService.create(createCountryDto);
      this.appService.trackProcessingDuration('country_create_api_success', (Date.now() - startTime) / 1000);
      
      // Audit logging
      this.auditService.logAction({
        action: 'CREATE_COUNTRY',
        entityType: 'Country',
        entityId: result.id.toString(),
        details: createCountryDto
      });
      
      return result;
    } catch (error) {
      this.appService.trackProcessingDuration('country_create_api_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }

  @Get()
  async findAll(@Query('page') page = 1, @Query('limit') limit = 10) {
    const startTime = Date.now();
    try {
      const result = await this.countryService.findAll(page, limit);
      this.appService.trackProcessingDuration('country_find_all_api_success', (Date.now() - startTime) / 1000);
      return result;
    } catch (error) {
      this.appService.trackProcessingDuration('country_find_all_api_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }

  @Get(':id')
  async findOne(@Param('id', ParseIntPipe) id: number) {
    const startTime = Date.now();
    try {
      const result = await this.countryService.findOne(id);
      this.appService.trackProcessingDuration('country_find_one_api_success', (Date.now() - startTime) / 1000);
      return result;
    } catch (error) {
      this.appService.trackProcessingDuration('country_find_one_api_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }

  @Put(':id')
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateCountryDto: UpdateCountryDto
  ) {
    const startTime = Date.now();
    try {
      const result = await this.countryService.update(id, updateCountryDto);
      this.appService.trackProcessingDuration('country_update_api_success', (Date.now() - startTime) / 1000);
      
      // Audit logging
      this.auditService.logAction({
        action: 'UPDATE_COUNTRY',
        entityType: 'Country',
        entityId: id.toString(),
        details: updateCountryDto
      });
      
      return result;
    } catch (error) {
      this.appService.trackProcessingDuration('country_update_api_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }

  @Delete(':id')
  async remove(@Param('id', ParseIntPipe) id: number) {
    const startTime = Date.now();
    try {
      await this.countryService.remove(id);
      this.appService.trackProcessingDuration('country_delete_api_success', (Date.now() - startTime) / 1000);
      
      // Audit logging
      this.auditService.logAction({
        action: 'DELETE_COUNTRY',
        entityType: 'Country',
        entityId: id.toString(),
        details: { id }
      });
      
      return { success: true };
    } catch (error) {
      this.appService.trackProcessingDuration('country_delete_api_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }
}
