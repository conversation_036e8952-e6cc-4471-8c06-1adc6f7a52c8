import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { Country } from './country.model';
import { CountryService } from './country.service';
import { CountryController } from './country.controller';
import { CountryGrpcController } from './country.grpc.controller';
import { AuditClientService } from '../audit.service';

@Module({
  imports: [
    SequelizeModule.forFeature([Country]),
  ],
  controllers: [CountryController, CountryGrpcController],
  providers: [CountryService, AuditClientService],
  exports: [CountryService],
})
export class CountryModule {}
