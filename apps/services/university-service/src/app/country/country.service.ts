import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Country } from './country.model';
import { BaseRepository } from '@apply-goal-backend/database';
import { CreateCountryDto, UpdateCountryDto } from '@apply-goal-backend/dto';
import { Op } from 'sequelize';
import { AppService } from '../app.service';

@Injectable()
export class CountryService {
  private countryRepository: BaseRepository<Country>;

  constructor(
    @InjectModel(Country)
    private countryModel: typeof Country,
    private readonly appService: AppService
  ) {
    this.countryRepository = new BaseRepository<Country>(countryModel);
  }

  async create(createCountryDto: CreateCountryDto): Promise<Country> {
    const startTime = Date.now();
    try {
      // Check if country with same name already exists
      const existingCountry = await this.countryRepository.findOne({
        where: {
          countryName: createCountryDto.countryName
        }
      });

      if (existingCountry) {
        this.appService.trackProcessingDuration('country_create_conflict', (Date.now() - startTime) / 1000);
        throw new ConflictException(`Country with name "${createCountryDto.countryName}" already exists`);
      }

      return this.countryRepository.create(createCountryDto);
    } catch (error) {
      this.appService.trackProcessingDuration('country_create_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }

  async findAll(page: number = 1, limit: number = 10, filter: any = {}): Promise<{ rows: Country[]; count: number }> {
    return this.countryRepository.findAndCountAll({
      where: filter,
      offset: (page - 1) * limit,
      limit,
      order: [['createdAt', 'DESC']],
    });
  }

  async findOne(id: number): Promise<Country> {
    const country = await this.countryRepository.findById(id);
    if (!country) {
      throw new NotFoundException(`Country with ID ${id} not found`);
    }
    return country;
  }

  async update(id: number, updateCountryDto: UpdateCountryDto): Promise<Country> {
    // If updating countryName, check for uniqueness
    if (updateCountryDto.countryName) {
      const existingCountry = await this.countryRepository.findOne({
        where: {
          countryName: updateCountryDto.countryName,
          id: { [Op.ne]: id } // Exclude current country
        }
      });

      if (existingCountry) {
        throw new ConflictException(`Country with name "${updateCountryDto.countryName}" already exists`);
      }
    }

    const country = await this.findOne(id);
    await country.update(updateCountryDto);
    return country;
  }

  async remove(id: number): Promise<void> {
    const deleted = await this.countryRepository.delete(id);
    
    if (deleted === 0) {
      throw new NotFoundException(`Country with ID ${id} not found`);
    }
  }
}
