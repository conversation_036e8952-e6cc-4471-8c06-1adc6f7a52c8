import { Controller, Logger } from '@nestjs/common';
import { GrpcMethod } from '@nestjs/microservices';
import { CountryService } from './country.service';
import { status } from '@grpc/grpc-js';
import { AppService } from '../app.service';
import { AuditClientService } from '../audit.service';

@Controller()
export class CountryGrpcController {
  private readonly logger = new Logger(CountryGrpcController.name);

  constructor(
    private readonly countryService: CountryService,
    private readonly appService: AppService,
    private readonly auditService: AuditClientService
  ) {}

  @GrpcMethod('UniversityService', 'CreateCountry')
  async createCountry(request: any) {
    const startTime = Date.now();
    try {
      this.logger.log(`gRPC CreateCountry request for name: ${request.countryName}`);
      
      const result = await this.countryService.create({
        countryName: request.countryName,
        continent: request.continent,
        isActive: request.isActive
      });
      
      this.appService.trackProcessingDuration('country_create_grpc_success', (Date.now() - startTime) / 1000);
      
      // Audit logging
      this.auditService.logAction({
        action: 'CREATE_COUNTRY',
        entityType: 'Country',
        entityId: result.id.toString(),
        details: request
      });
      
      return {
        status: 200,
        message: 'Country created successfully',
        data: {
          id: result.id.toString(),
          countryName: result.countryName,
          continent: result.continent,
          isActive: result.isActive,
          createdAt: result.createdAt.toISOString(),
          updatedAt: result.updatedAt.toISOString(),
        },
      };
    } catch (error) {
      this.logger.error(`CreateCountry error: ${error.message}`, error.stack);

      return {
        status: error?.response?.statusCode,
        message: error?.response?.message,
        error: {
          details: error?.response?.error,
        },
      };
    }
  }

  @GrpcMethod('UniversityService', 'GetCountry')
  async getCountry(request: { id: number }) {
    const startTime = Date.now();
    try {
      this.logger.log(`gRPC GetCountry request for ID: ${request.id}`);
      
      const result = await this.countryService.findOne(request.id);
      
      this.appService.trackProcessingDuration('country_get_grpc_success', (Date.now() - startTime) / 1000);
      
      return {
        id: result.id,
        name: result.countryName,
        code: result.continent,
        is_active: result.isActive,
        created_at: result.createdAt.toISOString(),
        updated_at: result.updatedAt.toISOString()
      };
    } catch (error) {
      this.logger.error(`GetCountry error: ${error.message}`, error.stack);
      this.appService.trackProcessingDuration('country_get_grpc_error', (Date.now() - startTime) / 1000);
      throw {
        code: status.NOT_FOUND,
        message: error.message || 'Country not found',
        details: error.stack || '',
      };
    }
  }

  @GrpcMethod('UniversityService', 'UpdateCountry')
  async updateCountry(request: { id: number; name?: string; code?: string; is_active?: boolean }) {
    const startTime = Date.now();
    try {
      this.logger.log(`gRPC UpdateCountry request for ID: ${request.id}`);
      
      const updateData: any = {};
      if (request.name !== undefined) updateData.countryName = request.name;
      if (request.code !== undefined) updateData.continent = request.code;
      if (request.is_active !== undefined) updateData.isActive = request.is_active;
      
      const result = await this.countryService.update(request.id, updateData);
      
      this.appService.trackProcessingDuration('country_update_grpc_success', (Date.now() - startTime) / 1000);
      
      // Audit logging
      this.auditService.logAction({
        action: 'UPDATE_COUNTRY',
        entityType: 'Country',
        entityId: result.id.toString(),
        details: request
      });
      
      return {
        id: result.id,
        name: result.countryName,
        code: result.continent,
        is_active: result.isActive,
        created_at: result.createdAt.toISOString(),
        updated_at: result.updatedAt.toISOString()
      };
    } catch (error) {
      this.logger.error(`UpdateCountry error: ${error.message}`, error.stack);
      this.appService.trackProcessingDuration('country_update_grpc_error', (Date.now() - startTime) / 1000);
      throw {
        code: status.INTERNAL,
        message: error.message || 'Failed to update country',
        details: error.stack || '',
      };
    }
  }

  @GrpcMethod('UniversityService', 'DeleteCountry')
  async deleteCountry(request: { id: number }) {
    const startTime = Date.now();
    try {
      this.logger.log(`gRPC DeleteCountry request for ID: ${request.id}`);
      
      await this.countryService.remove(request.id);
      
      this.appService.trackProcessingDuration('country_delete_grpc_success', (Date.now() - startTime) / 1000);
      
      // Audit logging
      this.auditService.logAction({
        action: 'DELETE_COUNTRY',
        entityType: 'Country',
        entityId: request.id.toString(),
        details: { id: request.id }
      });
      
      return { success: true, id: request.id };
    } catch (error) {
      this.logger.error(`DeleteCountry error: ${error.message}`, error.stack);
      this.appService.trackProcessingDuration('country_delete_grpc_error', (Date.now() - startTime) / 1000);
      throw {
        code: status.INTERNAL,
        message: error.message || 'Failed to delete country',
        details: error.stack || '',
      };
    }
  }

  @GrpcMethod('UniversityService', 'ListCountries')
  async listCountries(request: { page?: number; limit?: number; is_active?: boolean }) {
    const startTime = Date.now();
    try {
      this.logger.log(`gRPC ListCountries request - page: ${request.page}, limit: ${request.limit}`);
      
      const page = request.page || 1;
      const limit = request.limit || 10;
      
      // Add isActive filter if provided
      const filter: any = {};
      if (request.is_active !== undefined) {
        filter.isActive = request.is_active;
      }
      
      const result = await this.countryService.findAll(page, limit, filter);
      
      this.appService.trackProcessingDuration('country_list_grpc_success', (Date.now() - startTime) / 1000);
      
      return {
        total: result.count,
        page: page,
        limit: limit,
        countries: result.rows.map(country => ({
          id: country.id,
          name: country.countryName,
          code: country.continent,
          is_active: country.isActive,
          created_at: country.createdAt.toISOString(),
          updated_at: country.updatedAt.toISOString()
        }))
      };
    } catch (error) {
      this.logger.error(`ListCountries error: ${error.message}`, error.stack);
      this.appService.trackProcessingDuration('country_list_grpc_error', (Date.now() - startTime) / 1000);
      throw {
        code: status.INTERNAL,
        message: error.message || 'Failed to list countries',
        details: error.stack || '',
      };
    }
  }
}
