import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Campus } from './campus.model';
import { BaseRepository } from '@apply-goal-backend/database';
import { CreateCampusRequestDto, UpdateCampusRequestDto } from '@apply-goal-backend/dto';
import { Op } from 'sequelize';
import { AppService } from '../app.service';

@Injectable()
export class CampusService {
  private campusRepository: BaseRepository<Campus>;

  constructor(
    @InjectModel(Campus)
    private campusModel: typeof Campus,
    private readonly appService: AppService
  ) {
    this.campusRepository = new BaseRepository<Campus>(campusModel);
  }

  async create(createCampusDto: CreateCampusRequestDto): Promise<any> {
    const startTime = Date.now();
    try {
      console.log("createCampusDto",createCampusDto);
      // Check if campus with same name already exists for this university
      // const existingCampus = await this.campusRepository.findOne({
      //   where: {
      //     name: createCampusDto.name,
      //     universityId: createCampusDto.universityId
      //   }
      // });

      // if (existingCampus) {
      //   this.appService.trackProcessingDuration('campus_create_conflict', (Date.now() - startTime) / 1000);
      //   throw new ConflictException(`Campus with name "${createCampusDto.name}" already exists for this university`);
      // }

      // const campus = await this.campusRepository.create(createCampusDto);
      // this.appService.trackProcessingDuration('campus_create_success', (Date.now() - startTime) / 1000);
      return "campus";
    } catch (error) {
      this.appService.trackProcessingDuration('campus_create_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }

  async findAll(universityId?: number, page: number = 1, limit: number = 10): Promise<{ rows: Campus[]; count: number }> {
    const where = universityId ? { universityId } : {};
    
    return this.campusRepository.findAndCountAll({
      where,
      offset: (page - 1) * limit,
      limit,
      order: [['createdAt', 'DESC']],
    });
  }

  async findOne(id: number): Promise<Campus> {
    const campus = await this.campusRepository.findById(id);
    
    if (!campus) {
      throw new NotFoundException(`Campus with ID ${id} not found`);
    }
    
    return campus;
  }

  async update(id: number, updateCampusDto: UpdateCampusRequestDto): Promise<Campus> {
    const startTime = Date.now();
    try {
      // If updating name, check for uniqueness within the university
      if (updateCampusDto.name) {
        const campus = await this.findOne(id);
        const existingCampus = await this.campusRepository.findOne({
          where: {
            name: updateCampusDto.name,
            universityId: campus.universityId,
            id: { [Op.ne]: id } // Exclude current campus
          }
        });

        if (existingCampus) {
          throw new ConflictException(`Campus with name "${updateCampusDto.name}" already exists for this university`);
        }
      }

      const campus = await this.findOne(id);
      await campus.update(updateCampusDto);
      this.appService.trackProcessingDuration('campus_update_success', (Date.now() - startTime) / 1000);
      return campus;
    } catch (error) {
      this.appService.trackProcessingDuration('campus_update_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }

  async remove(id: number): Promise<void> {
    const deleted = await this.campusRepository.delete(id);
    
    if (deleted === 0) {
      throw new NotFoundException(`Campus with ID ${id} not found`);
    }
  }
}