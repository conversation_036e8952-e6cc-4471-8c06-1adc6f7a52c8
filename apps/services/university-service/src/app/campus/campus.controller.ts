import { Controller, Get, Post, Body, Param, Put, Delete, Query, ParseIntPipe } from '@nestjs/common';
import { CampusService } from './campus.service';
import { 
  CreateCampusRequestDto, 
  CreateCampusResponseDto, 
  UpdateCampusRequestDto, 
  UpdateCampusResponseDto,
  GetCampusRequestDto,
  GetCampusResponseDto,
  GetAllCampusesRequestDto,
  GetAllCampusesResponseDto,
  DeleteCampusRequestDto,
  DeleteCampusResponseDto
} from '@apply-goal-backend/dto';
import { AppService } from '../app.service';
import { AuditClientService } from '../audit.service';

@Controller('campuses')
export class CampusController {
  constructor(
    private readonly campusService: CampusService,
    private readonly appService: AppService,
    private readonly auditService: AuditClientService
  ) {}

  @Post()
  async create(@Body() createCampusDto: CreateCampusRequestDto): Promise<CreateCampusResponseDto> {
    const startTime = Date.now();
    try {
      const result = await this.campusService.create(createCampusDto);
      this.appService.trackProcessingDuration('campus_create_api_success', (Date.now() - startTime) / 1000);
      
      // Audit logging
      this.auditService.logAction({
        action: 'CREATE_CAMPUS',
        entityType: 'Campus',
        entityId: result.id.toString(),
        details: createCampusDto
      });
      
      return result as CreateCampusResponseDto;
    } catch (error) {
      this.appService.trackProcessingDuration('campus_create_api_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }

  @Get()
  async findAll(@Query() query: GetAllCampusesRequestDto): Promise<GetAllCampusesResponseDto> {
    const { universityId, page = 1, limit = 10 } = query;
    const { rows, count } = await this.campusService.findAll(universityId, page, limit);
    
    return {
      total: count,
      page,
      limit,
      campuses: rows as any[]
    };
  }

  @Get(':id')
  async findOne(@Param('id', ParseIntPipe) id: number): Promise<GetCampusResponseDto> {
    return this.campusService.findOne(id) as unknown as GetCampusResponseDto;
  }

  @Put(':id')
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateCampusDto: UpdateCampusRequestDto
  ): Promise<UpdateCampusResponseDto> {
    const startTime = Date.now();
    try {
      const result = await this.campusService.update(id, updateCampusDto);
      this.appService.trackProcessingDuration('campus_update_api_success', (Date.now() - startTime) / 1000);
      
      // Audit logging
      this.auditService.logAction({
        action: 'UPDATE_CAMPUS',
        entityType: 'Campus',
        entityId: id.toString(),
        details: updateCampusDto
      });
      
      return result as UpdateCampusResponseDto;
    } catch (error) {
      this.appService.trackProcessingDuration('campus_update_api_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }

  @Delete(':id')
  async remove(@Param('id', ParseIntPipe) id: number): Promise<DeleteCampusResponseDto> {
    const startTime = Date.now();
    try {
      await this.campusService.remove(id);
      this.appService.trackProcessingDuration('campus_delete_api_success', (Date.now() - startTime) / 1000);
      
      // Audit logging
      this.auditService.logAction({
        action: 'DELETE_CAMPUS',
        entityType: 'Campus',
        entityId: id.toString(),
        details: { id }
      });
      
      return { id, success: true };
    } catch (error) {
      this.appService.trackProcessingDuration('campus_delete_api_error', (Date.now() - startTime) / 1000);
      throw error;
    }
  }
}