import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { Campus } from './campus.model';
import { CampusService } from './campus.service';
import { CampusController } from './campus.controller';

@Module({
  imports: [
    SequelizeModule.forFeature([Campus]),
  ],
  controllers: [CampusController],
  providers: [CampusService],
  exports: [CampusService],
})
export class CampusModule {}