{"name": "university-service", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/services/university-service/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "nx:run-commands", "options": {"command": "webpack-cli build", "args": ["node-env=production"]}, "configurations": {"development": {"args": ["node-env=development"]}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "university-service:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "university-service:build:development"}, "production": {"buildTarget": "university-service:build:production"}}}, "test": {"options": {"passWithNoTests": true}}}}