{"name": "students-service", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/services/students-service/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "nx:run-commands", "options": {"command": "webpack-cli build", "args": ["node-env=production"]}, "configurations": {"development": {"args": ["node-env=development"]}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "students-service:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "students-service:build:development"}, "production": {"buildTarget": "students-service:build:production"}}}, "test": {"options": {"passWithNoTests": true}}}}