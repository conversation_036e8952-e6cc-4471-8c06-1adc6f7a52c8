# Students Service Environment Configuration

# Server Configuration
PORT=5008
NODE_ENV=development

# gRPC Configuration
GRPC_URL=0.0.0.0:50058

# Database Configuration
DB_TYPE=postgres
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=password
DB_NAME=apply_goal_students
DB_SYNCHRONIZE=true
DB_LOGGING=true

# Monitoring Configuration
METRICS_PORT=5008
JAEGER_ENDPOINT=http://jaeger:4318/v1/traces

# Logging
LOG_LEVEL=info

# Health Check
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_PATH=/health

# Academic Configuration
DEFAULT_ACADEMIC_YEAR=2024
DEFAULT_SEMESTER=Fall
GPA_SCALE=4.0

# Student ID Generation
STUDENT_ID_PREFIX=ST
STUDENT_ID_LENGTH=8

# File Upload (for transcripts, documents)
MAX_FILE_SIZE=10MB
ALLOWED_FILE_TYPES=pdf,doc,docx,jpg,jpeg,png

# Email Configuration (for notifications)
SMTP_HOST=localhost
SMTP_PORT=587
SMTP_USER=
SMTP_PASS=
SMTP_FROM=<EMAIL>
