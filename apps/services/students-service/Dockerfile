# Build stage
FROM node:18-alpine as builder

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY nx.json ./
COPY tsconfig*.json ./
COPY eslint.config.mjs ./

# Copy source code
COPY apps/services/students-service ./apps/services/students-service
COPY libs ./libs

# Install dependencies
RUN npm ci

# Build the application
RUN npx nx build students-service --prod

# Production stage
FROM node:18-alpine

WORKDIR /app

# Copy built assets from builder
COPY --from=builder /app/dist/apps/services/students-service ./

# Install production dependencies
RUN npm ci --only=production

# Set environment variables
ENV NODE_ENV=production
ENV PORT=5008

# Expose the service port
EXPOSE 5008

# Start the service
CMD ["node", "main.js"]
