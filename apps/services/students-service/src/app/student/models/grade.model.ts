import { BaseModel } from '@apply-goal-backend/database';
import {
  Column,
  DataType,
  BelongsTo,
  ForeignKey,
  Table,
  Index,
  Default,
} from 'sequelize-typescript';
import { Student } from './student.model';
import { Enrollment } from './enrollment.model';

@Table({ 
  tableName: 'grades',
  indexes: [
    { unique: true, fields: ['student_id', 'enrollment_id'] }
  ]
})
export class Grade extends BaseModel {
  @ForeignKey(() => Student)
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  student_id!: bigint;

  @ForeignKey(() => Enrollment)
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  enrollment_id!: bigint;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  course_id!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  course_name!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  course_code!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    comment: 'A+, A, A-, B+, B, B-, C+, C, C-, D+, D, D-, F',
  })
  letter_grade!: string;

  @Column({
    type: DataType.DECIMAL(3, 2),
    allowNull: false,
    comment: '4.0 scale',
  })
  grade_points!: number;

  @Column({
    type: DataType.DECIMAL(5, 2),
    allowNull: true,
    comment: '0-100',
  })
  percentage?: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  credits!: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  semester!: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  academic_year!: number;

  @Default('final')
  @Column({
    type: DataType.ENUM('final', 'midterm', 'assignment', 'quiz', 'exam', 'project'),
    allowNull: false,
  })
  grade_type!: string;

  @Column({
    type: DataType.DATEONLY,
    allowNull: false,
  })
  grade_date!: Date;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  instructor_id?: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  instructor_name?: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  comments?: string;

  @Column({
    type: DataType.JSON,
    allowNull: true,
  })
  breakdown?: {
    assignments?: number;
    quizzes?: number;
    midterm?: number;
    final?: number;
    participation?: number;
    projects?: number;
  };

  @Column({
    type: DataType.JSON,
    allowNull: true,
  })
  metadata?: Record<string, any>;

  @BelongsTo(() => Student)
  student!: Student;

  @BelongsTo(() => Enrollment)
  enrollment!: Enrollment;

  // Virtual properties
  get quality_points(): number {
    return this.grade_points * this.credits;
  }

  get is_passing(): boolean {
    return this.grade_points >= 1.0; // D- or better
  }

  get is_honor_grade(): boolean {
    return this.grade_points >= 3.5; // B+ or better
  }

  get grade_description(): string {
    const gradeMap: Record<string, string> = {
      'A+': 'Excellent',
      'A': 'Excellent',
      'A-': 'Very Good',
      'B+': 'Good',
      'B': 'Good',
      'B-': 'Satisfactory',
      'C+': 'Satisfactory',
      'C': 'Satisfactory',
      'C-': 'Below Average',
      'D+': 'Poor',
      'D': 'Poor',
      'D-': 'Very Poor',
      'F': 'Failing',
    };
    
    return gradeMap[this.letter_grade] || 'Unknown';
  }
}
