import { BaseModel } from '@apply-goal-backend/database';
import {
  Column,
  DataType,
  BelongsTo,
  ForeignKey,
  Table,
  Index,
  Default,
} from 'sequelize-typescript';
import { Student } from './student.model';

@Table({ 
  tableName: 'emergency_contacts',
  indexes: [
    { unique: true, fields: ['student_id', 'email'] }
  ]
})
export class EmergencyContact extends BaseModel {
  @ForeignKey(() => Student)
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  student_id!: bigint;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  name!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    comment: 'parent, guardian, spouse, sibling, friend, etc.',
  })
  relationship!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  phone!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  email!: string;

  @Column({
    type: DataType.JSON,
    allowNull: true,
  })
  address?: {
    street: string;
    city: string;
    state: string;
    postal_code: string;
    country: string;
  };

  @Default(true)
  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
  })
  is_primary!: boolean;

  @Default(true)
  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    comment: 'Can pick up student in emergency',
  })
  can_pick_up!: boolean;

  @Default(true)
  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    comment: 'Can authorize medical treatment',
  })
  can_authorize_medical!: boolean;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  notes?: string;

  @Column({
    type: DataType.JSON,
    allowNull: true,
  })
  metadata?: Record<string, any>;

  @BelongsTo(() => Student)
  student!: Student;

  // Virtual properties
  get full_contact_info(): string {
    return `${this.name} (${this.relationship}) - ${this.phone} - ${this.email}`;
  }

  get is_family(): boolean {
    const familyRelationships = ['parent', 'guardian', 'spouse', 'sibling', 'child'];
    return familyRelationships.includes(this.relationship.toLowerCase());
  }
}
