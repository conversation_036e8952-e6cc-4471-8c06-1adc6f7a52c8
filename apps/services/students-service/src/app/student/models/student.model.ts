import { BaseModel } from '@apply-goal-backend/database';
import {
  Column,
  DataType,
  HasMany,
  Table,
  Index,
  Default,
} from 'sequelize-typescript';
import { EmergencyContact } from './emergency-contact.model';
import { Enrollment } from './enrollment.model';
import { Grade } from './grade.model';

@Table({
  tableName: 'students',
  indexes: [
    { unique: true, fields: ['email'] },
    { unique: true, fields: ['student_id'] },
  ],
})
export class Student extends BaseModel {
  @Column({
    type: DataType.BIGINT,
    allowNull: true,
  })
  user_id?: bigint;

  @Column({
    type: DataType.STRING,
    unique: true,
    allowNull: false,
  })
  student_id!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  first_name!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  last_name!: string;

  @Column({
    type: DataType.STRING,
    unique: true,
    allowNull: false,
  })
  email!: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  phone?: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  nationality?: string;

  @Column({
    type: DataType.DATEONLY,
    allowNull: true,
  })
  date_of_birth?: Date;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    defaultValue: 'Undeclared',
  })
  major?: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  minor?: string;

  @Column({
    type: DataType.JSON,
    allowNull: true,
  })
  address?: {
    street: string;
    city: string;
    state: string;
    postal_code: string;
    country: string;
  };

  @Column({
    type: DataType.ENUM(
      'freshman',
      'sophomore',
      'junior',
      'senior',
      'graduate',
      'postgraduate'
    ),
    allowNull: false,
  })
  academic_level!: string;

  @Column({
    type: DataType.DECIMAL(3, 2),
    allowNull: true,
    defaultValue: 0.0,
  })
  gpa?: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 0,
  })
  total_credits?: number;

  @Column({
    type: DataType.ENUM(
      'active',
      'inactive',
      'graduated',
      'on_leave',
      'withdrawn',
      'suspended'
    ),
    allowNull: false,
    defaultValue: 'active',
  })
  status!: string;

  @Column({
    type: DataType.DATEONLY,
    allowNull: true,
  })
  enrollment_date?: Date;

  @Column({
    type: DataType.DATEONLY,
    allowNull: true,
  })
  graduation_date?: Date;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  university_id?: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  agency_id?: string;

  @Column({
    type: DataType.JSON,
    allowNull: true,
  })
  metadata?: Record<string, any>;

  @HasMany(() => Enrollment)
  enrollments!: Enrollment[];

  @HasMany(() => Grade)
  grades!: Grade[];

  @HasMany(() => EmergencyContact)
  emergency_contacts!: EmergencyContact[];

  // Virtual properties
  get full_name(): string {
    return `${this.first_name} ${this.last_name}`;
  }

  get is_active(): boolean {
    return this.status === 'active';
  }

  get is_graduated(): boolean {
    return this.status === 'graduated';
  }

  get age(): number {
    const today = new Date();
    const birthDate = new Date(this.date_of_birth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (
      monthDiff < 0 ||
      (monthDiff === 0 && today.getDate() < birthDate.getDate())
    ) {
      age--;
    }

    return age;
  }

  get academic_standing(): string {
    if (!this.gpa) return 'Unknown';

    if (this.gpa >= 3.5) return "Dean's List";
    if (this.gpa >= 3.0) return 'Good Standing';
    if (this.gpa >= 2.0) return 'Satisfactory';
    if (this.gpa >= 1.0) return 'Probation';
    return 'Academic Warning';
  }

  get credits_to_graduation(): number {
    const requiredCredits =
      this.academic_level === 'graduate' ||
      this.academic_level === 'postgraduate'
        ? 30
        : 120;
    return Math.max(0, requiredCredits - (this.total_credits || 0));
  }
}
