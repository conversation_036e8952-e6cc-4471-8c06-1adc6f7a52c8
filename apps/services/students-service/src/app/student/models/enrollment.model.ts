import { BaseModel } from '@apply-goal-backend/database';
import {
  Column,
  DataType,
  BelongsTo,
  ForeignKey,
  HasMany,
  Table,
  Index,
  Default,
} from 'sequelize-typescript';
import { Student } from './student.model';
import { Grade } from './grade.model';

@Table({ 
  tableName: 'enrollments',
  indexes: [
    { unique: true, fields: ['student_id', 'course_id', 'semester'] }
  ]
})
export class Enrollment extends BaseModel {
  @ForeignKey(() => Student)
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  student_id!: bigint;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  course_id!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  course_name!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  course_code!: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  credits!: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  semester!: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  academic_year!: number;

  @Default('enrolled')
  @Column({
    type: DataType.ENUM('enrolled', 'completed', 'dropped', 'failed', 'withdrawn'),
    allowNull: false,
  })
  status!: string;

  @Column({
    type: DataType.DATEONLY,
    allowNull: false,
  })
  enrollment_date!: Date;

  @Column({
    type: DataType.DATEONLY,
    allowNull: true,
  })
  completion_date?: Date;

  @Column({
    type: DataType.DATEONLY,
    allowNull: true,
  })
  drop_date?: Date;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  instructor_id?: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  instructor_name?: string;

  @Column({
    type: DataType.JSON,
    allowNull: true,
  })
  schedule?: {
    days: string[];
    start_time: string;
    end_time: string;
    location: string;
  };

  @Column({
    type: DataType.JSON,
    allowNull: true,
  })
  metadata?: Record<string, any>;

  @BelongsTo(() => Student)
  student!: Student;

  @HasMany(() => Grade)
  grades!: Grade[];

  // Virtual properties
  get is_current(): boolean {
    return this.status === 'enrolled';
  }

  get is_completed(): boolean {
    return this.status === 'completed';
  }

  get duration_days(): number {
    if (!this.completion_date && !this.drop_date) {
      return Math.floor((new Date().getTime() - this.enrollment_date.getTime()) / (1000 * 60 * 60 * 24));
    }
    
    const endDate = this.completion_date || this.drop_date;
    return Math.floor((endDate!.getTime() - this.enrollment_date.getTime()) / (1000 * 60 * 60 * 24));
  }
}
