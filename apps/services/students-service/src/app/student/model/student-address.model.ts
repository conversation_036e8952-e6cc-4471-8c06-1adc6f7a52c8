import { BaseModel } from '@apply-goal-backend/database';
import {
  AutoI<PERSON>rement,
  BelongsTo,
  Column,
  DataType,
  ForeignKey,
  PrimaryKey,
  Table,
} from 'sequelize-typescript';
import { StudentPersonal } from './student-personal.model';

@Table({ tableName: 'student_address' })
export class StudentAddress extends BaseModel {
  @ForeignKey(() => StudentPersonal)
  @Column(DataType.BIGINT)
  userId!: number;

  @Column(DataType.STRING(20)) addressType!: string;
  @Column(DataType.TEXT) addressLine?: string;
  @Column(DataType.STRING(100)) country?: string;
  @Column(DataType.STRING(100)) state?: string;
  @Column(DataType.STRING(100)) city?: string;
  @Column(DataType.STRING(50)) postalCode?: string;

  @BelongsTo(() => StudentPersonal)
  student?: StudentPersonal;
}
