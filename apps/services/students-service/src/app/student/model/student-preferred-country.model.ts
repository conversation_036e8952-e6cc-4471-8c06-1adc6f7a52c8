import { BaseModel } from '@apply-goal-backend/database';
import { Column, DataType, ForeignKey, Table } from 'sequelize-typescript';
import { StudentPersonal } from './student-personal.model';

@Table({ tableName: 'student_preferred_country' })
export class StudentPreferredCountry extends BaseModel {
  @ForeignKey(() => StudentPersonal)
  @Column(DataType.BIGINT)
  userId!: number;

  @Column(DataType.STRING(100)) country!: string;
}
