import { BaseModel } from '@apply-goal-backend/database';
import { Column, DataType, ForeignKey, Table } from 'sequelize-typescript';
import { StudentPersonal } from './student-personal.model';

@Table({ tableName: 'student_subject' })
export class StudentSubject extends BaseModel {
  @ForeignKey(() => StudentPersonal)
  @Column(DataType.BIGINT)
  userId!: number;

  // @ForeignKey(() => Subject)
  // @Column(DataType.BIGINT)
  // subjectId!: number;
}
