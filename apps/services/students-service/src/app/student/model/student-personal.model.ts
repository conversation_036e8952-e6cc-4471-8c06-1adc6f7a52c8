import { BaseModel } from '@apply-goal-backend/database';
import {
  <PERSON><PERSON>sToMany,
  Column,
  DataType,
  ForeignKey,
  HasMany,
  PrimaryKey,
  Table,
} from 'sequelize-typescript';
import { User } from '../../../../../auth-service/src/app/user/model/user.model';
import { StudentAddress } from './student-address.model';
import { StudentMarital } from './student-marital.model';
import { StudentSponsor } from './student-sponsor.model';
import { StudentEmergencyContact } from './student-emergency-contact.model';
import { StudentPreferredCountry } from './student-preferred-country.model';
import { StudentSocialLink } from './student-social-links.model';
import { StudentReference } from './student-reference.model';
import { StudentSubject } from './student-subject.model';

@Table({ tableName: 'student_personal' })
export class StudentPersonal extends BaseModel {
  @PrimaryKey
  @ForeignKey(() => User)
  @Column(DataType.BIGINT)
  userId!: number;

  @Column(DataType.STRING(100)) lastName?: string;
  @Column(DataType.STRING(100)) firstName?: string;
  @Column(DataType.STRING(100)) nativeLanguage?: string;
  @Column(DataType.STRING(255)) email?: string;
  @Column(DataType.DATE) dateOfBirth?: Date;
  @Column(DataType.STRING(10)) gender?: string;
  @Column(DataType.STRING(200)) fatherName?: string;
  @Column(DataType.STRING(200)) motherName?: string;
  @Column(DataType.STRING(100)) nationalId?: string;
  @Column(DataType.STRING(100)) passportNumber?: string;
  @Column(DataType.TEXT) noteText?: string;

  @HasMany(() => StudentAddress)
  addresses?: StudentAddress[];

  @HasMany(() => StudentMarital)
  marital?: StudentMarital;

  @HasMany(() => StudentSponsor)
  sponsor?: StudentSponsor;

  @HasMany(() => StudentEmergencyContact)
  emergencyContacts?: StudentEmergencyContact[];

  //   @BelongsToMany(() => Subject, () => StudentSubject)
  //   subjects?: Subject[];

  @HasMany(() => StudentPreferredCountry)
  preferredCountries?: StudentPreferredCountry[];

  @HasMany(() => StudentSocialLink)
  socialLinks?: StudentSocialLink[];

  @HasMany(() => StudentReference)
  reference?: StudentReference;
}
