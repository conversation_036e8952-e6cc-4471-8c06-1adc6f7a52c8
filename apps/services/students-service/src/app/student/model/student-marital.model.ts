import { BaseModel } from '@apply-goal-backend/database';
import {
  BelongsTo,
  Column,
  DataType,
  ForeignKey,
  PrimaryKey,
  Table,
} from 'sequelize-typescript';
import { StudentPersonal } from './student-personal.model';

@Table({ tableName: 'student_marital' })
export class StudentMarital extends BaseModel {
  @ForeignKey(() => StudentPersonal)
  @PrimaryKey
  @Column(DataType.BIGINT)
  userId!: number;

  @Column(DataType.STRING(20)) maritalStatus?: string;
  @Column(DataType.STRING(200)) spouseName?: string;
  @Column(DataType.STRING(100)) spousePassport?: string;

  @BelongsTo(() => StudentPersonal)
  student?: StudentPersonal;
}
