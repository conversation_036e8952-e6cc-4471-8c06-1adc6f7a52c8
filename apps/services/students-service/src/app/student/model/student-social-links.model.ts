import { BaseModel } from '@apply-goal-backend/database';
import {
  AutoIncrement,
  BelongsTo,
  Column,
  DataType,
  ForeignKey,
  PrimaryKey,
  Table,
} from 'sequelize-typescript';
import { StudentPersonal } from './student-personal.model';

@Table({ tableName: 'student_social_link' })
export class StudentSocialLink extends BaseModel {
  @ForeignKey(() => StudentPersonal)
  @Column(DataType.BIGINT)
  userId!: number;

  @Column(DataType.STRING(50)) platform?: string;
  @Column(DataType.TEXT) url?: string;

  @BelongsTo(() => StudentPersonal)
  student?: StudentPersonal;
}
