import { BaseModel } from '@apply-goal-backend/database';
import {
  BelongsTo,
  Column,
  DataType,
  ForeignKey,
  PrimaryKey,
  Table,
} from 'sequelize-typescript';
import { StudentPersonal } from './student-personal.model';

@Table({ tableName: 'student_reference' })
export class StudentReference extends BaseModel {
  @ForeignKey(() => StudentPersonal)
  @PrimaryKey
  @Column(DataType.BIGINT)
  userId!: number;

  @Column(DataType.TEXT) referenceText?: string;

  @BelongsTo(() => StudentPersonal)
  student?: StudentPersonal;
}
