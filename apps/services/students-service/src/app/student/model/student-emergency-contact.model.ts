import { BaseModel } from '@apply-goal-backend/database';
import {
  AutoIncrement,
  BelongsTo,
  Column,
  DataType,
  ForeignKey,
  PrimaryKey,
  Table,
} from 'sequelize-typescript';
import { StudentPersonal } from './student-personal.model';

@Table({ tableName: 'student_emergency_contact' })
export class StudentEmergencyContact extends BaseModel {
  @ForeignKey(() => StudentPersonal)
  @Column(DataType.BIGINT)
  userId!: number;

  @Column(DataType.STRING(100)) lastName?: string;
  @Column(DataType.STRING(100)) middleName?: string;
  @Column(DataType.STRING(100)) firstName?: string;
  @Column(DataType.STRING(50)) phoneHome?: string;
  @Column(DataType.STRING(50)) phoneMobile?: string;
  @Column(DataType.STRING(100)) relationship?: string;

  @BelongsTo(() => StudentPersonal)
  student?: StudentPersonal;
}
