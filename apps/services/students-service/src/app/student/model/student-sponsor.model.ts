import {
  <PERSON>ongsTo,
  Column,
  DataType,
  ForeignKey,
  PrimaryKey,
  Table,
} from 'sequelize-typescript';
import { StudentPersonal } from './student-personal.model';
import { BaseModel } from '@apply-goal-backend/database';

@Table({ tableName: 'student_sponsor' })
export class StudentSponsor extends BaseModel {
  @ForeignKey(() => StudentPersonal)
  @PrimaryKey
  @Column(DataType.BIGINT)
  userId!: number;

  @Column(DataType.STRING(200)) sponsorName?: string;
  @Column(DataType.STRING(100)) relationship?: string;
  @Column(DataType.STRING(50)) phoneNumber?: string;
  @Column(DataType.STRING(50)) guardianPhone?: string;

  @BelongsTo(() => StudentPersonal)
  student?: StudentPersonal;
}
