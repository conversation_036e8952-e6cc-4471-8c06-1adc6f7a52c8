import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { StudentController } from './student.controller';
import { StudentService } from './student.service';
import { Student } from './models/student.model';
import { Enrollment } from './models/enrollment.model';
import { Grade } from './models/grade.model';
import { EmergencyContact } from './models/emergency-contact.model';

@Module({
  imports: [
    SequelizeModule.forFeature([Student, Enrollment, Grade, EmergencyContact]),
  ],
  controllers: [StudentController],
  providers: [StudentService],
  exports: [StudentService],
})
export class StudentModule {}
