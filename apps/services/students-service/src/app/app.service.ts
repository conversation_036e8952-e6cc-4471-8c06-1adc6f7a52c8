import { Injectable } from '@nestjs/common';
import { MetricsService } from '@apply-goal-backend/monitoring';

@Injectable()
export class AppService {
  private requestCounter: any;
  private requestDuration: any;
  private studentCounter: any;
  private studentOperationsCounter: any;
  private studentProcessingDuration: any;
  private activeStudentsGauge: any;

  constructor(private metricsService: MetricsService) {
    // HTTP metrics
    this.requestCounter = this.metricsService.createCounter(
      'http_requests_total',
      'Total number of HTTP requests processed',
      ['method', 'endpoint', 'status']
    );

    this.requestDuration = this.metricsService.createHistogram(
      'http_request_duration_seconds',
      'HTTP request duration in seconds',
      ['method', 'endpoint']
    );

    // Student-specific metrics
    this.studentCounter = this.metricsService.createCounter(
      'students_total',
      'Total number of students',
      ['status', 'grade_level']
    );

    this.studentOperationsCounter = this.metricsService.createCounter(
      'student_operations_total',
      'Total number of student operations',
      ['operation', 'status']
    );

    this.studentProcessingDuration = this.metricsService.createHistogram(
      'student_processing_duration_seconds',
      'Student processing duration in seconds',
      ['operation'],
      [0.1, 0.5, 1, 2, 5, 10]
    );

    this.activeStudentsGauge = this.metricsService.createGauge(
      'active_students_current',
      'Current number of active students',
      ['grade_level']
    );
  }

  // Track HTTP request
  trackHttpRequest(method: string, endpoint: string, status: string, duration: number) {
    this.requestCounter.inc({ method, endpoint, status });
    this.requestDuration.observe({ method, endpoint }, duration);
  }

  // Track student registration
  trackStudentRegistration(status: string, gradeLevel: string) {
    this.studentCounter.inc({ status, grade_level: gradeLevel });
  }

  // Track student operation
  trackStudentOperation(operation: string, status: string) {
    this.studentOperationsCounter.inc({ operation, status });
  }

  // Track student processing duration
  trackStudentProcessingDuration(operation: string, durationSeconds: number) {
    this.studentProcessingDuration.observe({ operation }, durationSeconds);
  }

  // Update active students count
  updateActiveStudentsCount(gradeLevel: string, count: number) {
    this.activeStudentsGauge.set({ grade_level: gradeLevel }, count);
  }

  getData(): { message: string } {
    this.trackHttpRequest('GET', '/api', '200', 0.1);
    return { message: 'Hello from Students Service' };
  }
}
