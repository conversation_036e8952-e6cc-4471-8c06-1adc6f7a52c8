# Students Service

The Students Service is a gRPC microservice that handles all student-related operations including student management, academic records, enrollments, and grades.

## Features

### Student Management
- Student CRUD operations
- Student profile management
- Emergency contact management
- Student ID generation
- Academic status tracking

### Academic Records
- Course enrollment management
- Grade recording and management
- GPA calculation
- Academic progress tracking
- Transcript generation

### Data Models

#### Student Entity
- Personal information (name, email, phone, address)
- Academic information (major, minor, GPA, credits)
- Status tracking (active, inactive, graduated, etc.)
- University and agency associations
- Metadata support

#### Enrollment Entity
- Student-course associations
- Semester and academic year tracking
- Enrollment status (enrolled, completed, dropped, etc.)
- Schedule information
- Instructor details

#### Grade Entity
- Letter grades and grade points
- Credit hours and quality points
- Grade breakdown (assignments, exams, etc.)
- Academic year and semester
- Instructor information

#### Emergency Contact Entity
- Contact information
- Relationship details
- Authorization levels
- Primary contact designation

## gRPC Service Methods

### Student Operations
- `CreateStudent` - Create a new student
- `GetStudent` - Retrieve student by ID
- `UpdateStudent` - Update student information
- `DeleteStudent` - Delete student
- `ListStudents` - List students with filtering and pagination

### Academic Operations
- `EnrollInCourse` - Enroll student in a course
- `DropCourse` - Drop student from a course
- `GetEnrollments` - Get student enrollments
- `UpdateGrades` - Update student grades
- `GetAcademicProgress` - Get academic progress summary
- `GetTranscript` - Generate student transcript

## Database Schema

### Tables
- `students` - Main student information
- `enrollments` - Course enrollments
- `grades` - Academic grades
- `emergency_contacts` - Emergency contact information

### Indexes
- Unique indexes on email and student_id
- Composite indexes for enrollment lookups
- Performance indexes for common queries

## Configuration

Copy `.env.example` to `.env` and configure:

```bash
cp .env.example .env
```

Key configuration options:
- `GRPC_URL` - gRPC server binding (default: 0.0.0.0:50058)
- `DB_HOST`, `DB_PORT`, `DB_NAME` - Database connection
- `DB_USERNAME`, `DB_PASSWORD` - Database credentials

## Database Setup

The service uses PostgreSQL with TypeORM:

```sql
-- Create database
CREATE DATABASE apply_goal_students;

-- The service will auto-create tables in development
-- For production, run migrations
```

## Running the Service

```bash
# Development
npm run start:dev students-service

# Production
npm run start:prod students-service
```

## gRPC Proto Definition

The service implements the `students.proto` definition:

```protobuf
service StudentService {
  rpc CreateStudent(CreateStudentRequest) returns (CreateStudentResponse);
  rpc GetStudent(GetStudentRequest) returns (GetStudentResponse);
  rpc UpdateStudent(UpdateStudentRequest) returns (UpdateStudentResponse);
  rpc DeleteStudent(DeleteStudentRequest) returns (DeleteStudentResponse);
  rpc ListStudents(ListStudentsRequest) returns (ListStudentsResponse);
  
  rpc EnrollInCourse(EnrollInCourseRequest) returns (EnrollInCourseResponse);
  rpc DropCourse(DropCourseRequest) returns (DropCourseResponse);
  rpc GetEnrollments(GetEnrollmentsRequest) returns (GetEnrollmentsResponse);
  rpc UpdateGrades(UpdateGradesRequest) returns (UpdateGradesResponse);
  rpc GetAcademicProgress(GetAcademicProgressRequest) returns (GetAcademicProgressResponse);
  rpc GetTranscript(GetTranscriptRequest) returns (GetTranscriptResponse);
}
```

## Business Logic

### GPA Calculation
- Uses 4.0 scale by default
- Calculates cumulative GPA based on all completed courses
- Updates automatically when grades are modified
- Supports quality points calculation

### Academic Standing
- Summa Cum Laude: GPA >= 3.8
- Magna Cum Laude: GPA >= 3.6
- Cum Laude: GPA >= 3.4
- Good Standing: GPA >= 3.0
- Satisfactory: GPA >= 2.0
- Probation: GPA >= 1.0
- Academic Warning: GPA < 1.0

### Student ID Generation
- Format: ST{YY}{NNNN} (e.g., ST24001)
- YY = Last two digits of current year
- NNNN = Sequential 4-digit number

## Monitoring

- **Metrics**: Exposed on port 5008
- **Health Check**: Available at /health
- **Tracing**: Integrated with Jaeger
- **Logging**: Structured logging with correlation IDs

## Error Handling

The service provides comprehensive error handling:
- Input validation
- Database constraint violations
- Business rule violations
- Proper gRPC status codes

## Development

### Adding New Features

1. Update proto definition
2. Regenerate gRPC types
3. Implement service methods
4. Add database entities if needed
5. Write tests

### Database Migrations

For production deployments:

```bash
# Generate migration
npm run migration:generate -- -n MigrationName

# Run migrations
npm run migration:run

# Revert migration
npm run migration:revert
```

## Testing

```bash
# Unit tests
npm run test students-service

# E2E tests
npm run test:e2e students-service

# Test coverage
npm run test:cov students-service
```

## Performance Considerations

- Database indexes for common queries
- Pagination for large result sets
- Connection pooling
- Query optimization
- Caching strategies for frequently accessed data

## Security

- Input validation and sanitization
- SQL injection prevention via TypeORM
- Proper error handling to prevent information leakage
- Database connection security
