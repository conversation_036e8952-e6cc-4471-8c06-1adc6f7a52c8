import { Injectable, OnModuleInit } from '@nestjs/common';
import { Client, ClientGrpc } from '@nestjs/microservices';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import { join } from 'path';
import { Observable } from 'rxjs';

interface GetAgencyResponse {
  name: string;
}

interface AgencyService {
  getAgency(request: { id: string }): Observable<GetAgencyResponse>;
}

@Injectable()
export class AgencyClientService implements OnModuleInit {
  @Client({
    transport: Transport.GRPC,
    options: {
      package: 'agency',
      protoPath: join(process.cwd(), 'libs/shared/dto/src/lib/agency/agency.proto'),
      url: 'agency-service:50051', // Update to use service name from docker-compose
    },
  })
  private readonly client: ClientGrpc;

  private agencyService: AgencyService;

  onModuleInit() {
    this.agencyService = this.client.getService<AgencyService>('AgencyService');
  }

  getAgency(id: string): Observable<GetAgencyResponse> {
    return this.agencyService.getAgency({ id });
  }
}
