# Student API Gateway Environment Configuration

# Server Configuration
PORT=4007
NODE_ENV=development
CORS_ORIGIN=*

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=1h
JWT_REFRESH_EXPIRES_IN=7d

# Service URLs (gRPC)
STUDENTS_SERVICE_URL=localhost:50058
AUTH_SERVICE_URL=localhost:50051
UNIVERSITY_SERVICE_URL=localhost:50059
AGENCY_SERVICE_URL=localhost:50060

# Monitoring Configuration
METRICS_PORT=4007
JAEGER_ENDPOINT=http://jaeger:4318/v1/traces

# Logging
LOG_LEVEL=info

# API Documentation
SWAGGER_ENABLED=true
SWAGGER_PATH=/api/docs

# Rate Limiting
RATE_LIMIT_TTL=60
RATE_LIMIT_LIMIT=100

# Health Check
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_PATH=/health
