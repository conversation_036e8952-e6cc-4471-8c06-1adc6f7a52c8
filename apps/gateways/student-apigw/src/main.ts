/**
 * Student API Gateway
 * Connects to student-service, auth-service, university-service, and agency-service
 */

import { Logger, ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { AppModule } from './app/app.module';
import express from 'express';
import { healthRouter } from './routes/health.routes';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Add express middleware for health routes
  app.use(express.json());
  app.use('/health', healthRouter);

  // Global prefix
  const globalPrefix = 'api';
  app.setGlobalPrefix(globalPrefix);

  // Enable CORS
  app.enableCors({
    origin: process.env.CORS_ORIGIN || '*',
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    credentials: true,
  });

  // Global validation pipe
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
    })
  );

  // Swagger documentation
  const config = new DocumentBuilder()
    .setTitle('Student API Gateway')
    .setDescription(
      'API Gateway for student management, connecting to multiple services'
    )
    .setVersion('1.0')
    .addBearerAuth()
    .addTag('students', 'Student management operations')
    .addTag(
      'academic',
      'Academic operations (enrollments, grades, transcripts)'
    )
    .addTag('universities', 'University information')
    .addTag('agencies', 'Agency information')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup(`${globalPrefix}/docs`, app, document);

  // Start server
  const port = process.env.PORT || 4007;
  await app.listen(port);

  Logger.log(
    `🚀 Student API Gateway is running on: http://localhost:${port}/${globalPrefix}`
  );
  Logger.log(
    `📚 API Documentation: http://localhost:${port}/${globalPrefix}/docs`
  );
  Logger.log(
    `📊 Metrics endpoint: http://localhost:${port}/${globalPrefix}/metrics`
  );
  Logger.log(`🔗 Connected services:`);
  Logger.log(
    `  - Students Service: ${
      process.env.STUDENTS_SERVICE_URL || 'localhost:50058'
    }`
  );
  Logger.log(
    `  - Auth Service: ${process.env.AUTH_SERVICE_URL || 'localhost:50051'}`
  );
  Logger.log(
    `  - University Service: ${
      process.env.UNIVERSITY_SERVICE_URL || 'localhost:50059'
    }`
  );
  Logger.log(
    `  - Agency Service: ${process.env.AGENCY_SERVICE_URL || 'localhost:50060'}`
  );
}

bootstrap().catch((error) => {
  Logger.error('Failed to start Student API Gateway:', error);
  process.exit(1);
});
