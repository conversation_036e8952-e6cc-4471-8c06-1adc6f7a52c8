import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import {
  Client,
  ClientGrpc,
  RpcException,
  Transport,
} from '@nestjs/microservices';
import { join } from 'path';
import { firstValueFrom, Observable, map, catchError, throwError } from 'rxjs';

// Student Service interfaces
interface StudentService {
  createStudent(data: any): Observable<any>;
  getStudent(data: any): Observable<any>;
  updateStudent(data: any): Observable<any>;
  deleteStudent(data: any): Observable<any>;
  listStudents(data: any): Observable<any>;
  enrollInCourse(data: any): Observable<any>;
  dropCourse(data: any): Observable<any>;
  getEnrollments(data: any): Observable<any>;
  updateGrades(data: any): Observable<any>;
  getAcademicProgress(data: any): Observable<any>;
  getTranscript(data: any): Observable<any>;
}

// Auth Service interfaces
interface AuthService {
  validateToken(data: any): Observable<any>;
  getUserById(data: any): Observable<any>;
  getUserRoles(data: any): Observable<any>;
}

// University Service interfaces
interface UniversityService {
  getUniversity(data: any): Observable<any>;
  listUniversities(data: any): Observable<any>;
  getCourses(data: any): Observable<any>;
  getPrograms(data: any): Observable<any>;
}

// Agency Service interfaces
interface AgencyService {
  getAgency(data: any): Observable<any>;
  listAgencies(data: any): Observable<any>;
  getAgencyStudents(data: any): Observable<any>;
}

@Injectable()
export class StudentClientService implements OnModuleInit {
  private readonly logger = new Logger(StudentClientService.name);
  
  private studentService: StudentService;
  private authService: AuthService;
  private universityService: UniversityService;
  private agencyService: AgencyService;

  // gRPC Clients
  @Client({
    transport: Transport.GRPC,
    options: {
      package: 'students',
      protoPath: join(process.cwd(), 'libs/shared/dto/src/lib/students/students.proto'),
      url: process.env.STUDENTS_SERVICE_URL || 'localhost:50058',
    },
  })
  private studentsClient: ClientGrpc;

  @Client({
    transport: Transport.GRPC,
    options: {
      package: 'auth',
      protoPath: join(process.cwd(), 'libs/shared/dto/src/lib/auth/auth.proto'),
      url: process.env.AUTH_SERVICE_URL || 'localhost:50051',
    },
  })
  private authClient: ClientGrpc;

  @Client({
    transport: Transport.GRPC,
    options: {
      package: 'university',
      protoPath: join(process.cwd(), 'libs/shared/dto/src/lib/university/university.proto'),
      url: process.env.UNIVERSITY_SERVICE_URL || 'localhost:50059',
    },
  })
  private universityClient: ClientGrpc;

  @Client({
    transport: Transport.GRPC,
    options: {
      package: 'agency',
      protoPath: join(process.cwd(), 'libs/shared/dto/src/lib/agency/agency.proto'),
      url: process.env.AGENCY_SERVICE_URL || 'localhost:50060',
    },
  })
  private agencyClient: ClientGrpc;

  onModuleInit() {
    this.studentService = this.studentsClient.getService<StudentService>('StudentService');
    this.authService = this.authClient.getService<AuthService>('AuthService');
    this.universityService = this.universityClient.getService<UniversityService>('UniversityService');
    this.agencyService = this.agencyClient.getService<AgencyService>('AgencyService');
  }

  // Student Service Methods
  async createStudent(studentData: any): Promise<any> {
    try {
      return await firstValueFrom(
        this.studentService.createStudent(studentData).pipe(
          catchError((error) => {
            this.logger.error('Error creating student:', error);
            throw new RpcException(error);
          })
        )
      );
    } catch (error) {
      this.logger.error('Failed to create student:', error);
      throw error;
    }
  }

  async getStudent(id: string): Promise<any> {
    try {
      return await firstValueFrom(
        this.studentService.getStudent({ id }).pipe(
          catchError((error) => {
            this.logger.error('Error getting student:', error);
            throw new RpcException(error);
          })
        )
      );
    } catch (error) {
      this.logger.error('Failed to get student:', error);
      throw error;
    }
  }

  async updateStudent(id: string, studentData: any): Promise<any> {
    try {
      return await firstValueFrom(
        this.studentService.updateStudent({ id, student: studentData }).pipe(
          catchError((error) => {
            this.logger.error('Error updating student:', error);
            throw new RpcException(error);
          })
        )
      );
    } catch (error) {
      this.logger.error('Failed to update student:', error);
      throw error;
    }
  }

  async deleteStudent(id: string): Promise<any> {
    try {
      return await firstValueFrom(
        this.studentService.deleteStudent({ id }).pipe(
          catchError((error) => {
            this.logger.error('Error deleting student:', error);
            throw new RpcException(error);
          })
        )
      );
    } catch (error) {
      this.logger.error('Failed to delete student:', error);
      throw error;
    }
  }

  async listStudents(filters: any): Promise<any> {
    try {
      return await firstValueFrom(
        this.studentService.listStudents(filters).pipe(
          catchError((error) => {
            this.logger.error('Error listing students:', error);
            throw new RpcException(error);
          })
        )
      );
    } catch (error) {
      this.logger.error('Failed to list students:', error);
      throw error;
    }
  }

  // Academic Operations
  async enrollInCourse(studentId: string, courseId: string, semester: string): Promise<any> {
    try {
      return await firstValueFrom(
        this.studentService.enrollInCourse({ student_id: studentId, course_id: courseId, semester }).pipe(
          catchError((error) => {
            this.logger.error('Error enrolling in course:', error);
            throw new RpcException(error);
          })
        )
      );
    } catch (error) {
      this.logger.error('Failed to enroll in course:', error);
      throw error;
    }
  }

  async dropCourse(studentId: string, courseId: string, semester: string): Promise<any> {
    try {
      return await firstValueFrom(
        this.studentService.dropCourse({ student_id: studentId, course_id: courseId, semester }).pipe(
          catchError((error) => {
            this.logger.error('Error dropping course:', error);
            throw new RpcException(error);
          })
        )
      );
    } catch (error) {
      this.logger.error('Failed to drop course:', error);
      throw error;
    }
  }

  async getEnrollments(studentId: string, semester?: string): Promise<any> {
    try {
      return await firstValueFrom(
        this.studentService.getEnrollments({ student_id: studentId, semester }).pipe(
          catchError((error) => {
            this.logger.error('Error getting enrollments:', error);
            throw new RpcException(error);
          })
        )
      );
    } catch (error) {
      this.logger.error('Failed to get enrollments:', error);
      throw error;
    }
  }

  async updateGrades(studentId: string, courseId: string, grade: string): Promise<any> {
    try {
      return await firstValueFrom(
        this.studentService.updateGrades({ student_id: studentId, course_id: courseId, grade }).pipe(
          catchError((error) => {
            this.logger.error('Error updating grades:', error);
            throw new RpcException(error);
          })
        )
      );
    } catch (error) {
      this.logger.error('Failed to update grades:', error);
      throw error;
    }
  }

  async getAcademicProgress(studentId: string): Promise<any> {
    try {
      return await firstValueFrom(
        this.studentService.getAcademicProgress({ student_id: studentId }).pipe(
          catchError((error) => {
            this.logger.error('Error getting academic progress:', error);
            throw new RpcException(error);
          })
        )
      );
    } catch (error) {
      this.logger.error('Failed to get academic progress:', error);
      throw error;
    }
  }

  async getTranscript(studentId: string): Promise<any> {
    try {
      return await firstValueFrom(
        this.studentService.getTranscript({ student_id: studentId }).pipe(
          catchError((error) => {
            this.logger.error('Error getting transcript:', error);
            throw new RpcException(error);
          })
        )
      );
    } catch (error) {
      this.logger.error('Failed to get transcript:', error);
      throw error;
    }
  }

  // Auth Service Methods
  async validateToken(token: string): Promise<any> {
    try {
      return await firstValueFrom(
        this.authService.validateToken({ token }).pipe(
          catchError((error) => {
            this.logger.error('Error validating token:', error);
            throw new RpcException(error);
          })
        )
      );
    } catch (error) {
      this.logger.error('Failed to validate token:', error);
      throw error;
    }
  }

  async getUserById(userId: string): Promise<any> {
    try {
      return await firstValueFrom(
        this.authService.getUserById({ id: userId }).pipe(
          catchError((error) => {
            this.logger.error('Error getting user by ID:', error);
            throw new RpcException(error);
          })
        )
      );
    } catch (error) {
      this.logger.error('Failed to get user by ID:', error);
      throw error;
    }
  }

  // University Service Methods
  async getUniversity(universityId: string): Promise<any> {
    try {
      return await firstValueFrom(
        this.universityService.getUniversity({ id: universityId }).pipe(
          catchError((error) => {
            this.logger.error('Error getting university:', error);
            throw new RpcException(error);
          })
        )
      );
    } catch (error) {
      this.logger.error('Failed to get university:', error);
      throw error;
    }
  }

  async listUniversities(filters: any): Promise<any> {
    try {
      return await firstValueFrom(
        this.universityService.listUniversities(filters).pipe(
          catchError((error) => {
            this.logger.error('Error listing universities:', error);
            throw new RpcException(error);
          })
        )
      );
    } catch (error) {
      this.logger.error('Failed to list universities:', error);
      throw error;
    }
  }

  // Agency Service Methods
  async getAgency(agencyId: string): Promise<any> {
    try {
      return await firstValueFrom(
        this.agencyService.getAgency({ id: agencyId }).pipe(
          catchError((error) => {
            this.logger.error('Error getting agency:', error);
            throw new RpcException(error);
          })
        )
      );
    } catch (error) {
      this.logger.error('Failed to get agency:', error);
      throw error;
    }
  }

  async listAgencies(filters: any): Promise<any> {
    try {
      return await firstValueFrom(
        this.agencyService.listAgencies(filters).pipe(
          catchError((error) => {
            this.logger.error('Error listing agencies:', error);
            throw new RpcException(error);
          })
        )
      );
    } catch (error) {
      this.logger.error('Failed to list agencies:', error);
      throw error;
    }
  }
}
