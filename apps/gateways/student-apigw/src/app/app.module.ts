import { Module, MiddlewareConsumer, NestModule } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { StudentController } from './student.controller';
import { StudentClientService } from './student.service';
import { MonitoringModule } from '@apply-goal-backend/monitoring';
import { MetricsController } from './metrics.controller';
import { MetricsMiddleware } from './middleware/metrics.middleware';
import { AuthenticationModule } from '@apply-goal-backend/auth';

@Module({
  imports: [
    MonitoringModule.forRoot({
      metrics: {
        serviceName: 'student-apigw',
        serviceVersion: '1.0.0',
        port: parseInt(process.env.PORT || '4007', 10), // Use the same port as the app
        path: '/api/metrics',
        labels: {
          environment: process.env.NODE_ENV || 'development',
          service_type: 'gateway',
        },
      },
      tracing: {
        serviceName: 'student-apigw',
        jaegerEndpoint: 'http://jaeger:4318/v1/traces',
      },
    }),
    AuthenticationModule.forRoot({
      secret: process.env.JWT_SECRET || 'secret',
      expiresIn: '1h',
      refreshExpiresIn: '7d',
    }),
  ],
  controllers: [AppController, StudentController, MetricsController],
  providers: [AppService, StudentClientService],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(MetricsMiddleware)
      .exclude('/api/metrics') // Exclude metrics endpoint to avoid recursive tracking
      .forRoutes('*');
  }
}
