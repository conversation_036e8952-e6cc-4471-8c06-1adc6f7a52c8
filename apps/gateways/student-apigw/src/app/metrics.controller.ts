import { <PERSON>, Get, Res } from '@nestjs/common';
import { Response } from 'express';
import { MetricsService } from '@apply-goal-backend/monitoring';
import { Public } from '@apply-goal-backend/auth';

@Controller('metrics')
export class MetricsController {
  constructor(private readonly metricsService: MetricsService) {}

  @Public()
  @Get()
  async getMetrics(@Res() res: Response) {
    const metrics = await this.metricsService.getMetrics();
    res.set('Content-Type', 'text/plain');
    res.send(metrics);
  }
}
