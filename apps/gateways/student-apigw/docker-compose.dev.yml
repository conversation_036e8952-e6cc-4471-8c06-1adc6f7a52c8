# Development Docker Compose for Student API Gateway and related services
version: '3.8'

services:
  # Student API Gateway
  student-apigw:
    build:
      context: ../../..
      dockerfile: apps/gateways/student-apigw/Dockerfile
    ports:
      - "4007:4007"
    environment:
      - NODE_ENV=development
      - PORT=4007
      - STUDENTS_SERVICE_URL=students-service:50058
      - AUTH_SERVICE_URL=auth-service:50051
      - UNIVERSITY_SERVICE_URL=university-service:50059
      - AGENCY_SERVICE_URL=agency-service:50060
      - JWT_SECRET=dev-secret-key
      - CORS_ORIGIN=*
    depends_on:
      - students-service
      - postgres-students
    networks:
      - student-network

  # Students Service
  students-service:
    build:
      context: ../../..
      dockerfile: apps/services/students-service/Dockerfile
    ports:
      - "50058:50058"
      - "5008:5008"  # Metrics port
    environment:
      - NODE_ENV=development
      - GRPC_URL=0.0.0.0:50058
      - DB_HOST=postgres-students
      - DB_PORT=5432
      - DB_USERNAME=postgres
      - DB_PASSWORD=password
      - DB_NAME=apply_goal_students
      - DB_SYNCHRONIZE=true
      - DB_LOGGING=true
      - METRICS_PORT=5008
    depends_on:
      - postgres-students
    networks:
      - student-network

  # PostgreSQL for Students Service
  postgres-students:
    image: postgres:15-alpine
    ports:
      - "5433:5432"  # Different port to avoid conflicts
    environment:
      - POSTGRES_DB=apply_goal_students
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_students_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - student-network

  # Redis for caching (optional)
  redis-students:
    image: redis:7-alpine
    ports:
      - "6380:6379"  # Different port to avoid conflicts
    networks:
      - student-network

  # Jaeger for tracing
  jaeger:
    image: jaegertracing/all-in-one:latest
    ports:
      - "16686:16686"  # Jaeger UI
      - "4318:4318"    # OTLP HTTP receiver
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    networks:
      - student-network

volumes:
  postgres_students_data:

networks:
  student-network:
    driver: bridge
