# Student API Gateway

The Student API Gateway is a comprehensive REST API that connects to multiple microservices to provide student management functionality.

## Connected Services

This gateway connects to the following services via gRPC:

1. **Students Service** (`localhost:50058`) - Core student management
2. **Auth Service** (`localhost:50051`) - Authentication and authorization
3. **University Service** (`localhost:50059`) - University information
4. **Agency Service** (`localhost:50060`) - Agency information

## Features

### Student Management
- Create, read, update, delete students
- Student profile management
- Emergency contact management
- Academic status tracking

### Academic Operations
- Course enrollment and dropping
- Grade management
- Academic progress tracking
- Transcript generation
- GPA calculation

### Integration Features
- University information lookup
- Agency information lookup
- Authentication and authorization
- Audit logging with user tracking
- Comprehensive error handling

## API Endpoints

### Student CRUD
- `POST /api/students` - Create a new student
- `GET /api/students/:id` - Get student by ID
- `PUT /api/students/:id` - Update student
- `DELETE /api/students/:id` - Delete student
- `GET /api/students` - List students with filtering

### Academic Operations
- `POST /api/students/:studentId/enrollments` - Enroll in course
- `DELETE /api/students/:studentId/enrollments/:courseId` - Drop course
- `GET /api/students/:studentId/enrollments` - Get enrollments
- `PUT /api/students/:studentId/grades` - Update grades
- `GET /api/students/:studentId/academic-progress` - Get academic progress
- `GET /api/students/:studentId/transcript` - Get transcript

### University Integration
- `GET /api/students/universities/:universityId` - Get university info
- `GET /api/students/universities` - List universities

### Agency Integration
- `GET /api/students/agencies/:agencyId` - Get agency info
- `GET /api/students/agencies` - List agencies

## Authentication

All endpoints require JWT authentication with appropriate permissions:

- `StudentProfileManagement:View` - View student profiles
- `StudentProfileManagement:Edit` - Edit student profiles
- `StudentProfileManagement:Create` - Create student profiles
- `StudentProfileManagement:Delete` - Delete student profiles
- `StudentAcademic:View` - View academic information
- `StudentAcademic:Edit` - Edit academic information
- `StudentEnrollment:Manage` - Manage enrollments

## Audit Logging

All endpoints include user tracking parameters for audit logging:
- `@CurrentUser('id')` - Current user ID
- `@CurrentUser('roles')` - Current user roles
- `@Ip()` - Client IP address
- `@Headers('user-agent')` - Client user agent

## Configuration

Copy `.env.example` to `.env` and configure:

```bash
cp .env.example .env
```

Key configuration options:
- `PORT` - API Gateway port (default: 4007)
- `STUDENTS_SERVICE_URL` - Students service gRPC URL
- `AUTH_SERVICE_URL` - Auth service gRPC URL
- `UNIVERSITY_SERVICE_URL` - University service gRPC URL
- `AGENCY_SERVICE_URL` - Agency service gRPC URL
- `JWT_SECRET` - JWT secret for authentication

## Running the Service

```bash
# Development
npm run start:dev student-apigw

# Production
npm run start:prod student-apigw
```

## API Documentation

Swagger documentation is available at:
- Development: http://localhost:4007/api/docs
- Production: Configure SWAGGER_PATH environment variable

## Monitoring

- **Metrics**: http://localhost:4007/api/metrics
- **Health Check**: http://localhost:4007/health
- **Tracing**: Configured for Jaeger integration

## Error Handling

The gateway provides comprehensive error handling:
- gRPC errors are converted to appropriate HTTP status codes
- Detailed error messages for debugging
- Proper error logging for monitoring

## Development

### Adding New Endpoints

1. Add method to `StudentClientService`
2. Add endpoint to `StudentController`
3. Update permissions as needed
4. Add tests

### Service Integration

To integrate with additional services:
1. Add gRPC client configuration in `StudentClientService`
2. Define service interface
3. Add service methods
4. Create controller endpoints

## Testing

```bash
# Unit tests
npm run test student-apigw

# E2E tests
npm run test:e2e student-apigw

# Test coverage
npm run test:cov student-apigw
```
