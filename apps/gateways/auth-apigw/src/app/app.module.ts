import { Module, MiddlewareConsumer, NestModule } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AuthController } from './auth.controller';
import { AuthClientService } from './auth.service';
import { MonitoringModule } from '@apply-goal-backend/monitoring';
import { MetricsController } from './metrics.controller';
import { MetricsMiddleware } from './middleware/metrics.middleware';
import { UploadModule } from '@apply-goal-backend/common';
import { UploadController } from './upload.controller';
import { AuthenticationModule } from '@apply-goal-backend/auth';

@Module({
  imports: [
    MonitoringModule.forRoot({
      metrics: {
        serviceName: 'auth-apigw',
        serviceVersion: '1.0.0',
        port: parseInt(process.env.PORT || '4006', 10), // Use the same port as the app
        path: '/api/metrics',
        labels: {
          environment: process.env.NODE_ENV || 'development',
          service_type: 'gateway',
        },
      },
      tracing: {
        serviceName: 'auth-apigw',
        jaegerEndpoint: 'http://jaeger:4318/v1/traces',
      },
    }),
    UploadModule.forRoot({
      provider: 's3',
      s3: {
        endpoint: process.env.S3_ENDPOINT,
        accessKey: process.env.S3_ACCESS_KEY,
        secretKey: process.env.S3_SECRET_KEY,
        bucket: process.env.S3_BUCKET,
        region: process.env.S3_REGION,
        forcePathStyle: process.env.S3_FORCE_PATH_STYLE === 'true',
        publicEndpoint:
          process.env.S3_PUBLIC_ENDPOINT || process.env.S3_ENDPOINT,
      },
    }),
    AuthenticationModule.forRoot({
      secret: process.env.JWT_SECRET || 'secret',
      expiresIn: '1h',
      refreshExpiresIn: '7d',
    }),
  ],
  controllers: [
    AppController,
    AuthController,
    MetricsController,
    UploadController,
  ],
  providers: [AppService, AuthClientService],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(MetricsMiddleware)
      .exclude('/api/metrics') // Exclude metrics endpoint to avoid recursive tracking
      .forRoutes('*');
  }
}
