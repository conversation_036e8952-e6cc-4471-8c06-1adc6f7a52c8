import Long from 'long';
import { DepartmentInfo, DepartmentWithUsersInfo } from '../auth.interface';

export function serializeUserResponse(user: any) {
  if (!user) return null;

  const userId = toNum(user.id);
  const name = user.name ?? '';
  const email = user.email ?? '';

  const rawRole = Array.isArray(user.roles) ? user.roles[0] : null;
  const roleId = toNum(rawRole?.id);
  const roleName = rawRole?.name?.replace(/\s+/g, '') ?? '';

  const modulesMap: Record<
    number,
    {
      id: number;
      name: string;
      features: Array<{
        id: number;
        name: string;
        permission: boolean;
        subFeatures: Array<{ id: number; name: string; permission: boolean }>;
      }>;
    }
  > = {};

  function ensureModule(mid: number, mName: string) {
    if (!modulesMap[mid]) {
      modulesMap[mid] = { id: mid, name: mName, features: [] };
    }
    return modulesMap[mid];
  }

  // ← here’s the fix: read `.isAllowed` on RoleFeaturePermission
  for (const feat of rawRole?.features || []) {
    if (!feat?.module) continue;
    const mid = toNum(feat.module.id);
    const mb = ensureModule(mid, feat.module.name ?? '');
    mb.features.push({
      id: toNum(feat.id),
      name: feat.name ?? '',
      permission: feat.permission,
      subFeatures: [],
    });
  }

  // ← and ditto for sub-features
  for (const sf of rawRole?.subFeatures || []) {
    if (!sf?.feature) continue;
    const parentId = toNum(sf.feature.id);
    const mid = toNum(sf.feature.moduleId);
    const mb = modulesMap[mid];
    const fx = mb?.features.find((f) => f.id === parentId);
    if (!fx) continue;
    fx.subFeatures.push({
      id: toNum(sf.id),
      name: sf.name ?? '',
      permission: sf.permission,
    });
  }

  const modules = Object.values(modulesMap);
  const departments = (user.departments || []).map((d: any) => ({
    id: toNum(d.id),
    name: d.name ?? '',
  }));

  return {
    id: userId,
    name,
    email,
    roles: {
      id: roleId,
      name: roleName,
      modules,
    },
    departments,
  };
}

export function toNumber(x: any): number {
  // if it’s a Long.js object:
  if (Long.isLong(x)) {
    return x.toNumber();
  }
  // else assume it’s already a number
  return x;
}

// helper to cast Long → number in the serializer
export function longReplacer(key: string, value: any): any {
  if (Long.isLong(value)) {
    return value.toNumber();
  }
  return value;
}

export function serializeDepartmentIds(
  departments: DepartmentInfo[]
): DepartmentInfo[] {
  return (departments || []).map((dept) => {
    // — normalize the id
    const idNum = Number(dept.id);
    if (isNaN(idNum) || !Number.isSafeInteger(idNum)) {
      throw new Error(`Invalid or unsafe department ID: ${dept.id}`);
    }

    // — recursively normalize children
    const children = dept.children
      ? serializeDepartmentIds(dept.children)
      : undefined;

    // — normalize parentId
    const parentIdNum =
      dept.parentId != null ? Number(dept.parentId) : undefined;
    if (
      dept.parentId != null &&
      (isNaN(parentIdNum!) || !Number.isSafeInteger(parentIdNum!))
    ) {
      throw new Error(`Invalid or unsafe parent ID: ${dept.parentId}`);
    }

    return {
      ...dept,
      id: idNum,
      parentId: parentIdNum,
      children,
    };
  });
}

export function serializeDeptAndUserIds(
  d: DepartmentWithUsersInfo
): DepartmentWithUsersInfo {
  const id = Number(d.id);
  if (!Number.isSafeInteger(id)) {
    throw new Error(`Invalid department ID: ${d.id}`);
  }

  const parentId = d.parentId != null ? Number(d.parentId) : undefined;
  if (d.parentId != null && !Number.isSafeInteger(parentId!)) {
    throw new Error(`Invalid parent ID: ${d.parentId}`);
  }

  // normalize users
  const users = (d.users || []).map((u) => {
    const uid = Number(u.id);
    if (!Number.isSafeInteger(uid)) {
      throw new Error(`Invalid user ID: ${u.id}`);
    }
    return { ...u, id: uid };
  });

  // recurse children
  const children = (d.children || []).map(serializeDeptAndUserIds);

  return {
    ...d,
    id,
    parentId,
    users,
    children,
  };
}

export const toNum = (raw: any): number =>
  Long.isLong(raw) ? raw.toNumber() : Number(raw);
