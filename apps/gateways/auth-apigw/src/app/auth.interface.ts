import { Observable } from 'rxjs';

// ==== Authentication messages ====
// #region
export interface LoginRequest {
  email: string;
  password: string;
  ipAddress?: string;
  userAgent?: string;
}
export interface LoginResponse {
  success: boolean;
  message: string;
  accessToken?: string;
  refreshToken?: string;
  user?: AuthUserInfo;
}

export interface RegisterRequest {
  name: string;
  nationality: string;
  organizationName: string;
  email: string;
  password: string;
  phone?: string;
  roleName: string;
  departmentName: string;
  ipAddress?: string;
  userAgent?: string;
}
export interface RegisterResponse {
  success: boolean;
  message: string;
}

export interface ValidateTokenRequest {
  token: string;
}
export interface ValidateTokenResponse {
  valid: boolean;
  message: string;
  user?: AuthUserInfo;
}

export interface LogoutRequest {
  accessToken: string;
}
export interface LogoutResponse {
  success: boolean;
  message: string;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}
export interface RefreshTokenResponse {
  success: boolean;
  message: string;
  accessToken?: string;
  refreshToken?: string;
}

export interface SsoAuthRequest {
  provider: string;
  token: string;
  email: string;
  name: string;
  ipAddress?: string;
  userAgent?: string;
}
export interface SsoAuthResponse {
  success: boolean;
  message: string;
  accessToken?: string;
  refreshToken?: string;
  user?: AuthUserInfo;
}
// ====Auth====
export interface AuthUserInfo {
  id: number;
  name: string;
  email: string;
  phone: string;
  socialLink: string;
  gender: string;
  nationality: string;
  presentAddress: string;
  presentCountry: string;
  presentState: string;
  presentCity: string;
  presentAddressZipcode: string;
  permanentAddress: string;
  permanentCountry: string;
  permanentState: string;
  permanentCity: string;
  permanentAddressZipcode: string;
  status: string;
  roles: AuthRoleDetailsInfo[];
  departments: AuthDepartmentWithUsersInfo[];
  socialSites: AuthSocialSiteInfo[];
}

export interface AuthSocialSiteInfo {
  id: number;
  domain: string;
  url: string;
}

export interface AuthDepartmentWithUsersInfo {
  id: number;
  name: string;
  parentId?: number;
  users: AuthDepartmentUserInfo[];
  children: AuthDepartmentWithUsersInfo[];
}

export interface AuthDepartmentUserInfo {
  id: number;
  name: string;
  email: string;
}

export interface AuthRoleDetailsInfo {
  id: number;
  role: string;
  department: AuthRoleDepartmentInfo[];
  modules: AuthRoleModuleInfo[];
}

export interface AuthRoleDepartmentInfo {
  id: number;
  name: string;
  users: AuthRoleUserInfo[];
}

export interface AuthRoleUserInfo {
  id: number;
  name: string;
  email: string;
}

export interface AuthRoleModuleInfo {
  id: number;
  module: string;
  features: AuthRoleFeaturePermission[];
}

export interface AuthRoleFeaturePermission {
  id: number;
  feature: string;
  permissions: boolean;
  subFeatures: AuthRoleSubFeaturePermission[];
}

export interface AuthRoleSubFeaturePermission {
  id: number;
  subFeature: string;
  permissions: boolean;
}

// ==== OTP ====

export interface GenerateOtpRequest {
  email: string;
  type: string;
  ipAddress?: string;
  userAgent?: string;
}
export interface GenerateOtpResponse {
  success: boolean;
  message: string;
  expiresAt?: string;
}

export interface VerifyOtpRequest {
  email: string;
  otp: string;
  type: string;
  ipAddress?: string;
  userAgent?: string;
}
export interface VerifyOtpResponse {
  success: boolean;
  message: string;
}

// ==== SocialSite ====

export interface SocialSiteInfo {
  id: number;
  domain: string;
  url: string;
}
// #endregion

// ==== Role Management messages ====
// #region
export interface CreateOrgRoleRequest {
  name: string;
  description?: string;
  organizationId?: number;
  isSystemRole?: boolean;
  permissions?: {
    featureId: number;
    hasPermission: boolean;
  }[];
}

export interface UpdateOrgRoleRequest {
  id: number;
  name?: string;
  description?: string;
  permissions?: {
    featureId: number;
    hasPermission: boolean;
  }[];
}

export interface OrgRoleResponse {
  success: boolean;
  message: string;
  role?: any;
}

export interface ListOrgRolesRequest {
  organizationId?: number;
  includeSystemRoles?: boolean;
  page?: number;
  limit?: number;
}

export interface ListOrgRolesResponse {
  success: boolean;
  message: string;
  roles: any[];
  total: number;
  page: number;
  limit: number;
}

export interface GetRoleRequest {
  id: number;
}

export interface DeleteRoleRequest {
  id: number;
}

export interface DeleteRoleResponse {
  success: boolean;
  message: string;
}

export interface GetOrganizationRolesRequest {
  organizationId: number;
}

export interface GetOrganizationRolesResponse {
  success: boolean;
  message: string;
  roles: any[];
}
// #endregion

// ==== Department Management messages ====
// #region
export interface CreateOrgDepartmentRequest {
  name: string;
  organizationId: number;
  parentId?: number;
  description?: string;
}

export interface UpdateOrgDepartmentRequest {
  id: number;
  name?: string;
  parentId?: number;
  description?: string;
}

export interface OrgDepartmentResponse {
  success: boolean;
  message: string;
  department?: any;
}

export interface ListOrgDepartmentsRequest {
  organizationId: number;
  parentId?: number;
  includeChildren?: boolean;
  page?: number;
  limit?: number;
}

export interface ListOrgDepartmentsResponse {
  success: boolean;
  message: string;
  departments: any[];
  total: number;
  page: number;
  limit: number;
}

export interface GetOrgDepartmentRequest {
  id: number;
}

export interface DeleteOrgDepartmentRequest {
  id: number;
}

export interface DeleteOrgDepartmentResponse {
  success: boolean;
  message: string;
}

export interface GetOrganizationDepartmentsRequest {
  organizationId: number;
  includeHierarchy?: boolean;
}

export interface GetOrganizationDepartmentsResponse {
  success: boolean;
  message: string;
  departments: any[];
}
// #endregion

// ==== Password Reset messages ====
// #region
export interface ForgotPasswordRequest {
  email: string;
  ipAddress?: string;
  userAgent?: string;
}

export interface ForgotPasswordResponse {
  success: boolean;
  message: string;
  resetToken?: string; // Only for development/testing
}

export interface ResetPasswordRequest {
  email: string;
  resetToken: string;
  newPassword: string;
  confirmPassword: string;
  ipAddress?: string;
  userAgent?: string;
}

export interface ResetPasswordResponse {
  success: boolean;
  message: string;
}

export interface VerifyResetTokenRequest {
  email: string;
  resetToken: string;
}

export interface VerifyResetTokenResponse {
  valid: boolean;
  message: string;
  expiresAt?: Date;
}
// #endregion

// ==== Employee Management messages ====
// #region
export interface CreateEmployeeRequest {
  // User creation fields
  email: string;
  password?: string;
  name?: string;
  phone?: string;
  departmentName?: string;
  organizationName?: string;
  employeeRoleName?: string; // Role for the employee user (e.g., "Employee", "Staff")

  // Employee specific fields
  userId?: number; // Optional - will be set after user creation
  lastName?: string;
  firstName?: string;
  joiningDate?: string;
  jobType?: string;
  jobStatus?: string;
  dateOfBirth?: string;
  bloodGroup?: string;
  gender?: string;
  agencyId?: number;
  orgId?: string;
  addresses?: EmployeeAddressInfo[];
  emergencyContacts?: EmployeeEmergencyContactInfo[];
  identityDocs?: EmployeeIdentityDocInfo[];
  bankAccounts?: EmployeeBankAccountInfo[];

  // Audit fields
  requestUserId: number;
  roleName: string;
  ipAddress: string;
  userAgent: string;
}

export interface UpdateEmployeeRequest {
  id: number;
  lastName?: string;
  firstName?: string;
  joiningDate?: string;
  jobType?: string;
  jobStatus?: string;
  dateOfBirth?: string;
  bloodGroup?: string;
  gender?: string;
  agencyId?: number;
  orgId?: string;
  addresses?: EmployeeAddressInfo[];
  emergencyContacts?: EmployeeEmergencyContactInfo[];
  identityDocs?: EmployeeIdentityDocInfo[];
  bankAccounts?: EmployeeBankAccountInfo[];
  requestUserId: number;
  roleName: string;
  ipAddress: string;
  userAgent: string;
}

export interface GetEmployeeRequest {
  id: number;
  requestUserId: number;
  roleName: string;
  ipAddress: string;
  userAgent: string;
}

export interface ListEmployeesRequest {
  page?: number;
  limit?: number;
  search?: string;
  jobType?: string;
  jobStatus?: string;
  agencyId?: number;
  orgId?: string;
  requestUserId: number;
  roleName: string;
  ipAddress: string;
  userAgent: string;
}

export interface RemoveEmployeeRequest {
  id: number;
  requestUserId: number;
  roleName: string;
  ipAddress: string;
  userAgent: string;
}

export interface EmployeeResponse {
  success: boolean;
  message: string;
  employee?: EmployeeInfo;
}

export interface ListEmployeesResponse {
  success: boolean;
  message: string;
  employees: EmployeeInfo[];
  total: number;
  page: number;
  limit: number;
}

export interface RemoveEmployeeResponse {
  success: boolean;
  message: string;
}

export interface EmployeeInfo {
  userId: number;
  lastName?: string;
  firstName?: string;
  joiningDate?: string;
  jobType?: string;
  jobStatus?: string;
  dateOfBirth?: string;
  bloodGroup?: string;
  gender?: string;
  agencyId?: number;
  orgId?: string;
  addresses?: EmployeeAddressInfo[];
  emergencyContacts?: EmployeeEmergencyContactInfo[];
  identityDocs?: EmployeeIdentityDocInfo[];
  bankAccounts?: EmployeeBankAccountInfo[];
  createdAt: string;
  updatedAt: string;
}

export interface EmployeeAddressInfo {
  id?: number;
  userId: number;
  addressType: string;
  addressLine?: string;
  country?: string;
  state?: string;
  city?: string;
  postalCode?: string;
}

export interface EmployeeEmergencyContactInfo {
  id?: number;
  userId: number;
  contactType: string;
  contactName?: string;
  contactPhone?: string;
  contactEmail?: string;
  relationship?: string;
}

export interface EmployeeIdentityDocInfo {
  id?: number;
  userId: number;
  docType: string;
  docNumber?: string;
  docUrl?: string;
  expiryDate?: string;
}

export interface EmployeeBankAccountInfo {
  id?: number;
  userId: number;
  bankName?: string;
  accountNumber?: string;
  routingNumber?: string;
  accountType?: string;
}
// #endregion

// ==== User Management messages ====
// #region
export interface CreateUserRequest {
  name: string;
  email: string;
  password: string;
  phone?: string;
  socialLink?: string;

  gender?: string;
  nationality?: string;

  presentAddress?: string;
  presentCountry?: string;
  presentState?: string;
  presentCity?: string;
  presentAddressZipcode?: string;

  permanentAddress?: string;
  permanentCountry?: string;
  permanentState?: string;
  permanentCity?: string;
  permanentAddressZipcode?: string;

  roleIds?: number[];
  departmentIds?: number[];
}
export interface GetUserRequest {
  id: number;
}
export interface UpdateUserRequest {
  id: number;

  name?: string;
  email?: string;
  password?: string;
  phone?: string;
  socialLink?: string;

  gender?: string;
  nationality?: string;

  presentAddress?: string;
  presentCountry?: string;
  presentState?: string;
  presentCity?: string;
  presentAddressZipcode?: string;

  permanentAddress?: string;
  permanentCountry?: string;
  permanentState?: string;
  permanentCity?: string;
  permanentAddressZipcode?: string;

  status?: string;

  roleIds?: number[];
  departmentIds?: number[];
}
export interface DeleteUserRequest {
  id: number;
}
export interface DeleteUserResponse {
  success: boolean;
  message: string;
}

export interface ListUsersRequest {
  page: number;
  limit: number;
  search?: string;
}
export interface ListUsersResponse {
  success: boolean;
  message: string;
  users: UserInfo[];
  total: number;
  page: number;
  limit: number;
}

export interface UserResponse {
  success: boolean;
  message: string;
  user?: UserInfo;
}

export interface UserInfo {
  id: number;
  name: string;
  email: string;
  phone?: string;
  socialLink?: string;

  gender?: string;
  nationality?: string;

  presentAddress?: string;
  presentCountry?: string;
  presentState?: string;
  presentCity?: string;
  presentAddressZipcode?: string;

  permanentAddress?: string;
  permanentCountry?: string;
  permanentState?: string;
  permanentCity?: string;
  permanentAddressZipcode?: string;

  status: string;

  roles: RoleInfo[];
  departments: DepartmentInfo[];
  socialSites: SocialSiteInfo[];
}
// #endregion

// ==== Role & Permission Management ====
// #region
export interface AssignRoleRequest {
  userId: number;
  roleId: number;
}
export interface AssignRoleResponse {
  success: boolean;
  message: string;
}
export interface RemoveRoleRequest {
  userId: number;
  roleId: number;
}
export interface RemoveRoleResponse {
  success: boolean;
  message: string;
}

export interface AssignPermissionRequest {
  roleId: number;
  permissionId: number;
}
export interface AssignPermissionResponse {
  success: boolean;
  message: string;
}
export interface RemovePermissionRequest {
  roleId: number;
  permissionId: number;
}
export interface RemovePermissionResponse {
  success: boolean;
  message: string;
}

export interface CreateRoleRequest {
  name: string;
  description?: string;
  organizationId?: number;
  isSystemRole?: boolean;
  permissions?: {
    featureId: number;
    hasPermission: boolean;
  }[];
}
export interface GetRoleRequest {
  id: number;
}
export interface UpdateRoleRequest {
  id: number;
  name?: string;
  description?: string;
  permissions?: {
    featureId: number;
    hasPermission: boolean;
  }[];
}
export interface DeleteRoleRequest {
  id: number;
}
export interface DeleteRoleResponse {
  success: boolean;
  message: string;
}

export interface ListRolesRequest {
  organizationId?: number;
  includeSystemRoles?: boolean;
  page?: number;
  limit?: number;
}
export interface ListRolesResponse {
  success: boolean;
  message: string;
  roles: any[];
  total: number;
  page: number;
  limit: number;
}

export interface RoleResponse {
  success: boolean;
  message: string;
  role?: any;
}

export interface RoleInfo {
  id: number;
  name: string;
  features: FeatureInfo[];
  subFeatures: SubFeatureInfo[];
  users: RoleUserInfo[];
}

export interface RoleUserInfo {
  id: number;
  name: string;
  email: string;
}

export interface GetRoleDetailsRequest {
  name: string;
  getArgs: GetDataRequest;
}

export interface RoleWithDetailsResponse {
  success: boolean;
  message: string;
  roles: RoleDetailsInfo[];
}
export interface RoleDetailsResponse {
  success: boolean;
  message: string;
  role: RoleDetailsInfo;
}

export interface RoleDetailsInfo {
  id: number;
  role: string;
  department: RoleDepartmentInfo[];
  modules: RoleModuleInfo[];
}

export interface RoleDepartmentInfo {
  id: number;
  name: string;
  users: RoleUserInfo[];
}

export interface RoleModuleInfo {
  id: number;
  module: string;
  features: RoleFeaturePermission[];
}

export interface RoleFeaturePermission {
  id: number;
  feature: string;
  permissions: boolean;
  subFeatures: RoleSubFeaturePermission[];
}

export interface RoleSubFeaturePermission {
  id: number;
  subFeature: string;
  permissions: boolean;
}

// Create Role
export interface CreateRoleWithDetailsRequest {
  id?: number;
  name: string;
  departments: CreateRoleDepartmentDetail[];
  modules: CreateRoleModuleDetail[];
  getArgs: GetDataRequest;
}

export interface CreateOrganizationRoleRequest {
  name: string;
  description?: string;
  organizationId: number;
  permissions: Array<{
    id: number;
    module: string;
    features: Array<{
      id: number;
      feature: string;
      permissions: boolean;
      subFeatures?: Array<{
        id: number;
        subFeature: string;
        permissions: boolean;
      }>;
    }>;
  }>;
}
export interface CreateRoleDepartmentDetail {
  id: number;
  name: string;
  users: CreateRoleUserDetail[];
}
export interface CreateRoleUserDetail {
  id: number;
  name: string;
  email: string;
}
export interface CreateRoleModuleDetail {
  id: number;
  features: CreateRoleFeaturePermission[];
}
export interface CreateRoleFeaturePermission {
  id: number;
  feature: string;
  permissions: boolean;
  subFeatures: CreateRoleSubFeaturePermission[];
}
export interface CreateRoleSubFeaturePermission {
  id: number;
  subFeature: string;
  permissions: boolean;
}

export interface CreateRoleWithDetailsResponse {
  success: boolean;
  message: string;
  role: CreateRoleInfoResponse;
}
export interface CreateRoleInfoResponse {
  id: number;
  name: string;
  description?: string;
  departments?: Array<{
    id: number;
    name: string;
    users: Array<{ id: number; name: string; email: string }>;
  }>;
  modules?: Array<{
    id: number;
    module: string;
    features: Array<{
      id: number;
      feature: string;
      permissions: boolean;
      subFeatures: Array<{
        id: number;
        subFeature: string;
        permissions: boolean;
      }>;
    }>;
  }>;
}

// Update Role Name
export interface UpdateRoleRequest {
  id: number;
  roleName: string;
  getArgs: GetDataRequest;
}
export interface UpdateRoleResponse {
  success: boolean;
  message: string;
}

export interface DeleteRoleRequest {
  id: number;
  getArgs: GetDataRequest;
}
export interface DeleteRoleResponse {
  success: boolean;
  message: string;
}

// #endregion

// ==== Module & Feature ====
// #region
export interface CreateModuleRequest {
  name: string;
  features: FeatureInput[];
}
export interface CreateModuleResponse {
  success: boolean;
  message: string;
  module?: ModuleInfo;
}

export interface ModuleInfo {
  id: number;
  name: string;
  features: FeatureInfo[];
}

export interface FeatureInput {
  name: string;
  subFeatures?: SubFeatureInput[];
}
export interface FeatureInfo {
  id: number;
  name: string;
  moduleId: number;
  module?: ModuleInfo;
  subFeatures: SubFeatureInfo[];
}

export interface SubFeatureInput {
  name: string;
}
export interface SubFeatureInfo {
  id: number;
  name: string;
  featureId: number;
  feature?: FeatureInfo;
}

export interface ListModulesResponse {
  success: boolean;
  message: string;
  modules: ModuleInfo[];
}

/** one module + its features */
export interface ModuleInput {
  name: string;
  features: FeatureInput[];
}

/** carry _all_ modules in one go */
export interface BulkCreateModulesRequest {
  modules: ModuleInput[];
  userId?: number;
  roleName?: string;
  ipAddress?: string;
  userAgent?: string;
}

/** bulk reply: success + each ModuleInfo */
export interface BulkCreateModulesResponse {
  success: boolean;
  message?: string;
  modules: ModuleInfo[];
}
// #endregion

// ==== Department ====
// #region
export interface CreateDepartmentRequest {
  departments: NewDepartmentInfo[];
  organizationId?: number;
  userId?: number;
  roleName?: string;
  ipAddress?: string;
  userAgent?: string;
}

export interface NewDepartmentInfo {
  name: string;
  parent?: string;
}

export interface BulkCreateDepartmentRequest {
  name: string;
  parent: string;
}
export interface CreateDepartmentResponse {
  success: boolean;
  message: string;
  department: DepartmentInfo;
}

export interface AssignDepartmentRequest {
  userId: number;
  departmentId: number;
}
export interface AssignDepartmentResponse {
  success: boolean;
  message: string;
}

export interface DepartmentInfo {
  id: number;
  name: string;
  parentId?: number;
  children?: DepartmentInfo[];
}

export interface GetDataRequest {
  userId: number;
  roleName: string;
  ipAddress: string;
  userAgent: string;
  includeHierarchy?: boolean;
  organizationId?: number;
}

export interface ListDepartmentsResponse {
  success: boolean;
  message: string;
  departments: any[];
}

export interface ListDepartmentsWithUsersResponse {
  success: boolean;
  message: string;
  departments: DepartmentWithUsersInfo[];
}

export interface DepartmentWithUsersInfo {
  id: number;
  name: string;
  parentId?: number;
  users: DepartmentUserInfo[];
  children?: DepartmentWithUsersInfo[];
}

export interface DepartmentUserInfo {
  id: number;
  name: string;
  email: string;
}
// #endregion

// ==== AuthService client ====

export interface AuthService {
  login(data: LoginRequest): Observable<LoginResponse>;
  register(data: RegisterRequest): Observable<RegisterResponse>;
  validateToken(data: ValidateTokenRequest): Observable<ValidateTokenResponse>;
  logout(data: LogoutRequest): Observable<LogoutResponse>;
  refreshToken(data: RefreshTokenRequest): Observable<RefreshTokenResponse>;
  ssoAuth(data: SsoAuthRequest): Observable<SsoAuthResponse>;

  generateOtp(data: GenerateOtpRequest): Observable<GenerateOtpResponse>;
  verifyOtp(data: VerifyOtpRequest): Observable<VerifyOtpResponse>;

  // modules
  createModule(data: CreateModuleRequest): Observable<CreateModuleResponse>;
  bulkCreateModules(
    data: BulkCreateModulesRequest
  ): Observable<BulkCreateModulesResponse>;
  listModules(getArgs: GetDataRequest): Observable<ListModulesResponse>;

  // departments
  createDepartment(
    data: CreateDepartmentRequest
  ): Observable<CreateDepartmentResponse>;
  assignDepartment(
    data: AssignDepartmentRequest
  ): Observable<AssignDepartmentResponse>;
  listDepartments(getArgs: GetDataRequest): Observable<DepartmentInfo[]>;
  listDepartmentsWithUsers(
    getArgs: GetDataRequest
  ): Observable<ListDepartmentsWithUsersResponse>;

  // Roles & Permissions
  createRoleWithDetails(
    data: CreateRoleWithDetailsRequest
  ): Observable<CreateRoleWithDetailsResponse>;
  getRolesWithDetails(
    getArgs: GetDataRequest
  ): Observable<RoleWithDetailsResponse>;
  getRoleDetails(data: GetRoleDetailsRequest): Observable<RoleDetailsResponse>;
  updateRole(data: UpdateRoleRequest): Observable<UpdateRoleResponse>;
  deleteRole(data: DeleteRoleRequest): Observable<DeleteRoleResponse>;

  // Organization-specific Role Management
  createRole(data: CreateRoleRequest): Observable<RoleResponse>;
  getRole(data: GetRoleRequest): Observable<RoleResponse>;
  listRoles(data: ListRolesRequest): Observable<ListRolesResponse>;
  updateRoleNew(data: UpdateRoleRequest): Observable<RoleResponse>;
  deleteRoleNew(data: DeleteRoleRequest): Observable<DeleteRoleResponse>;
  getOrganizationRoles(
    data: GetOrganizationRolesRequest
  ): Observable<GetOrganizationRolesResponse>;

  // Employee Management
  createEmployee(data: CreateEmployeeRequest): Observable<EmployeeResponse>;
  getEmployee(data: GetEmployeeRequest): Observable<EmployeeResponse>;
  listEmployees(data: ListEmployeesRequest): Observable<ListEmployeesResponse>;
  updateEmployee(data: UpdateEmployeeRequest): Observable<EmployeeResponse>;
  removeEmployee(
    data: RemoveEmployeeRequest
  ): Observable<RemoveEmployeeResponse>;

  // Organization-specific Department Management
  createOrgDepartment(
    data: CreateOrgDepartmentRequest
  ): Observable<OrgDepartmentResponse>;
  getOrgDepartment(
    data: GetOrgDepartmentRequest
  ): Observable<OrgDepartmentResponse>;
  listOrgDepartments(
    data: ListOrgDepartmentsRequest
  ): Observable<ListOrgDepartmentsResponse>;
  updateOrgDepartment(
    data: UpdateOrgDepartmentRequest
  ): Observable<OrgDepartmentResponse>;
  deleteOrgDepartment(
    data: DeleteOrgDepartmentRequest
  ): Observable<DeleteOrgDepartmentResponse>;
  getOrganizationDepartments(
    data: GetOrganizationDepartmentsRequest
  ): Observable<GetOrganizationDepartmentsResponse>;

  // Password Reset
  forgotPassword(
    data: ForgotPasswordRequest
  ): Observable<ForgotPasswordResponse>;
  verifyResetToken(
    data: VerifyResetTokenRequest
  ): Observable<VerifyResetTokenResponse>;
  resetPassword(data: ResetPasswordRequest): Observable<ResetPasswordResponse>;
}
