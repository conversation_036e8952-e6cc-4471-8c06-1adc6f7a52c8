import { Test, TestingModule } from '@nestjs/testing';
import { AuthController } from './auth.controller';
import { AuthClientService } from './auth.service';
import { of } from 'rxjs';
import { HttpException, HttpStatus } from '@nestjs/common';

describe('AuthController - Employee Endpoints', () => {
  let controller: AuthController;
  let authService: jest.Mocked<AuthClientService>;

  const mockEmployeeResponse = {
    success: true,
    message: 'Employee created successfully',
    employee: {
      userId: 1,
      firstName: 'John',
      lastName: 'Doe',
      jobType: 'Full-time',
      jobStatus: 'Active',
      createdAt: '2023-01-01T00:00:00Z',
      updatedAt: '2023-01-01T00:00:00Z',
    },
  };

  const mockListEmployeesResponse = {
    success: true,
    message: 'Employees retrieved successfully',
    employees: [mockEmployeeResponse.employee],
    total: 1,
    page: 1,
    limit: 10,
  };

  beforeEach(async () => {
    const mockAuthService = {
      createEmployee: jest.fn(),
      getEmployee: jest.fn(),
      listEmployees: jest.fn(),
      updateEmployee: jest.fn(),
      removeEmployee: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [AuthController],
      providers: [
        {
          provide: AuthClientService,
          useValue: mockAuthService,
        },
      ],
    }).compile();

    controller = module.get<AuthController>(AuthController);
    authService = module.get(AuthClientService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('createEmployee', () => {
    it('should create an employee successfully', async () => {
      const employeeData = {
        userId: 1,
        firstName: 'John',
        lastName: 'Doe',
        jobType: 'Full-time',
        jobStatus: 'Active',
      };

      authService.createEmployee.mockReturnValue(of(mockEmployeeResponse));

      const result = await controller.createEmployee(
        1, // userId
        ['Admin'], // roles
        '127.0.0.1', // ipAddress
        'test-agent', // userAgent
        employeeData
      );

      expect(result).toEqual(mockEmployeeResponse);
      expect(authService.createEmployee).toHaveBeenCalledWith({
        ...employeeData,
        requestUserId: 1,
        roleName: 'Admin',
        ipAddress: '127.0.0.1',
        userAgent: 'test-agent',
      });
    });

    it('should handle errors when creating employee', async () => {
      const employeeData = {
        userId: 1,
        firstName: 'John',
        lastName: 'Doe',
      };

      authService.createEmployee.mockImplementation(() => {
        throw new Error('Service error');
      });

      await expect(
        controller.createEmployee(
          1,
          ['Admin'],
          '127.0.0.1',
          'test-agent',
          employeeData
        )
      ).rejects.toThrow(HttpException);
    });
  });

  describe('getEmployee', () => {
    it('should get an employee successfully', async () => {
      authService.getEmployee.mockReturnValue(of(mockEmployeeResponse));

      const result = await controller.getEmployee(
        1, // id
        1, // userId
        ['Admin'], // roles
        '127.0.0.1', // ipAddress
        'test-agent' // userAgent
      );

      expect(result).toEqual(mockEmployeeResponse);
      expect(authService.getEmployee).toHaveBeenCalledWith({
        id: 1,
        requestUserId: 1,
        roleName: 'Admin',
        ipAddress: '127.0.0.1',
        userAgent: 'test-agent',
      });
    });
  });

  describe('listEmployees', () => {
    it('should list employees successfully', async () => {
      authService.listEmployees.mockReturnValue(of(mockListEmployeesResponse));

      const result = await controller.listEmployees(
        1, // userId
        ['Admin'], // roles
        '127.0.0.1', // ipAddress
        'test-agent', // userAgent
        1, // page
        10, // limit
        'John', // search
        'Full-time', // jobType
        'Active', // jobStatus
        1, // agencyId
        'org1' // orgId
      );

      expect(result).toEqual(mockListEmployeesResponse);
      expect(authService.listEmployees).toHaveBeenCalledWith({
        page: 1,
        limit: 10,
        search: 'John',
        jobType: 'Full-time',
        jobStatus: 'Active',
        agencyId: 1,
        orgId: 'org1',
        requestUserId: 1,
        roleName: 'Admin',
        ipAddress: '127.0.0.1',
        userAgent: 'test-agent',
      });
    });
  });

  describe('updateEmployee', () => {
    it('should update an employee successfully', async () => {
      const updateData = {
        firstName: 'Jane',
        lastName: 'Smith',
      };

      authService.updateEmployee.mockReturnValue(of(mockEmployeeResponse));

      const result = await controller.updateEmployee(
        1, // id
        1, // userId
        ['Admin'], // roles
        '127.0.0.1', // ipAddress
        'test-agent', // userAgent
        updateData
      );

      expect(result).toEqual(mockEmployeeResponse);
      expect(authService.updateEmployee).toHaveBeenCalledWith({
        id: 1,
        ...updateData,
        requestUserId: 1,
        roleName: 'Admin',
        ipAddress: '127.0.0.1',
        userAgent: 'test-agent',
      });
    });
  });

  describe('removeEmployee', () => {
    it('should remove an employee successfully', async () => {
      const mockRemoveResponse = {
        success: true,
        message: 'Employee removed successfully',
      };

      authService.removeEmployee.mockReturnValue(of(mockRemoveResponse));

      const result = await controller.removeEmployee(
        1, // id
        1, // userId
        ['Admin'], // roles
        '127.0.0.1', // ipAddress
        'test-agent' // userAgent
      );

      expect(result).toEqual(mockRemoveResponse);
      expect(authService.removeEmployee).toHaveBeenCalledWith({
        id: 1,
        requestUserId: 1,
        roleName: 'Admin',
        ipAddress: '127.0.0.1',
        userAgent: 'test-agent',
      });
    });
  });
});
