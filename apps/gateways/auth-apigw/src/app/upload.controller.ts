import {
  Controller,
  Post,
  UploadedFile,
  UseInterceptors,
  Query,
  Logger,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { UploadService, MulterFile } from '@apply-goal-backend/common'; // adjust import path as needed
import { error } from 'console';

@Controller('upload')
export class UploadController {
  constructor(private readonly uploadService: UploadService) {}

  @Post()
  @UseInterceptors(FileInterceptor('file'))
  async uploadFile(
    @UploadedFile() file: MulterFile,
    @Query('type') type: 'image' | 'file' = 'file'
  ) {
    try {
      const { url } = await this.uploadService.uploadFile(file, type);
      if (!url) {
        Logger.error('File upload failed: URL is null');
        throw new Error('File upload failed: URL is null');
      }
      return {
        url: url,
        message: 'File uploaded successfully',
        error: null,
      };
    } catch (error) {
      Logger.error('Upload error:', error);
      return {
        url: null,
        message: 'File upload failed',
        error: `Upload error: ${error.message}`,
      };
    }
  }
}
