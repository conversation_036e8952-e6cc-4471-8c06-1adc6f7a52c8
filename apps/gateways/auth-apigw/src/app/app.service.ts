import { Injectable } from '@nestjs/common';
import { MetricsService } from '@apply-goal-backend/monitoring';

@Injectable()
export class AppService {
  private requestCounter: any;
  private requestDuration: any;
  private authOperationsCounter: any;
  private authOperationsDuration: any;
  private errorCounter: any;

  constructor(private metricsService: MetricsService) {
    // Initialize metrics
    this.requestCounter = this.metricsService.createCounter(
      'http_requests_total',
      'Total number of HTTP requests processed',
      ['method', 'endpoint', 'status']
    );

    this.requestDuration = this.metricsService.createHistogram(
      'http_request_duration_seconds',
      'HTTP request duration in seconds',
      ['method', 'endpoint']
    );

    this.authOperationsCounter = this.metricsService.createCounter(
      'auth_operations_total',
      'Total number of authentication operations',
      ['operation', 'status']
    );

    this.authOperationsDuration = this.metricsService.createHistogram(
      'auth_operation_duration_seconds',
      'Authentication operation duration in seconds',
      ['operation']
    );

    this.errorCounter = this.metricsService.createCounter(
      'auth_errors_total',
      'Total number of authentication errors',
      ['operation', 'error_type']
    );
  }

  getData(): { message: string } {
    return { message: 'Welcome to auth-apigw!' };
  }

  // Track HTTP request metrics
  trackHttpRequest(method: string, endpoint: string, status: string, duration: number) {
    this.requestCounter.inc({ method, endpoint, status });
    this.requestDuration.observe({ method, endpoint }, duration);
  }

  // Track authentication operation metrics
  trackAuthOperation(operation: string, status: string, duration: number) {
    this.authOperationsCounter.inc({ operation, status });
    this.authOperationsDuration.observe({ operation }, duration);
  }

  // Track authentication errors
  trackAuthError(operation: string, errorType: string) {
    this.errorCounter.inc({ operation, error_type: errorType });
  }
}
