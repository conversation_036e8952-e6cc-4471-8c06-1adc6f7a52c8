# Build stage
FROM node:18-alpine as builder

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY nx.json ./
COPY tsconfig*.json ./
COPY eslint.config.mjs ./

# Copy source code
COPY apps/gateways/auth-apigw ./apps/gateways/auth-apigw
COPY libs ./libs

# Install dependencies
RUN npm ci

# Build the application
RUN npx nx build auth-apigw --prod

# Production stage
FROM node:18-alpine

WORKDIR /app

# Copy built assets from builder
COPY --from=builder /app/dist/apps/gateways/auth-apigw ./

# Install production dependencies
RUN npm ci --only=production

# Install curl for healthcheck
RUN apk --no-cache add curl

# Set environment variables
ENV NODE_ENV=production
ENV PORT=4006

# Expose the service port
EXPOSE 4006

# Add healthcheck
HEALTHCHECK --interval=30s --timeout=30s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:4006/health || exit 1

# Start the service
CMD ["node", "main.js"]