{"name": "student-apigw-e2e", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "implicitDependencies": ["student-apigw"], "targets": {"e2e": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{e2eProjectRoot}"], "options": {"jestConfig": "apps/gateways/student-apigw-e2e/jest.config.ts", "passWithNoTests": true}, "dependsOn": ["student-apigw:build"]}}}