import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { CountryController } from './country/country.controller';
import { UniversityClientService } from './university.service';

@Module({
  imports: [],
  controllers: [AppController,CountryController],
  providers: [AppService,UniversityClientService],
})
export class AppModule {}
