import { Injectable, OnModuleInit } from '@nestjs/common';
import { Client, ClientGrpc } from '@nestjs/microservices';
import { Transport } from '@nestjs/microservices';
import { join } from 'path';
import { Observable } from 'rxjs';
import {
  UniversityService,
  CreateCountryRequest,
  CountryResponse,
  GetCountryRequest,
  UpdateCountryRequest,
  DeleteCountryRequest,
  ListCountriesRequest,
  ListCountriesResponse,
  CreateCourseStudyFieldRequest,
  CourseStudyFieldResponse,
  GetCourseStudyFieldRequest,
  UpdateCourseStudyFieldRequest,
  DeleteCourseStudyFieldRequest,
  ListCourseStudyFieldsRequest,
  ListCourseStudyFieldsResponse,
  GetCourseStudyFieldsByCourseRequest,
  GetCourseStudyFieldsByStudyFieldRequest,
  DeleteCourseStudyFieldResponse
} from './university.interface';

@Injectable()
export class UniversityClientService implements OnModuleInit {
  @Client({
    transport: Transport.GRPC,
    options: {
      package: 'university',
      protoPath: join(process.cwd(), 'libs/shared/dto/src/lib/university/university.proto'),
      url: process.env.NODE_ENV === 'production' ? 'university-service:50059' : 'localhost:50059',
    }
  })
  private readonly client: ClientGrpc;

  private universityService: UniversityService;

  onModuleInit() {
    this.universityService = this.client.getService<UniversityService>('UniversityService');
  }

  // Country methods
  createCountry(data: CreateCountryRequest): Observable<CountryResponse> {
    console.log('Creating country: ', data);
    return this.universityService.createCountry(data);
  }

  getCountry(id: number): Observable<CountryResponse> {
    return this.universityService.getCountry({ id });
  }

  updateCountry(id: number, data: Partial<UpdateCountryRequest>): Observable<CountryResponse> {
    return this.universityService.updateCountry({ id, ...data });
  }

  deleteCountry(id: number): Observable<CountryResponse> {
    return this.universityService.deleteCountry({ id });
  }

  listCountries(page: number = 1, limit: number = 10, isActive?: boolean): Observable<ListCountriesResponse> {
    return this.universityService.listCountries({ page, limit, isActive });
  }

  // Course Study Field methods
  createCourseStudyField(data: CreateCourseStudyFieldRequest): Observable<CourseStudyFieldResponse> {
    return this.universityService.createCourseStudyField(data);
  }

  getCourseStudyField(id: number): Observable<CourseStudyFieldResponse> {
    return this.universityService.getCourseStudyField({ id });
  }

  updateCourseStudyField(id: number, isActive?: boolean): Observable<CourseStudyFieldResponse> {
    return this.universityService.updateCourseStudyField({ id, isActive });
  }

  deleteCourseStudyField(id: number): Observable<DeleteCourseStudyFieldResponse> {
    return this.universityService.deleteCourseStudyField({ id });
  }

  listCourseStudyFields(
    page: number = 1, 
    limit: number = 10, 
    courseId?: number, 
    studyFieldId?: number, 
    isActive?: boolean
  ): Observable<ListCourseStudyFieldsResponse> {
    return this.universityService.listCourseStudyFields({ 
      page, 
      limit, 
      courseId, 
      studyFieldId, 
      isActive 
    });
  }

  getCourseStudyFieldsByCourse(courseId: number): Observable<ListCourseStudyFieldsResponse> {
    return this.universityService.getCourseStudyFieldsByCourse({ courseId });
  }

  getCourseStudyFieldsByStudyField(studyFieldId: number): Observable<ListCourseStudyFieldsResponse> {
    return this.universityService.getCourseStudyFieldsByStudyField({ studyFieldId });
  }
}