import { Observable } from 'rxjs';

// Country interfaces
export interface CreateCountryRequest {
  countryName: string;
  continent: string;
  isActive: boolean;
}

export interface GetCountryRequest {
  id: number;
}

export interface UpdateCountryRequest {
  id: number;
  countryName?: string;
  continent?: string;
  isActive?: boolean;
}

export interface DeleteCountryRequest {
  id: number;
}

export interface CountryResponse {
  id: number;
  countryName: string;
  continent: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface ListCountriesRequest {
  page?: number;
  limit?: number;
  isActive?: boolean;
}

export interface ListCountriesResponse {
  total: number;
  page: number;
  limit: number;
  countries: CountryResponse[];
}

// Course Study Field interfaces
export interface CreateCourseStudyFieldRequest {
  courseId: number;
  studyFieldId: number;
  isActive?: boolean;
}

export interface GetCourseStudyFieldRequest {
  id: number;
}

export interface UpdateCourseStudyFieldRequest {
  id: number;
  isActive?: boolean;
}

export interface DeleteCourseStudyFieldRequest {
  id: number;
}

export interface CourseInfo {
  id: number;
  title: string;
}

export interface StudyFieldInfo {
  id: number;
  field: string;
}

export interface CourseStudyFieldResponse {
  id: number;
  courseId: number;
  studyFieldId: number;
  isActive: boolean;
  course?: CourseInfo;
  studyField?: StudyFieldInfo;
  createdAt: string;
  updatedAt: string;
}

export interface ListCourseStudyFieldsRequest {
  page?: number;
  limit?: number;
  courseId?: number;
  studyFieldId?: number;
  isActive?: boolean;
}

export interface ListCourseStudyFieldsResponse {
  total: number;
  page: number;
  limit: number;
  courseStudyFields: CourseStudyFieldResponse[];
}

export interface GetCourseStudyFieldsByCourseRequest {
  courseId: number;
}

export interface GetCourseStudyFieldsByStudyFieldRequest {
  studyFieldId: number;
}

export interface DeleteCourseStudyFieldResponse {
  success: boolean;
  id: number;
}

// University Service interface
export interface UniversityService {
  // Country methods
  createCountry(request: CreateCountryRequest): Observable<CountryResponse>;
  getCountry(request: GetCountryRequest): Observable<CountryResponse>;
  updateCountry(request: UpdateCountryRequest): Observable<CountryResponse>;
  deleteCountry(request: DeleteCountryRequest): Observable<CountryResponse>;
  listCountries(request: ListCountriesRequest): Observable<ListCountriesResponse>;
  
  // Course Study Field methods
  createCourseStudyField(request: CreateCourseStudyFieldRequest): Observable<CourseStudyFieldResponse>;
  getCourseStudyField(request: GetCourseStudyFieldRequest): Observable<CourseStudyFieldResponse>;
  updateCourseStudyField(request: UpdateCourseStudyFieldRequest): Observable<CourseStudyFieldResponse>;
  deleteCourseStudyField(request: DeleteCourseStudyFieldRequest): Observable<DeleteCourseStudyFieldResponse>;
  listCourseStudyFields(request: ListCourseStudyFieldsRequest): Observable<ListCourseStudyFieldsResponse>;
  getCourseStudyFieldsByCourse(request: GetCourseStudyFieldsByCourseRequest): Observable<ListCourseStudyFieldsResponse>;
  getCourseStudyFieldsByStudyField(request: GetCourseStudyFieldsByStudyFieldRequest): Observable<ListCourseStudyFieldsResponse>;
}
