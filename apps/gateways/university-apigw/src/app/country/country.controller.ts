import { Controller, Get, Post, Body, Param, Put, Delete, Query, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { UniversityClientService } from '../university.service';
import { firstValueFrom } from 'rxjs';
import { CreateCountryRequest, UpdateCountryRequest } from '../university.interface';

@Controller('countries')
export class CountryController {
  private readonly logger = new Logger(CountryController.name);

  constructor(private readonly universityService: UniversityClientService) {}

  @Post()
  async createCountry(@Body() createCountryDto: CreateCountryRequest) {
    try {
      this.logger.log(`Creating country: ${createCountryDto.countryName}`);
      return await firstValueFrom(this.universityService.createCountry(createCountryDto));
    } catch (error) {
      this.logger.error(`Failed to create country: ${error.message}`, error.stack);
      throw new HttpException(
        error.message || 'Failed to create country',
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Get()
  async listCountries(
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Query('isActive') isActive?: boolean
  ) {
    try {
      this.logger.log(`Listing countries - page: ${page}, limit: ${limit}`);
      return await firstValueFrom(this.universityService.listCountries(page, limit, isActive));
    } catch (error) {
      this.logger.error(`Failed to list countries: ${error.message}`, error.stack);
      throw new HttpException(
        error.message || 'Failed to list countries',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get(':id')
  async getCountry(@Param('id') id: number) {
    try {
      this.logger.log(`Getting country with ID: ${id}`);
      return await firstValueFrom(this.universityService.getCountry(id));
    } catch (error) {
      this.logger.error(`Failed to get country: ${error.message}`, error.stack);
      throw new HttpException(
        error.message || 'Failed to get country',
        HttpStatus.NOT_FOUND
      );
    }
  }

  @Put(':id')
  async updateCountry(
    @Param('id') id: number,
    @Body() updateCountryDto: Partial<UpdateCountryRequest>
  ) {
    try {
      this.logger.log(`Updating country with ID: ${id}`);
      return await firstValueFrom(this.universityService.updateCountry(id, updateCountryDto));
    } catch (error) {
      this.logger.error(`Failed to update country: ${error.message}`, error.stack);
      throw new HttpException(
        error.message || 'Failed to update country',
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Delete(':id')
  async deleteCountry(@Param('id') id: number) {
    try {
      this.logger.log(`Deleting country with ID: ${id}`);
      return await firstValueFrom(this.universityService.deleteCountry(id));
    } catch (error) {
      this.logger.error(`Failed to delete country: ${error.message}`, error.stack);
      throw new HttpException(
        error.message || 'Failed to delete country',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}
