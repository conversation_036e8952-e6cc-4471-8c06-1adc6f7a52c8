{"name": "university-apigw", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/gateways/university-apigw/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "nx:run-commands", "options": {"command": "webpack-cli build", "args": ["node-env=production"]}, "configurations": {"development": {"args": ["node-env=development"]}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "university-apigw:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "university-apigw:build:development"}, "production": {"buildTarget": "university-apigw:build:production"}}}, "test": {"options": {"passWithNoTests": true}}}}