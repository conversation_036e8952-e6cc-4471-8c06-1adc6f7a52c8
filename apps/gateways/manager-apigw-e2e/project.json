{"name": "manager-apigw-e2e", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "implicitDependencies": ["manager-api<PERSON>w"], "targets": {"e2e": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{e2eProjectRoot}"], "options": {"jestConfig": "apps/gateways/manager-apigw-e2e/jest.config.ts", "passWithNoTests": true}, "dependsOn": ["manager-apigw:build"]}}}