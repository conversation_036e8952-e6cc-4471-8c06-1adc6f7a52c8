{"name": "manager-api<PERSON>w", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/gateways/manager-apigw/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "nx:run-commands", "options": {"command": "webpack-cli build", "args": ["node-env=production"]}, "configurations": {"development": {"args": ["node-env=development"]}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "manager-apigw:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "manager-apigw:build:development"}, "production": {"buildTarget": "manager-apigw:build:production"}}}, "test": {"options": {"passWithNoTests": true}}}}