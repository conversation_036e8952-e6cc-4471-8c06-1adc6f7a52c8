{"name": "auth-apigw-e2e", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "implicitDependencies": ["auth-apigw"], "targets": {"e2e": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{e2eProjectRoot}"], "options": {"jestConfig": "apps/gateways/auth-apigw-e2e/jest.config.ts", "passWithNoTests": true}, "dependsOn": ["auth-apigw:build"]}}}