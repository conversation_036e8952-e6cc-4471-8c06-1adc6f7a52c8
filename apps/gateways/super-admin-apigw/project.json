{"name": "super-admin-apigw", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/gateways/super-admin-apigw/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "nx:run-commands", "options": {"command": "webpack-cli build", "args": ["node-env=production"]}, "configurations": {"development": {"args": ["node-env=development"]}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "super-admin-apigw:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "super-admin-apigw:build:development"}, "production": {"buildTarget": "super-admin-apigw:build:production"}}}, "test": {"options": {"passWithNoTests": true}}}}