# See https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files for more about ignoring files.

# Dependencies
node_modules
.pnpm-store/

# Nx
.nx/
dist/
coverage/
tmp/

# IDEs and editors
.idea/
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace
.vscode/*

# Misc
.DS_Store
*.pem
.env
.env.*
!.env.example

# Debug
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*

# Vercel
.vercel

# Local files
*.local

